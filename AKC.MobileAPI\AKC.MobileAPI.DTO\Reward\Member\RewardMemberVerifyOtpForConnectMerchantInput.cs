﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberVerifyOtpForConnectMerchantInput
    {
        public string SessionId { get; set; }
        public string OtpCode { get; set; }
        public string LinkID_PhoneNumber { get; set; }
        
        public int? ConnectSource { get; set; }
        //public string IdCard { get; set; }
        //public string VPBank_PhoneNumber { get; set; }
        //public RewardMemberVerifyOtpLoyaltyInfo LoyaltyInfo { get; set; }
    }

    public class RewardMemberVerifyOtpForConnectMerchantInputDto
    {
        public string SessionId { get; set; }
        public int MerchantId { get; set; }
        public string OtpCode { get; set; }
        public string PhoneNumber { get; set; }
        public string IdCard { get; set; }
        public string Cif { get; set; }
        public string PartnerPhoneNumber { get; set; }
        public string ConnectSource { get; set; }
        public RewardMemberVerifyOtpLoyaltyInfoDto LoyaltyInfo { get; set; }
    }

    public class RewardMemberVerifyOtpLoyaltyInfo
    {
        public string Cif { get; set; }
        public string MemberLoyaltyCode { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string Email { get; set; }
        public string Gender { get; set; }
        public DateTime? Dob { get; set; }
        public string IdCard { get; set; }
        public string Phone { get; set; }
        public string Segment { get; set; }
        public string VipType { get; set; }
    }

    public class RewardMemberVerifyOtpLoyaltyInfoDto
    {
        public string Cif { get; set; }
        public string MemberLoyaltyCode { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string Email { get; set; }
        public string Gender { get; set; }
        public DateTime? Dob { get; set; }
        public string Type { get; set; }
        public string RegionCode { get; set; }
        public string FullRegionCode { get; set; }
        public string MemberTypeCode { get; set; }
        public string FullMemberTypeCode { get; set; }
        public string ChannelType { get; set; }
        public string FullChannelTypeCode { get; set; }
        public string RankTypeCode { get; set; }
        public string StandardMemberCode { get; set; }
        public string IdCard { get; set; }
        public string PartnerPhoneNumber { get; set; }
        public string Phone { get; set; }
        public string Avatar { get; set; }
        public string Segment { get; set; }
        public string VipType { get; set; }
    }
}
