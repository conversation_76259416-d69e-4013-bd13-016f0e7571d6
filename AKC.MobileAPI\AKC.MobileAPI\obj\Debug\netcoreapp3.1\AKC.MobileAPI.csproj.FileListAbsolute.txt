D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\appsettings.Development.json
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\appsettings.json
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\App_Data\adminSDKKey.json
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\AKC.MobileAPI.exe
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\log4net-audit.config
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\log4net.config
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\AKC.MobileAPI.deps.json
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\AKC.MobileAPI.runtimeconfig.json
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\AKC.MobileAPI.runtimeconfig.dev.json
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\AKC.MobileAPI.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\AKC.MobileAPI.pdb
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\AKC.MobileAPI.xml
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\AspNetCore.TotpGenerator.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\AWSSDK.Core.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\AWSSDK.S3.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Azure.Core.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Azure.Storage.Blobs.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Azure.Storage.Common.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Castle.Core.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Crc32.NET.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Cronos.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\FirebaseAdmin.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Gelf.Extensions.Logging.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Google.Api.Gax.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Google.Api.Gax.Rest.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Google.Apis.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Google.Apis.Auth.PlatformServices.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Google.Apis.Auth.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Google.Apis.Core.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\log4net.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\System.Net.Http.Formatting.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\System.Web.Http.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Authentication.JwtBearer.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Bcl.AsyncInterfaces.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Data.SqlClient.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Design.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Relational.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.SqlServer.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Caching.Abstractions.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Abstractions.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Binder.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.EnvironmentVariables.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.FileExtensions.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Json.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.FileProviders.Physical.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.FileSystemGlobbing.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Hosting.Abstractions.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.Abstractions.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.Log4Net.AspNetCore.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Options.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Primitives.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Identity.Client.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.JsonWebTokens.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Logging.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Tokens.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.IO.RecyclableMemoryStream.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.OpenApi.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Minio.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Newtonsoft.Json.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Newtonsoft.Json.Bson.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\BouncyCastle.Crypto.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\QRCoder.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Quartz.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\RabbitMQ.Client.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\RestSharp.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.Swagger.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerGen.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerUI.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\System.Configuration.ConfigurationManager.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\System.Data.SqlClient.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\System.IdentityModel.Tokens.Jwt.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\System.Interactive.Async.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\System.Reactive.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\System.Reactive.Linq.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\System.Runtime.Caching.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\System.Security.Cryptography.ProtectedData.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.WindowsAzure.Storage.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\runtimes\unix\lib\netcoreapp2.1\Microsoft.Data.SqlClient.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\runtimes\win\lib\netcoreapp2.1\Microsoft.Data.SqlClient.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\runtimes\win-arm64\native\sni.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\runtimes\win-x64\native\sni.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\runtimes\win-x86\native\sni.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\runtimes\unix\lib\netcoreapp2.1\System.Data.SqlClient.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\runtimes\win\lib\netcoreapp2.1\System.Data.SqlClient.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\runtimes\unix\lib\netcoreapp2.0\System.Runtime.Caching.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\runtimes\win\lib\netcoreapp2.0\System.Runtime.Caching.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\runtimes\win\lib\netstandard2.0\System.Security.Cryptography.ProtectedData.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\AKC.MobileAPI.DTO.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\AKC.MobileAPI.Service.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\AKC.RabbitMQ.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\AKC.MobileAPI.DTO.pdb
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\AKC.MobileAPI.Service.pdb
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\AKC.RabbitMQ.pdb
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\AKC.MobileAPI.csproj.AssemblyReference.cache
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\AKC.MobileAPI.GeneratedMSBuildEditorConfig.editorconfig
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\AKC.MobileAPI.AssemblyInfoInputs.cache
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\AKC.MobileAPI.AssemblyInfo.cs
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\AKC.MobileAPI.csproj.CoreCompileInputs.cache
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\AKC.MobileAPI.MvcApplicationPartsAssemblyInfo.cs
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\AKC.MobileAPI.MvcApplicationPartsAssemblyInfo.cache
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\AKC.MobileAPI.RazorTargetAssemblyInfo.cache
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\staticwebassets\AKC.MobileAPI.StaticWebAssets.Manifest.cache
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\staticwebassets\AKC.MobileAPI.StaticWebAssets.Pack.cache
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.AKC.MobileAPI.Microsoft.AspNetCore.StaticWebAssets.props
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.build.AKC.MobileAPI.props
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.buildMultiTargeting.AKC.MobileAPI.props
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.buildTransitive.AKC.MobileAPI.props
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\AKC.Mobi.D62F3DE1.Up2Date
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\AKC.MobileAPI.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\AKC.MobileAPI.xml
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\AKC.MobileAPI.pdb
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\obj\Debug\netcoreapp3.1\AKC.MobileAPI.genruntimeconfig.cache
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Caching.StackExchangeRedis.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\Pipelines.Sockets.Unofficial.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\StackExchange.Redis.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\System.ComponentModel.Annotations.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\System.IO.Pipelines.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI\bin\Debug\netcoreapp3.1\System.Runtime.CompilerServices.Unsafe.dll
