﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Order;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/Order")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    //[Authorize]
    public class LoyaltyOrderController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyOrderService _orderService;
        private readonly IExceptionReponseService _exceptionReponseService;
        public LoyaltyOrderController(
            ILogger<LoyaltyOrderController> logger,
            ILoyaltyOrderService orderService,
            IExceptionReponseService exceptionReponseService)
        {
            _logger = logger;
            _orderService = orderService;
            _exceptionReponseService = exceptionReponseService;
        }

        //[HttpPost]
        //[Route("PurchaseAgent")]
        //public async Task<ActionResult<LoyaltyPurchaseAgentOutput>> PurchaseAgent(LoyaltyPurchaseAgentInput input)
        //{
        //    try
        //    {
        //        var result = await _orderService.PurchaseAgent(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //        _logger.LogError(ex, "PurchaseAgent Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        //[HttpPost]
        //[Route("UsePoint")]
        //public async Task<ActionResult<LoyaltyResponse<string>>> UsePoint(LoyaltyUsePointInput input)
        //{
        //    try
        //    {
        //        var result = await _orderService.UsePoint(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //        _logger.LogError(ex, "UsePoint Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}
    }
}
