﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class GetAllEffectiveCategoryOutput
    {
        public ListResultGetAllEffectiveCategoryOutput Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }

        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }
    public class ListResultGetAllEffectiveCategoryOutput
    {
        public int TotalCount { get; set; }

        public List<GetGiftInforByGiftCategoryForView> Items { get; set; }
    }
    public class GetGiftInforByGiftCategoryForView
    {
        public GiftShortInforDto GiftInfor { get; set; }

        public List<ImageLinkDto> ImageLink { get; set; }

        public List<GiftGroupDto> GiftGroup { get; set; }
        public List<GiftShortInforForView> RelatedGiftInfor { get; set; }
    }
}
