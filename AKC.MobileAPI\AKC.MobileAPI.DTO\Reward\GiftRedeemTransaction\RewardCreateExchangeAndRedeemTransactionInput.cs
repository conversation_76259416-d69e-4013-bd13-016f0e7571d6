﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction
{
    public class RewardCreateExchangeAndRedeemTransactionInput
    {
        [Required]
        public string MemberCode { get; set; }
        public List<RewardCreateExchangeTransactionItems> ExchangeList { get; set; }
        public RewardCreateRedeemTransactionItems Redeem { get; set; }
    }

    public class RewardCreateExchangeTransactionItems
    {
        public string AccessToken { get; set; }
        public int MerchantId { get; set; }
        public string IdNumber { get; set; }
        public long ExchangeAmount { get; set; }
    }

    public class RewardCreateRedeemTransactionItems
    {
        public string GiftCode { get; set; }
        public int Quantity { get; set; }
        public decimal TotalAmount { get; set; }
        public string Date { get; set; }
        public string Description { get; set; }
    }
}
