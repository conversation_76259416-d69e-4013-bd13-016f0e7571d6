﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.DTO.MobileAPI;
using AKC.MobileAPI.DTO.Reward.Member;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Sme;
using AKC.MobileAPI.Service.Reward;

namespace AKC.MobileAPI.Service.Abstract.Reward
{
    public interface IRewardMemberService: IRewardBaseService
    {
        Task<RewardMemberViewOutput> View(ViewMemberInput request);
        Task<RewardMemberUpdateOutput> Update(RewardMemberUpdateInput input);
        Task<LoyaltyResponse<LoyaltyRewardMemberViewPoint>> ViewPoint(RewardMemberRequestInput request, string authorization);
        Task<ViewSmePointResponse> ViewSmePoint(ViewSmePointRequest request);
        Task<ViewBalanceWithExpiringCoinOutput> ViewBalanceWithExpiringCoins(ViewBalanceWithExpiringCoinInput request, string authorization);
        Task<RewardMemberVerifyOrCreateOutput> Create(RewardMemberCreateInput input, string authorization);
        Task<RewardRegisterWithEmailAndPasswordOutput> RegisterWithEmailAndPassword(RewardRegisterWithEmailAndPasswordInput input);
        Task<RewardMemberVerifyReferralCodeOutput> VerifyReferralCode(RewardMemberVerifyReferralCodeInput input);
        Task<RewardMemberTempPointTransGetByIdOutput> GetTempPointTransByMemberId(RewardMemberTempPointTransGetByIdInput input);
        Task<LoyaltyResponse<GatewayMemberTokenTransGetByIdOutput>> GetTokenTransByMemberId(RewardMemberTokenTransGetByIdInput input);
        Task<RewardAddPointUsingOrdinaryOutput> AddPointUsingOrdinary(RewardAddPointUsingOrdinaryInput input);
        Task<RewardGetMemberBalanceByNationalIdOutput> GetBalanceMember(string memberCode);
        Task<RewardMemberVerifyIsIdCardVerifiedOutput> VerifyIsIdCardVerified(RewardMemberVerifyIsIdCardVerifiedInput input);
        Task<RewardMemberHasPinCodeResponse> HasPinCode(RewardMemberHasPinCodeRequest input);
        Task<RewardMemberCreateOrUpdatePinCodeResponse> CreateOrUpdatePinCode(MobileAPICreateOrUpdatePinCodeInput input);
        Task<RewardMemberVerifyPinCodeResponse> VerifyPinCode(RewardMemberVerifyPinCodeRequest input);
		Task<VerifyProviderIdByPhoneNumberResponse> VerifyProviderIdByPhoneNumber(VerifyProviderIdByPhoneNumberRequest input);
        Task<UpdateProviderOutPut> UpdateProvider(VerifyProviderIdByPhoneNumberRequest input);
        Task<RewardMemberVerifyOrCreateOutput> VerifyOrCreate(RewardMemberVerifyOrCreateInput input, string authorization);
        Task<LoyaltyResponse<string>> UpdateNotificationSetting(UpdateNotificationSettingInput input);
        Task<RewardMemberGetRefreshTokenOutput> GetRefreshToken(RewardMemberGetRefreshTokenInput input);
        Task<RewardMemberSaveRefreshTokenOutput> SaveRefreshToken(RewardMemberSaveRefreshTokenInput input);
        Task<RewardMemberCreateRegisterLogOutput> CreateRegisterLog(RewardMemberCreateRegisterLogInput input);
        Task<UpdatePhoneNumberOutput> UpdatePhoneNumber(MobileUpdatePhoneNumberInput input);
        Task<RewardMemberAccountHavePhoneNumberOutput> AccountHavePhoneNumber(RewardMemberAccountHavePhoneNumberInput input);
        Task<RewardMemberRevokeTokenResponse> RevokeToken(string authorization);
        Task<RewardMemberGetInfoOutput> GetInfo(RewardMemberGetInfoInput input);
        Task<RewardMemberFirstActionMemberOutput> FirstActionMember(RewardMemberFirstActionMemberInput input);
        Task<RewardMemberGetMemberLoginByFirebaseIdOutput> GetMemberLoginByFirebaseId(string authorization);
        Task<RewardMemberGetUsagePriorityOutput> GetUsagePriority(RewardMemberGetUsagePriorityInput input);
        Task<RewardMemberUpdateUsagePriorityOutput> UpdateUsagePriority(RewardMemberUpdateUsagePriorityInput input);
        Task<RewardMemberUpdatePointUsageTypeOutput> UpdatePointUsageType(RewardMemberUpdatePointUsageTypeInput input);
        Task<RewardMemberGetCashoutAndTopupInfoOutput> GetCashoutAndTopupInfo(RewardMemberGetCashoutAndTopupInfoInput input);
        Task<RewardMemberSendOtpForConnectMerchantOutput> SendOtpForConnectMerchant(RewardMemberSendOtpForConnectMerchantInput input);
        Task<RewardMemberSendOtpForConnectMerchantOutput> AutoConnectMember(RewardMemberAutoConnectMemberInput input);
        Task<RewardMemberSendOtpForConnectMerchantOutput> AutoConnectMember2(RewardMemberAutoConnectMemberInput input);

        Task<RewardMemberAutoConnectMember3Output> AutoConnectMember3MasterCard(
            RewardMemberAutoConnectMember3Input input);
        Task<CreateUnconfirmedConnectionOutput> CreateUnconfirmedConnection(CreateUnconfirmedConnectionInputSendOperator input);
        Task<GetListConnectedMerchantSimplifiedByMemberIdOutput> GetListConnectedMerchantSimplifiedByMemberId(GetListConnectedMerchantSimplifiedByMemberIdInput input);
        Task<RewardMemberVerifyOtpForConnectMerchantOutput> VerifyOtpForConnectMerchant(RewardMemberVerifyOtpForConnectMerchantInput input);
        Task<RewardMemberConfirmConnectOutput> ConfirmConnect(RewardMemberConfirmConnectInput input);
        Task<RewardGetMemberInfoResponse> GetMemberInfo(int MemberId);
        Task<RewardMemberRedeemOutput> RedeemTransaction(RewardMemberRedeemInput input);
        Task<RewardMemberRevertRedeemOutput> RevertRedeemTransaction(RewardMemberRevertRedeemInput input, string url);
        Task<RewardMemberSendOtpConfirmOutput> SendOtpConfirm(RewardMemberSendOtpConfirmInput input);
        Task<RewardMemberVerifyOtpConfirmOutput> VerifyOtpConfirm(RewardMemberVerifyOtpConfirmInput input);
        Task<RewardMemberVerifyCreateRedeemOutput> VerifyCreateRedem(RewardMemberVerifyCreateRedeemInput input);
        Task<RewardMemberVerifyOtpExpiredOutput> VerifyOtpExpired(RewardMemberVerifyOtpExpiredInput input);

        Task<LoyaltyResponse<LoyaltyRewardMemberViewCreditBalance>> ViewCreditBalance(
            RewardViewCreditBalanceInput input);
        
        Task<LoyaltyResponse<string>> RemoveConnection(
            RewardRemoveConnectionInput input);
        Task<RewardMemberGetCifByPhoneNumberOutput> GetCifByPhoneNumber(RewardMemberGetCifByPhoneNumberInput input);
        Task<RewardMemberVerifyOtpMerchantViewOutput> sendOtpForConnectMerchantView(RewardMemberSendOtpForConnectMerchantViewInput input);
        Task<RewardMemberVerifyOtpMerchantViewOutput> verifyOtpForConnectMerchantView(RewardMemberVerifyOtpMerchantViewInput input);
        Task<RewardMemberCreateOutput> CreateLinkIdMember(CreateLinkIdMemberInput input);
        Task<RewardCreateSmeConnectionOutput> RewardCreateSmeConnection(RewardCreateSmeConnectionInput input);
        Task<RewardAckSmeConnectionOutput> RewardAckSmeConnection(RewardAckSmeConnectionInput input);
        Task<GetListMemberCodeByListPhoneNumberOutput> GetListMemberCodeByListPhoneNumber(GetListMemberCodeByListPhoneNumberInput input);
        Task<RewardSendAckAfterConnectedOutput> RewardSendAckAfterConnected(RewardSendAckAfterConnectedInput input);

        Task<LoyaltyResponse<LoyaltyRewardMemberViewPointV2>> ViewBalanceWithExpiringCoinsByMemberCode(RewardMemberRequestInput request, string authorization);
    }
}
