﻿using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.Reward.ExchangeTransaction;
using AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Loyalty;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardCreateExchangeAndRedeemService : RewardBaseService, IRewardCreateExchangeAndRedeemService
    {
        private IRewardMemberService _rewardMemberService;
        private IRewardGiftRedeemTransactionService _rewardGiftRedeemTransactionService;
        private ILoyaltyGiftTransactionsService _loyaltyGiftTransactionsService;
        private readonly ILoyaltyThirdPartyService _loyaltyThirdPartyService;
        private readonly IRewardExchangeTransactionService _rewardExchangeTransactionService;
        private readonly ILogger _logger;
        private readonly IExceptionReponseService _exceptionReponseService;
        public RewardCreateExchangeAndRedeemService(
            IConfiguration configuration,
            IRewardMemberService rewardMemberService,
            IRewardGiftRedeemTransactionService rewardGiftRedeemTransactionService,
            ILoyaltyGiftTransactionsService loyaltyGiftTransactionsService,
            ILogger<RewardCreateExchangeAndRedeemService> logger,
            ILoyaltyThirdPartyService loyaltyThirdPartyService,
            IExceptionReponseService exceptionReponseService,
            IRewardExchangeTransactionService rewardExchangeTransactionService
        ) : base(configuration)
        {
            _rewardMemberService = rewardMemberService;
            _rewardGiftRedeemTransactionService = rewardGiftRedeemTransactionService;
            _loyaltyGiftTransactionsService = loyaltyGiftTransactionsService;
            _logger = logger;
            _loyaltyThirdPartyService = loyaltyThirdPartyService;
            _exceptionReponseService = exceptionReponseService;
            _rewardExchangeTransactionService = rewardExchangeTransactionService;
        }
        public async Task<RewardCreateExchangeAndRedeemTransactionOutput> CreateExchangeAndRedeemTransaction(RewardCreateExchangeAndRedeemTransactionInput input, HttpContext httpContext)
        {
            if (!CheckValidInput(input))
            {
                return new RewardCreateExchangeAndRedeemTransactionOutput()
                {
                    ErrorCode = "InvalidInput",
                    IsNotEnoughBalance = false,
                    SuccessedRedeem = false,
                    InvalidInput = true,
                    Messages = "Redeem fail with invalid input",
                    Items = null,
                    Timeout = false,
                };
            }
            var orderCode = genOrderCode();
            var optionsLoop = new ParallelOptions
            {
                MaxDegreeOfParallelism = 10
            };
            var listExchangePartnerResult = new List<ExchangePartnerResult>();
            var listExchangeInternalResult = new List<ExchangePartnerResult>();
            var taskListExchangePartner = new List<Task>();
            // Run all exchange from partner
            Task taskRunAllExchange = Task.Run(() =>
            {
                Parallel.ForEach(input.ExchangeList, optionsLoop, item =>
                {
                    var exchange = ExchangePartnerTransaction(input.MemberCode, item, orderCode, httpContext).Result;
                    listExchangePartnerResult.Add(exchange);
                });
            });
            taskListExchangePartner.Add(taskRunAllExchange);
            Task.WaitAll(taskListExchangePartner.ToArray());
            // If all partner exchange success then call exchange internal
            var listExchangePartnerError = listExchangePartnerResult.Where(x => x.Error).ToList();
            var listExchangeSucess = listExchangePartnerResult.Where(x => !x.Error).ToList();

            if (listExchangePartnerError.Count() == 0) {
                listExchangePartnerResult.ForEach((item) =>
                {
                    var exchangeInternal = ExchangeInternalTransaction(input.MemberCode, item.AccessToken, item.IdNumber, item.ExchangeAmount, item.MerchantId, item.VpidTransactionId, item.LoyaltlyTransactionId).Result;
                    listExchangeInternalResult.Add(exchangeInternal);
                });
            }

            var listExchangeInternalError = listExchangeInternalResult.Where(x => x.Error).ToList();
            // Has exchange internal and exchange internal not error
            if (listExchangeInternalResult.Count > 0 && listExchangeInternalError.Count() == 0)
            {
                var redeemTransaction = await RetryRedeemLoyaltyTransaction(input, orderCode, httpContext);
                _logger.LogInformation("create exchange redeem loyalty reward" + input.MemberCode + "_RedeemLoyaltyTransaction" + JsonConvert.SerializeObject(redeemTransaction));
                if (redeemTransaction.Error)
                {
                    // When redeem error then revert all exchange success
                    SendRetryRevertPartnerTransaction(listExchangePartnerResult, httpContext);
                    SendRetryRevertExchangeInternalTransaction(listExchangePartnerResult, input.MemberCode);
                    return new RewardCreateExchangeAndRedeemTransactionOutput()
                    {
                        ErrorCode = redeemTransaction.ErrorCode,
                        IsNotEnoughBalance = redeemTransaction.ErrorCode == "BalanceNotEnough" ? true : false,
                        SuccessedRedeem = false,
                        Messages = redeemTransaction.Exception,
                        Timeout = redeemTransaction.Timeout,
                        InvalidInput = false,
                    };
                }
                return new RewardCreateExchangeAndRedeemTransactionOutput()
                {
                    ErrorCode = null,
                    IsNotEnoughBalance = false,
                    SuccessedRedeem = true,
                    Messages = "Redeem successfuly",
                    Items = redeemTransaction.Items,
                    Timeout = false,
                    InvalidInput = false,
                };
            }

            // Show all error when exchange fail
            var listErrorExchanges = listExchangePartnerError.Select(x => new DataErrorExhangeTransaction()
            {
                ErrorCode = x.ErrorCode,
                Messages = x.Exception,
                MerchantId = x.MerchantId
            }).ToList();

            // When exchange has merchant error then revert all merchant exchange success
            SendRetryRevertPartnerTransaction(listExchangeSucess, httpContext);
            return new RewardCreateExchangeAndRedeemTransactionOutput()
            {
                ErrorCode = "CreateExchangeAndRedeemFail",
                IsNotEnoughBalance = false,
                SuccessedRedeem = false,
                Messages = "Create exchange and redeem fail with any exchange fail",
                Items = null,
                Timeout = false,
                ErrorExchanges = listErrorExchanges,
                InvalidInput = false,
            };
        }

        private void SendRetryRevertExchangeInternalTransaction(
            List<ExchangePartnerResult> input, string memberCode
        )
        {
            input.ForEach(item =>
            {
                RetryRevertTokenExchange(new RewardRevertExchangeTransactionInput()
                {
                    MemberCode = memberCode,
                    MerchantId = item.MerchantId,
                    PartnerBindingTxId = item.LoyaltlyTransactionId,
                    RevertAmount = item.ExchangeAmount,
                    TransactionCode = item.VpidTransactionId,
                }).Wait();
            });
        }

        private void SendRetryRevertPartnerTransaction(
            List<ExchangePartnerResult> input, HttpContext httpContext
        )
        {
            var optionsLoop = new ParallelOptions
            {
                MaxDegreeOfParallelism = 10
            };
            var taskListRevertExchange = new List<Task>();
            Task taskRunAllExchangeRedeemError = Task.Run(() =>
            {
                Parallel.ForEach(input, optionsLoop, item =>
                {
                    var revert = new LoyaltyThirdPartyRevertPointInput()
                    {
                        AccessToken = item.AccessToken,
                        LoyaltlyTransactionId = item.LoyaltlyTransactionId,
                        MemberCode = item.IdNumber,
                        MerchantId = item.MerchantId,
                        VpidTransactionId = item.VpidTransactionId
                    };
                    RetryRevertPartnerTransaction(revert, httpContext).Wait();
                });
            });
            taskListRevertExchange.Add(taskRunAllExchangeRedeemError);
            Task.WaitAll(taskListRevertExchange.ToArray());
        }

        private async Task RetryRevertPartnerTransaction(
            LoyaltyThirdPartyRevertPointInput input, HttpContext httpContext
        )
        {
            var retryNum = 5;
            while (retryNum != 0)
            {
                var result = await RevertPartnerTransaction(input, httpContext);
                if (result == true)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        private async Task<bool> RevertPartnerTransaction(
            LoyaltyThirdPartyRevertPointInput input, HttpContext httpContext
        )
        {
            try
            {
                await _loyaltyThirdPartyService.RevertPoint(new LoyaltyThirdPartyRevertPointInput()
                {
                    AccessToken = input.AccessToken,
                    MemberCode = input.MemberCode,
                    LoyaltlyTransactionId = input.LoyaltlyTransactionId,
                    MerchantId = input.MerchantId,
                    VpidTransactionId = input.VpidTransactionId,
                }, httpContext);
                return true;
            } catch
            {
                return false;
            }
        }

        private async Task<ExchangePartnerResult> ExchangeInternalTransaction(
            string memberCode, string accessToken, string idNumber, double exchangeAmount, int merchantId, string orderCode, string loyaltlyTransactionId
        )
        {
            var requestReward = new RewardCreateExchangeTransactionInput()
            {
                TransactionCode = orderCode,
                ExchangeAmount = exchangeAmount,
                MemberCode = memberCode,
                MerchantId = merchantId,
                PartnerBindingTxId = loyaltlyTransactionId,
            };

            try
            {
                var rewardExchange = await _rewardExchangeTransactionService.CreateExchangeTransactionIntegration(requestReward);

                return new ExchangePartnerResult()
                {
                    Error = false,
                    ErrorCode = null,
                    Exception = null,
                    Timeout = false,
                    LoyaltlyTransactionId = loyaltlyTransactionId,
                    MerchantId = merchantId,
                    AccessToken = accessToken,
                    IdNumber = idNumber,
                    VpidTransactionId = orderCode,
                };
            }
            catch (Exception ex)
            {
                _logger.LogInformation("create exchange redeem " + memberCode + "_Error_Internal" + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return new ExchangePartnerResult()
                    {
                        Error = true,
                        ErrorCode = res.Code,
                        Exception = res.Message,
                        Timeout = false,
                        LoyaltlyTransactionId = loyaltlyTransactionId,
                        MerchantId = merchantId,
                        AccessToken = accessToken,
                        IdNumber = idNumber,
                        VpidTransactionId = orderCode,
                    };
                }
                else if (ex.Message == "YouAreNotInTheTestList")
                {
                    return new ExchangePartnerResult()
                    {
                        Error = true,
                        ErrorCode = "YouAreNotInTheTestList",
                        Exception = "Bạn không nằm trong danh sách thử nghiệm, vui lòng liên hệ số hotline để biết thêm chi tiết",
                        Timeout = false,
                        LoyaltlyTransactionId = loyaltlyTransactionId,
                        MerchantId = merchantId,
                        AccessToken = accessToken,
                        IdNumber = idNumber,
                        VpidTransactionId = orderCode,
                    };
                } else
                {
                    var errorCode = "SystemError";
                    var exception = "Exception";
                    if (ex.GetType() == typeof(LoyaltyException))
                    {
                        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                        errorCode = res.Code;
                        exception = res.Message;
                    }
                    return new ExchangePartnerResult()
                    {
                        Error = true,
                        ErrorCode = errorCode,
                        Exception = exception,
                        Timeout = false,
                        LoyaltlyTransactionId = loyaltlyTransactionId,
                        MerchantId = merchantId,
                        AccessToken = accessToken,
                        IdNumber = idNumber,
                        VpidTransactionId = orderCode,
                    };
                }
            }
        }

        private async Task<ExchangePartnerResult> ExchangePartnerTransaction(
            string memberCode, RewardCreateExchangeTransactionItems input, string orderCode, HttpContext httpContext
        )
        {
            try
            {
                var requestExchange = new LoyaltyThirdPartyPointExchangeInput()
                {
                    AccessToken = input.AccessToken,
                    ExchangeAmount = input.ExchangeAmount,
                    IdNumber = input.IdNumber,
                    MemberCode = memberCode,
                    MerchantId = input.MerchantId,
                    NationalId = memberCode,
                };
                var rewardResult = await _loyaltyThirdPartyService.PointExchangeIntegration(requestExchange, httpContext, orderCode);
                return new ExchangePartnerResult()
                {
                    Error = false,
                    ErrorCode = null,
                    Exception = null,
                    Timeout = false,
                    LoyaltlyTransactionId = rewardResult.Items.Transaction.PartnerBindingTxId,
                    MerchantId = input.MerchantId,
                    AccessToken = input.AccessToken,
                    IdNumber = input.IdNumber,
                    VpidTransactionId = orderCode,
                    ExchangeAmount = input.ExchangeAmount
                };
            }
            catch (Exception ex)
            {
                _logger.LogInformation("create exchange redeem " + memberCode + "_Error" + JsonConvert.SerializeObject(ex));
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return new ExchangePartnerResult()
                {
                    Error = true,
                    ErrorCode = res.Code,
                    Exception = res.Message,
                    Timeout = false,
                    MerchantId = input.MerchantId,
                };
            }
        }

        private async Task<RedeemLoyaltyResult> RetryRedeemLoyaltyTransaction(
            RewardCreateExchangeAndRedeemTransactionInput input, string orderCode, HttpContext httpContext
        )
        {
            var errorCode = "SystemError";
            var exception = "SystemError";
            var retryNum = 5;
            var error = true;
            var errorLoyalty = false;
            var items = new List<DataRedeemTransaction>();
            while (retryNum != 0 && !errorLoyalty)
            {
                Thread.Sleep(1000);
                var redeemTransaction = await RedeemLoyaltyTransaction(input, orderCode, httpContext);
                errorLoyalty = redeemTransaction.ErrorLoyalty;
                if (redeemTransaction.Error)
                {
                    retryNum--;
                    errorCode = redeemTransaction.ErrorCode;
                    exception = redeemTransaction.Exception;
                    _logger.LogInformation("create exchange redeem " + input.MemberCode + "_retry" + JsonConvert.SerializeObject(redeemTransaction));
                }
                else
                {
                    error = false;
                    retryNum = 0;
                    items = redeemTransaction.Items;
                }
            }
            return new RedeemLoyaltyResult()
            {
                Error = error,
                ErrorCode = errorCode,
                Exception = exception,
                Items = items.Count > 0 ? items : null,
                Timeout = false,
            };
        }


        private async Task<RedeemLoyaltyResult> RedeemLoyaltyTransaction(
            RewardCreateExchangeAndRedeemTransactionInput input, string orderCode, HttpContext httpContext
        )
        {
            var merchantId = Convert.ToInt32(_configuration.GetSection("Reward" + MerchantNameConfig.VPID + ":MerchantId").Value);
            var cts = new CancellationTokenSource();
            var requestRevert = new RewardRevertGiftRedeemTransactionRequest()
            {
                MerchantId = merchantId,
                NationalId = input.MemberCode,
                OrderCode = orderCode,
                TokenAmount = input.Redeem.TotalAmount,
            };
            try
            {
                var resultBalance = await _rewardMemberService.GetBalanceMember(input.MemberCode);
                var sumExchangeAmount = input.ExchangeList.Select(x => x.ExchangeAmount).Sum(x => x);
                var detail = new DetailExchangeAndRedeem()
                {
                    CurrentBalance = resultBalance.TokenBalance,
                    ExchangeBalance = sumExchangeAmount,
                    RedeemBalance = input.Redeem.TotalAmount,
                };
                _logger.LogInformation("create exchange redeem " + input.MemberCode + "_balance" + JsonConvert.SerializeObject(detail));
                // If avalible token + exchange token < total request redeem then show error BalanceNotEnough
                //if (resultBalance.TokenBalance + sumExchangeAmount < input.Redeem.TotalAmount)
                //{
                //    //throw new ArgumentException("Token Balance is not enough to make this transaction");
                //    return new RedeemLoyaltyResult()
                //    {
                //        Error = true,
                //        ErrorCode = "BalanceNotEnough",
                //        Exception = "Token Balance is not enough to make this transaction",
                //        Items = null,
                //    };
                //}
                var requestRedeem = new RewardCreateGiftRedeemTransactionRequest()
                {
                    OrderCode = orderCode,
                    MerchantId = merchantId,
                    NationalId = input.MemberCode,
                    TotalRequestedAmount = input.Redeem.TotalAmount,
                };
                var resultRedeemReward = await _rewardGiftRedeemTransactionService.CreateRedeem(requestRedeem);
                var request = new LoyaltyCreateRedeemTransactionDto()
                {
                    Date = input.Redeem.Date,
                    Description = input.Redeem.Description,
                    GiftCode = input.Redeem.GiftCode,
                    MemberCode = input.MemberCode,
                    Quantity = input.Redeem.Quantity,
                    TotalAmount = input.Redeem.TotalAmount,
                    TransactionCode = orderCode,
                };
                var result = await _loyaltyGiftTransactionsService.PostLoyaltyRedeem(request, httpContext);
                if (!string.IsNullOrWhiteSpace(result.Result.Exception) || !result.Result.SuccessedRedeem)
                {
                    await RetryRevertTokenRedeem(requestRevert);
                    return new RedeemLoyaltyResult()
                    {
                        Error = true,
                        ErrorCode = result.Result.Exception,
                        Exception = result.Result.Messages,
                        Items = null,
                        Timeout = false,
                        ErrorLoyalty = true,
                    };
                }
                return new RedeemLoyaltyResult()
                {
                    Error = false,
                    Items = result.Result.Items,
                    ErrorLoyalty = false,
                };
            }
            catch (WebException ex)
            {
                var errorCode = "SystemError";
                var exception = "WebException";
                var errorLoyalty = false;
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    errorCode = res.Code;
                    exception = res.Message;
                    errorLoyalty = true;
                }
                else if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    errorCode = res.Code;
                    exception = res.Message;
                }
                _logger.LogInformation("create exchange redeem " + input.MemberCode + "_Error" + JsonConvert.SerializeObject(ex));
                await RetryRevertTokenRedeem(requestRevert);
                return new RedeemLoyaltyResult()
                {
                    Error = true,
                    ErrorCode = errorCode,
                    Exception = exception,
                    Items = null,
                    Timeout = false,
                    ErrorLoyalty = errorLoyalty,
                };
            }
            catch (TaskCanceledException ex)
            {
                var errorCode = "SystemError";
                var exception = "TaskCanceledException";
                var errorLoyalty = false;
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    errorCode = res.Code;
                    exception = res.Message;
                    errorLoyalty = true;
                }
                else if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    errorCode = res.Code;
                    exception = res.Message;
                }
                _logger.LogInformation("create exchange redeem " + input.MemberCode + "_Error" + JsonConvert.SerializeObject(ex));
                await RetryRevertTokenRedeem(requestRevert);
                if (!cts.Token.IsCancellationRequested)
                {
                    return new RedeemLoyaltyResult()
                    {
                        Error = true,
                        ErrorCode = errorCode,
                        Exception = exception,
                        Items = null,
                        Timeout = true,
                        ErrorLoyalty = errorLoyalty,
                    };
                }
                else
                {
                    return new RedeemLoyaltyResult()
                    {
                        Error = true,
                        ErrorCode = errorCode,
                        Exception = exception,
                        Items = null,
                        Timeout = true,
                        ErrorLoyalty = errorLoyalty,
                    };
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
            catch (Exception ex)
            {
                var errorCode = "SystemError";
                var exception = "WebException";
                var errorLoyalty = false;
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    errorCode = res.Code;
                    exception = res.Message;
                    errorLoyalty = true;
                }
                else if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    errorCode = res.Code;
                    exception = res.Message;
                }
                _logger.LogInformation("create exchange redeem " + input.MemberCode + "_Error" + JsonConvert.SerializeObject(ex));
                await RetryRevertTokenRedeem(requestRevert);
                return new RedeemLoyaltyResult()
                {
                    Error = true,
                    ErrorCode = errorCode,
                    Exception = exception,
                    Items = null,
                    Timeout = false,
                    ErrorLoyalty = errorLoyalty,
                };
            }
        }

        private async Task RetryRevertTokenRedeem(RewardRevertGiftRedeemTransactionRequest request)
        {
            var retryNum = 5;
            while (retryNum != 0)
            {
                var result = await RevertTokenRedeem(request);
                if (result)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        private async Task<bool> RevertTokenRedeem(RewardRevertGiftRedeemTransactionRequest request)
        {
            try
            {
                await _rewardGiftRedeemTransactionService.RevertRedeem(request);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task RetryRevertTokenExchange(RewardRevertExchangeTransactionInput request)
        {
            var retryNum = 8;
            while (retryNum != 0)
            {
                var result = await RevertTokenExchange(request);
                if (result)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        private async Task<bool> RevertTokenExchange(RewardRevertExchangeTransactionInput request)
        {
            try
            {
                _logger.LogInformation("exchange and redeem revert token exchange" + JsonConvert.SerializeObject(request));
                await _rewardExchangeTransactionService.RevertExchangeTransactionIntegration(request);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private string genOrderCode()
        {
            var random = new Random().Next(99999).ToString("00000");
            return LoyaltyHelper.GenTransactionCodeV2(random);
        }

        private bool CheckValidInput(RewardCreateExchangeAndRedeemTransactionInput input)
        {
            if (input.Redeem == null || input.ExchangeList == null || (input.ExchangeList != null && input.ExchangeList.Count == 0))
            {
                return false;
            }
            var checkExchangeAmountValid = input.ExchangeList.Where(x => x.ExchangeAmount <= 0).Select(x => x.ExchangeAmount).ToList();
            if (checkExchangeAmountValid.Count() > 0)
            {
                return false;
            }
            //var sumExchangeAmount = input.ExchangeList.Select(x => x.ExchangeAmount).Sum(x => x);
            //if (sumExchangeAmount < input.Redeem.TotalAmount)
            //{
            //    return false;
            //}
            return true;
        }
    }

    public class DetailExchangeAndRedeem
    {
        public decimal CurrentBalance { get; set; }
        public decimal ExchangeBalance { get; set; }
        public decimal RedeemBalance { get; set; }
    }
}
