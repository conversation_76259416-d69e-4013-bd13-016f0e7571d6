﻿using AKC.MobileAPI.DTO.Loyalty.Article;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyArticleService : BaseLoyaltyService, ILoyaltyArticleService
    {
        public LoyaltyArticleService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<LoyaltyArticleGetAllOutput> GetAll(LoyaltyArticleGetAllInput input)
        {
            return await GetLoyaltyAsync<LoyaltyArticleGetAllOutput>(LoyaltyApiUrl.ARTICLE_GETALL, input);
        }
        public async Task<GetArticleForEditOutput> GetArticleByIdAndRelatedNews(GetArticleByIdAndRelatedNewsInput input)
        {
            return await GetLoyaltyAsync<GetArticleForEditOutput>(LoyaltyApiUrl.GET_ARTICLE_BY_ID, input);
        }
        public async Task<GetArticleForEditOutput> GetArticleByIdByMemberCode(GetArticleByIdByMemberCodeInput input)
        {
            return await GetLoyaltyAsync<GetArticleForEditOutput>(LoyaltyApiUrl.GET_ARTICLE_BY_ID_BY_MEMBERCODE, input);
        }
        public async Task<GetAllArticleByMemberCodeOutPut> GetAllArticleByMemberCode(GetAllArticleByMemberCodeInput input)
        {
            return await GetLoyaltyAsync<GetAllArticleByMemberCodeOutPut>(LoyaltyApiUrl.GET_ALL_ARTICLE_BY_MEMBERCODE, input);
        }
        public async Task<GetAllArticleAndRelatedNewsOutput> GetAllArticleAndRelatedNews(GetAllArticleAndRelatedNewsInput input)
        {
            return await GetLoyaltyAsync<GetAllArticleAndRelatedNewsOutput>(LoyaltyApiUrl.GET_ALL_ARTICLE_AND_RELATED_NEWS, input);
        }
    }
}
