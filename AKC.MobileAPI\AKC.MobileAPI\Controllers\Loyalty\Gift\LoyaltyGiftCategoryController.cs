﻿//using AKC.MobileAPI.DTO.Loyalty.Gift;
//using AKC.MobileAPI.Service.Abstract.Loyalty.Gift;
//using AKC.MobileAPI.Service.Exceptions;
//using Microsoft.AspNetCore.Authorization;
//using Microsoft.AspNetCore.Mvc;
//using Microsoft.Extensions.Logging;
//using Newtonsoft.Json;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading.Tasks;

//namespace AKC.MobileAPI.Controllers.Loyalty.Gift
//{
//    [Route("api/GiftCategory")]
//    [ApiController]
//    [ApiConventionType(typeof(DefaultApiConventions))]
//    [Authorize]
//    public class LoyaltyGiftCategoryController : ControllerBase
//    {
//        private readonly ILogger _logger;
//        private readonly ILoyaltyGiftService _loyaltyGiftService;
//        private readonly IExceptionReponseService _exceptionReponseService;

//        public LoyaltyGiftCategoryController
//        (
//              ILogger<LoyaltyGiftCategoryController> logger,
//              ILoyaltyGiftService loyaltyGiftService,
//              IExceptionReponseService exceptionReponseService
//        )
//        {
//            _logger = logger;
//            _loyaltyGiftService = loyaltyGiftService;
//            _exceptionReponseService = exceptionReponseService;
//        }

//        [HttpGet]
//        [Route("GetAll")]
//        public async Task<ActionResult<LoyaltyGiftCategoryGetAllOutput>> GetALL([FromQuery] LoyaltyGiftCategoryGetAllInput input)
//        {
//            try
//            {
//                var result = await _loyaltyGiftService.GetAll(input);
//                return StatusCode(200, result);
//            }
//            catch (Exception ex)
//            {
//                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
//                _logger.LogError(ex, "GettAll GiftCategory Error - " + JsonConvert.SerializeObject(ex));

//                return StatusCode(400, res);
//            }
//        }
//        [HttpGet]
//        [Route("GetAllGiftCategoriesAndInfo")]
//        public async Task<ActionResult<GetGiftCategoryAndInfoForView>> GetAllGiftCategoriesAndInfo([FromQuery] GetAllGiftCategoriesAndInfoInput input)
//        {
//            try
//            {
//                var result = await _loyaltyGiftService.GetAllGiftCategoriesAndInfo(input);
//                return StatusCode(200, result);
//            }
//            catch (Exception ex)
//            {
//                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
//                _logger.LogError(ex, "GettAll GiftCategory Error - " + JsonConvert.SerializeObject(ex));

//                return StatusCode(400, res);
//            }
//        }

//        [HttpGet]
//        [Route("GetAllGiftCategoriesAndInfo_V1")]
//        public async Task<ActionResult<GetGiftCategoryAndInfoForView>> GetAllGiftCategoriesAndInfo_V1([FromQuery] GetAllGiftCategoriesAndInfoInput input)
//        {
//            try
//            {
//                var result = await _loyaltyGiftService.GetAllGiftCategoriesAndInfo_V1(input);
//                return StatusCode(200, result);
//            }
//            catch (Exception ex)
//            {
//                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
//                _logger.LogError(ex, "GettAll GiftCategory v1 Error - " + JsonConvert.SerializeObject(ex));

//                return StatusCode(400, res);
//            }
//        }

//        [HttpGet]
//        [Route("GetAllByMemberCode")]
//        public async Task<ActionResult<GetAllByMemberCodeOutput>> GetAllByMemberCode([FromQuery] GetAllByMemberCodeGiftCategoriesInput input)
//        {
//            try
//            {
//                var result = await _loyaltyGiftService.GetAllByMemberCode(input);
//                return StatusCode(200, result);
//            }
//            catch (Exception ex)
//            {
//                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
//                _logger.LogError(ex, "GettAll GiftCategory ByMemberCode Error - " + JsonConvert.SerializeObject(res));

//                return StatusCode(400, res);
//            }
//        }
//    }
//}
