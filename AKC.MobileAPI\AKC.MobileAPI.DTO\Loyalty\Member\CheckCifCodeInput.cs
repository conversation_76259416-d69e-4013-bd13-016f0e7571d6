﻿using System;
using System.ComponentModel.DataAnnotations;

namespace AKC.MobileAPI.DTO.Loyalty.Member
{
    public class CheckCifCodeInput
    {
        //[Required]
        public string CifCode { get; set; }
        public CheckCifCodeLoyaltyInfo LoyaltyInfo { get; set; }
    }

    public class CheckCifCodeLoyaltyInfo
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }

        //[Required]
        [DataType(DataType.Date)]
        public string Birthday { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public string Gender { get; set; }

        //[Required]
        public string IdCard { get; set; }

        //[Required]
        public string Segment { get; set; }
        public string VipType { get; set; }
        public string IsStaff { get; set; }
    }

    public class GetContactListFromIdCardInput
    {
        public string IdCard { get; set; }
    }
}
