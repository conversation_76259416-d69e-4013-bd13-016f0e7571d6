﻿using AKC.MobileAPI.DTO.Loyalty.FAQ;
using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Loyalty;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;
using System.Web;

namespace AKC.MobileAPI.Service
{
    public class LoyaltyFAQService : BaseLoyaltyService, ILoyaltyFAQService
    {
        public LoyaltyFAQService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<GetAllFAQOutputDto> GetAll(GetAllFAQInputDto input)
        {
            return await GetLoyaltyAsync<GetAllFAQOutputDto>(LoyaltyApiUrl.FAQ_GET_ALL, input);
        }

    }
}
