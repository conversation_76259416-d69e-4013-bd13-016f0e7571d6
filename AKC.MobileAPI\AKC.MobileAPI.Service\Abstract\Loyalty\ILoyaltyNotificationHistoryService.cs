﻿using AKC.MobileAPI.DTO.Loyalty.NotificationHistory;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyNotificationHistoryService
    {
        Task<LoyaltyNotificationHistoryGetAllOutput> GetAllByMemberCode(MobileLoyaltyGetAllNotificationsInput input, string authorization);

        Task<LoyaltyNotificationHistoryChangeStatusOutput> ChangeStatus(MobileLoyaltyChangeStatusInput input, string authorization);

        Task<LoyaltyNotificationHistoryChangeCountOutput> ChangeNotificationCount(MobileLoyaltyChangeNotificationCountInput input, string authorization);

        Task<LoyaltyNotificationHistoryDeleteOutput> Delete(MobileLoyaltyDeleteInput input, string authorization);

        Task<LoyaltyNotificationHistoryDeleteOutput> HardDelete(MobileLoyaltyDeleteInput input, string authorization);
    }
}
