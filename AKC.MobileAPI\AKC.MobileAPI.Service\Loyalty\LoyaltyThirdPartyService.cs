﻿using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.Reward.ExchangeTransaction;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.PartnerPointCaching;
using AKC.MobileAPI.DTO.ThirdParty.Dummy;
using AKC.MobileAPI.DTO.ThirdParty.VPBank;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Abstract.ThirdParty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Constants;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyThirdPartyService : BaseLoyaltyService, ILoyaltyThirdPartyService
    {
        private readonly IThirdPartyVPBankService _thirdPartyVPBankService;
        private readonly IRewardPartnerPointCachingService _rewardPartnerPointCachingService;
        private readonly IThirdPartyDummyService _thirdPartyDummyService;
        private readonly IRewardMemberService _rewardMemberService;
        private readonly IRewardExchangeTransactionService _rewardExchangeTransactionService;
        private readonly IStorageS3Service _storageS3;
        public LoyaltyThirdPartyService(
            IConfiguration configuration,
            IDistributedCache cache,
            IThirdPartyVPBankService thirdPartyVPBankService,
            IRewardPartnerPointCachingService rewardPartnerPointCachingService,
            IThirdPartyDummyService thirdPartyDummyService,
            IRewardMemberService rewardMemberService,
            IRewardExchangeTransactionService rewardExchangeTransactionService,
            IStorageS3Service storageS3) : base(configuration, cache)
        {
            _thirdPartyVPBankService = thirdPartyVPBankService;
            _rewardPartnerPointCachingService = rewardPartnerPointCachingService;
            _thirdPartyDummyService = thirdPartyDummyService;
            _rewardMemberService = rewardMemberService;
            _rewardExchangeTransactionService = rewardExchangeTransactionService;
            _storageS3 = storageS3;
        }

        public async Task<LoyaltyThirdPartyPointViewOutput> PointView(LoyaltyThirdPartyPointViewInput input, HttpContext context)
        {
            if (!string.IsNullOrEmpty(input.NationalId))
                input.MemberCode = input.NationalId;

            var merchant = getMerchantConfigById(input.MerchantId);
            switch (merchant)
            {
                case "VPBank":
                    return await _thirdPartyVPBankService.PointView(input, context);
                default:
                    // For dump data
                    return await _thirdPartyDummyService.PointView(input);
            }
            throw new ArgumentException("System error");
        }

        public async Task<LoyaltyThirdPartyVerifyNationalIdOutput> VerifyNationalId(LoyaltyThirdPartyVerifyNationalIdInput input, HttpContext context)
        {
            var isCheckPhoneNumberExchangeList = _configuration.GetSection("IsCheckPhoneNumberExchangeList").Value;
            if (isCheckPhoneNumberExchangeList == "true")
            {
                //var memberInfo = await _rewardMemberService.GetInfo(new RewardMemberGetInfoInput()
                //{
                //    NationalId = input.MemberCode
                //});

                var keyName = _configuration.GetSection("StoreFiles:AWSS3:KeyName").Value;
                var phoneNumberListString = await _storageS3.DownloadFileAndGetData(keyName);
                var phoneNumberList = phoneNumberListString.Replace("\r", "").Split("\n").ToList();
                var flag = false;

                if (phoneNumberList != null && phoneNumberList.Count > 0)
                {
                    foreach (var item in phoneNumberList)
                    {
                        if (item.Trim() == input.PhoneNumber)
                        {
                            flag = true;
                            break;
                        }
                    }
                }

                if (!flag)
                {
                    throw new Exception("YouAreNotInTheTestList");
                }
            }

            var merchant = getMerchantConfigById(input.MerchantId);
            if (!string.IsNullOrEmpty(input.NationalId))
                input.MemberCode = input.NationalId;
            await _rewardMemberService.VerifyIsIdCardVerified(new RewardMemberVerifyIsIdCardVerifiedInput()
            {
                MemberCode = input.MemberCode,
                IdCard = input.IdNumber,
            });
            switch (merchant)
            {
                case "VPBank":
                    return await _thirdPartyVPBankService.VerifyNationalId(input, context);
                default:
                    return await _thirdPartyDummyService.VerifyNationalId(input);
            }
            throw new ArgumentException("System error");
        }

        public async Task<LoyaltyThirdPartyVerifyOTPOutput> VerifyOTP(LoyaltyThirdPartyVerifyOTPInput input, HttpContext context)
        {
            var merchant = getMerchantConfigById(input.MerchantId);
            var firstAction = new RewardMemberFirstActionMemberInput()
            {
                MemberCode = input.MemberCode,
                MerchantId = input.MerchantId,
                Type = "ConnectMerchant",
                IsFirst = true,
            };
            switch (merchant)
            {
                case "VPBank":
                    var result = await _thirdPartyVPBankService.VerifyOTP(input, context);
                    await _rewardMemberService.FirstActionMember(firstAction);
                    return result;
                default:
                    result = await _thirdPartyDummyService.VerifyOTP(input);
                    await _rewardMemberService.FirstActionMember(firstAction);
                    return result;
            }
            throw new ArgumentException("System error");
        }

        public async Task<LoyaltyThirdPartyPointExchangeOutput> PointExchange(LoyaltyThirdPartyPointExchangeInput input, HttpContext context, string orderCode = null)
        {
            //var isCheckPhoneNumberExchangeList = _configuration.GetSection("IsCheckPhoneNumberExchangeList").Value;
            //if (isCheckPhoneNumberExchangeList == "true")
            //{
            //    var memberInfo = await _rewardMemberService.GetInfo(new RewardMemberGetInfoInput()
            //    {
            //        NationalId = input.MemberCode
            //    });

            //    var keyName = _configuration.GetSection("StoreFiles:AWSS3:KeyName").Value;
            //    var phoneNumberListString = await _storageS3.DownloadFileAndGetData(keyName);
            //    var phoneNumberList = phoneNumberListString.Split("\n").ToList();

            //    if (!phoneNumberList.Contains(memberInfo.PartnerPhoneNumber))
            //    {
            //        throw new Exception("YouAreNotInTheTestList");
            //    }
            //}

            var merchant = getMerchantConfigById(input.MerchantId);
            switch (merchant)
            {
                case "VPBank":
                    return await _thirdPartyVPBankService.PointExchange(input, context, orderCode);
                default:
                    return await _thirdPartyDummyService.PointExchange(input, orderCode);
            }
            throw new ArgumentException("System error");
        }

        public async Task<LoyaltyThirdPartyPointExchangeOutput> PointExchangeIntegration(LoyaltyThirdPartyPointExchangeInput input, HttpContext context, string orderCode = null)
        {
            var merchant = getMerchantConfigById(input.MerchantId);
            switch (merchant)
            {
                case "VPBank":
                    return await _thirdPartyVPBankService.PointExchangeIntegration(input, context, orderCode);
                default:
                    return await _thirdPartyDummyService.PointExchangeIntegration(input, orderCode);
            }
            throw new ArgumentException("System error");
        }

        public async Task<LoyaltyThirdPartyRevertPointOutput> RevertPoint(LoyaltyThirdPartyRevertPointInput input, HttpContext context)
        {
            var merchant = getMerchantConfigById(input.MerchantId);
            switch (merchant)
            {
                case "VPBank":
                    return await _thirdPartyVPBankService.RevertPoint(input, context);
                default:
                    return await _thirdPartyDummyService.RevertPoint(input, context);
            }
            throw new ArgumentException("System error");
        }


        public async Task<LoyaltyThirdPartyRequestAccessTokenOutput> RequestAccessToken(LoyaltyThirdPartyRequestAccessTokenInput input, HttpContext context)
        {
            var merchant = getMerchantConfigById(input.MerchantId);
            if (string.IsNullOrWhiteSpace(merchant))
            {
                throw new ArgumentException("Merchant invalid");
            }
            switch (merchant)
            {
                case "VPBank":
                    return await _thirdPartyVPBankService.RequestAccessToken(input, context);
                default:
                    break;
            }
            throw new ArgumentException("System error");
        }

        public async Task<LoyaltyThirdPartyUpdatePartnerCachingOutput> UpdatePartnerCaching(LoyaltyThirdPartyUpdatePartnerCachingInput input, HttpContext context)
        {
            if (!string.IsNullOrEmpty(input.NationalId))
                input.MemberCode = input.NationalId;
            var requestPartnerPoint = new RewardPartnerPoingCachingInput()
            {
                NationalId = input.MemberCode,
                Items = new List<RewardPartnerPoingCachingItems>()
            };
            foreach (var item in input.Items)
            {
                var merchant = getMerchantConfigById(item.MerchantId);
                switch (merchant)
                {
                    case "VPBank":
                        var requestVPBank = new LoyaltyThirdPartyVPBankUpdatePartnerCachingInput()
                        {
                            AccessToken = item.AccessToken,
                            IdNumber = item.IdNumber,
                            MerchantId = item.MerchantId,
                            MemberCode = input.MemberCode
                        };
                        var responseVPBank = await _thirdPartyVPBankService.UpdatePartnerCaching(requestVPBank, context);
                        requestPartnerPoint.Items.Add(responseVPBank);
                        break;
                    default:
                        var requestDummy = new LoyaltyThirdPartyDummyUpdatePartnerCachingInput()
                        {
                            IdNumber = item.IdNumber,
                            MerchantId = item.MerchantId,
                        };
                        var responseDummy = await _thirdPartyDummyService.UpdatePartnerCaching(requestDummy);
                        requestPartnerPoint.Items.Add(responseDummy);
                        break;
                }
            }

            int result = 200;

            if (requestPartnerPoint.Items.Count == 0)
            {
                throw new ArgumentException("Please input items data for request");
            }
            else
            {
                await _rewardPartnerPointCachingService.RequestUpdateIntegration(requestPartnerPoint);

                foreach (var item in requestPartnerPoint.Items)
                {
                    if (item.HaveException)
                    {
                        result = 400;
                        break;
                    }
                }
            }

            return new LoyaltyThirdPartyUpdatePartnerCachingOutput()
            {
                Result = result,
                Message = result == 200 ? "Success" : "Error",
                Items = requestPartnerPoint.Items
            };
        }

        private string getMerchantConfigById(int id)
        {
            try
            {
                var flag = "";
                var listThirdPartyMerchant = _configuration.GetSection("ThirdPartyMerchant").GetChildren();
                foreach (var item in listThirdPartyMerchant)
                {
                    var merchantId = _configuration.GetSection(item.Path + ":" + "MerchantId").Value;
                    if (merchantId != null && merchantId == id.ToString())
                    {
                        flag = item.Key;
                        break;
                    }
                }
                return flag;
            }
            catch
            {
                return "";
            }

        }
    }
}
