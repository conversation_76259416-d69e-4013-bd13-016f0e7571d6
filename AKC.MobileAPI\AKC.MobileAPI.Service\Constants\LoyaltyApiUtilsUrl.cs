﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.Service.Constants
{
    public class LoyaltyApiUtilsUrl
    {
        //Schema flex card
        public const string GET_ALL_SCHEMA_FLEX_CARD = "SchemaFlexCard/GetAll";
        public const string GET_HISTORY_SCHEMA_FLEX_CARD = "SchemaFlexCard/GetHistory";
        public const string CREATE_SCHEMA_FLEX_CARD = "SchemaFlexCard/Create";
        public const string UPDATE_SCHEMA_FLEX_CARD = "SchemaFlexCard/Update";
        //Master card
        public const string MASTER_CARD_CUSTOMER_CHECK = "MasterCard/CustomerCheck";
        public const string MASTER_CARD_GET_CAMPAIGN = "MasterCard/GetCampaignByCustomer";
        public const string MASTER_CARD_REGISTER_CAMPAIGN = "MasterCard/RegisterCampaign";
        public const string MASTER_CARD_GET_GIFT = "MasterCard/GetGiftByCampaign";
        public const string MASTER_CARD_GET_GIFT_REDEEM = "MasterCard/GetAllGiftRedeemed";
        public const string MASTER_CARD_UPDATE_CHALLENGE = "MasterCard/GetInforChallenge";
        public const string MASTER_CARD_UPDATE_REDEEM_GIFT = "MasterCard/UpdateRedeemGift";
        public const string MASTER_CARD_GET_CARD_COIN = "MasterCard/GetCardCoin";
        public const string MASTER_CARD_GET_CARD_BY_CARD_CODE = "MasterCard/GetGroupCodeByCardCode";
        public const string MASTER_CARD_GET_CUSTOMER_INFOR_PROCESSING = "MasterCard/GetInforCustomerProcessing";
        public const string MASTER_CARD_GET_CUSTOMER_INFOR = "MasterCard/GetMemberCodeByCif";
        public const string MASTER_CARD_GET_UPDATE_PROCESSING_CHALLENGE = "MasterCard/UpdateInforChallenge";
        public const string MASTER_CARD_CHECK_CIF_WITH_CAMPAIGN = "MasterCard/CheckCifWithCampaign";
        public const string MASTER_CARD_UPDATE_REDEEM_STATUS_CARD = "MasterCard/UpdateRedeemStatusCoin";
        public const string MASTER_CARD_CAMPAIGN_INFOR = "MasterCard/GetListCampaigns";
        //BusinessLounge
        public const string BUSINESS_LOUNGE_CHECK_CIF = "BusinessLounge/CheckCif";
        public const string BUSINESS_LOUNGE_GET_LIST = "BusinessLounge/GetListAirportLounge";
        public const string AdminGiveGiftToMember = "GiftTransaction/AdminGiveGiftToMember";
        public const string GetAllGiftRedeemTransactionByGiftCategoryChannel = "GiftTransaction/GetAllGiftRedeemTransactionByGiftCategoryChannel_V1";
    }
}
