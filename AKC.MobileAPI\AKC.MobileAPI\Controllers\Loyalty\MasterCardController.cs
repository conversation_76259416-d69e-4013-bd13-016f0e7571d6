using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Const;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.MasterCard;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/mastercard")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class MasterCardController : ControllerBase
    {
        private IMasterCardService _masterCardService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILogger _logger;
        public MasterCardController(IMasterCardService masterCardService, ILogger<MasterCardController> logger, IExceptionReponseService exceptionReponseService)
        {
            _masterCardService = masterCardService;
            _exceptionReponseService = exceptionReponseService;
            _logger = logger;
        }
        [HttpPost]
        [Route("CustomerCheck")]
        public async Task<ActionResult<MasterCardOutput<MasterCardCheckRegisterOutput>>> CustomerCheck(MasterCardCustomerInput input)
        {
            try
            {
                _logger.LogInformation($" >> CustomerCheck >> {JsonConvert.SerializeObject(input)}");
                var output = await _masterCardService.CustomerCheck(input);
                _logger.LogInformation($" >> CustomerCheck Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException) && ex.Data["ErrorType"] != null && ex.Data["ErrorType"].ToString() == MasterCardConsts.ErrorTypeKey)
                {
                    var res = await _exceptionReponseService.GetExceptionFlexCardSchemaReponse(ex);
                    if (res != null && res.ErrorCode != 500)
                    {
                        _logger.LogInformation(ex, "MasterCard CustomerCheck - " + JsonConvert.SerializeObject(ex));
                    }
                    else
                    {
                        _logger.LogError(ex, "MasterCard CustomerCheck Error - " + JsonConvert.SerializeObject(ex));
                    }
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = res.ErrorCode, ErrorMessage = res.ErrorMessage });
                }
                else
                {
                    _logger.LogError(ex, "MasterCard CustomerCheck Error - " + JsonConvert.SerializeObject(ex));
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = Int32.Parse(res.Code), ErrorMessage = res.Message });
                }
            }
        }
        [HttpGet]
        [Route("GetCampaignByCustomer")]
        public async Task<ActionResult<LoyaltyResponsResultDto<MasterCardCampaignOutput>>> GetCampaignByCustomer(string Cif, string Status, string lang = "vi")
        {
            try
            {
                _logger.LogInformation($" >> GetCampaignByCustomer >> {Cif} - {Status} - {lang}");
                var output = await _masterCardService.GetCampaignByCustomer(Cif, Status, lang);
                _logger.LogInformation($" >> GetCampaignByCustomer Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException) && ex.Data["ErrorType"] != null && ex.Data["ErrorType"].ToString() == MasterCardConsts.ErrorTypeKey)
                {
                    var res = await _exceptionReponseService.GetExceptionFlexCardSchemaReponse(ex);
                    if (res != null && res.ErrorCode != 500)
                    {
                        _logger.LogInformation(ex, "MasterCard GetCampaignByCustomer Error - " + JsonConvert.SerializeObject(ex));
                    }
                    else
                    {
                        _logger.LogError(ex, "MasterCard GetCampaignByCustomer Error - " + JsonConvert.SerializeObject(ex));
                    }
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = res.ErrorCode, ErrorMessage = res.ErrorMessage });
                }
                else
                {
                    _logger.LogError(ex, "MasterCard GetCampaignByCustomer Error - " + JsonConvert.SerializeObject(ex));
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = Int32.Parse(res.Code), ErrorMessage = res.Message });
                }
            }
        }
        [HttpPost]
        [Route("RegisterCampaign")]
        public async Task<ActionResult<MasterCardOutput<string>>> RegisterCampaign(RegisterCampaignInput input)
        {
            try
            {
                _logger.LogInformation($" >> RegisterCampaign >> {JsonConvert.SerializeObject(input)}");
                var output = await _masterCardService.RegisterCampaign(input);
                _logger.LogInformation($" >> RegisterCampaign Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException) && ex.Data["ErrorType"] != null && ex.Data["ErrorType"].ToString() == MasterCardConsts.ErrorTypeKey)
                {
                    var res = await _exceptionReponseService.GetExceptionFlexCardSchemaReponse(ex);
                    if (res != null && res.ErrorCode != 500)
                    {
                        _logger.LogInformation(ex, "MasterCard RegisterCampaign Error - " + JsonConvert.SerializeObject(ex));
                    }
                    else
                    {
                        _logger.LogError(ex, "MasterCard RegisterCampaign Error - " + JsonConvert.SerializeObject(ex));
                    }
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = res.ErrorCode, ErrorMessage = res.ErrorMessage });
                }
                else
                {
                    _logger.LogError(ex, "MasterCard RegisterCampaign Error - " + JsonConvert.SerializeObject(ex));
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = Int32.Parse(res.Code), ErrorMessage = res.Message });
                }
            }
        }
        [HttpGet]
        [Route("GetGiftByCampaign")]
        public async Task<ActionResult<LoyaltyResponsResultDto<MasterCardGiftByCampaignOutput>>> GetGiftByCampaign(string Cif, string CampaignId, string lang = "vi")
        {
            try
            {
                _logger.LogInformation($" >> GetGiftByCampaign >> Cif: {Cif} - CampaignId: {CampaignId} - {lang}");
                var output = await _masterCardService.GetGiftByCampaign(Cif, CampaignId, lang);
                _logger.LogInformation($" >> GetGiftByCampaign Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException) && ex.Data["ErrorType"] != null && ex.Data["ErrorType"].ToString() == MasterCardConsts.ErrorTypeKey)
                {
                    var res = await _exceptionReponseService.GetExceptionFlexCardSchemaReponse(ex);
                    if (res != null && res.ErrorCode != 500)
                    {
                        _logger.LogInformation(ex, "MasterCard GetGiftByCampaign Error - " + JsonConvert.SerializeObject(ex));
                    }
                    else
                    {
                        _logger.LogError(ex, "MasterCard GetGiftByCampaign Error - " + JsonConvert.SerializeObject(ex));
                    }
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = res.ErrorCode, ErrorMessage = res.ErrorMessage });
                }
                else
                {
                    _logger.LogError(ex, "MasterCard GetGiftByCampaign Error - " + JsonConvert.SerializeObject(ex));
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = Int32.Parse(res.Code), ErrorMessage = res.Message });
                }
            }
        }
        [HttpGet]
        [Route("GetGiftByCampaignGroupBrand")]
        public async Task<ActionResult<LoyaltyResponsResultDto<MasterCardGiftByCampaignGroupBrandOutput>>> GetGiftByCampaignGroupBrand(string Cif, string CampaignId, string CardCode, string lang = "vi")
        {
            try
            {
                if (string.IsNullOrEmpty(CardCode))
                {
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = 112, ErrorMessage = "CardCode Is Required" });
                }
                _logger.LogInformation($" >> GetGiftByCampaignGroupBrand >> Cif: {Cif} - CampaignId: {CampaignId} - CardCode: {CardCode} - lang: {lang}");
                var output = await _masterCardService.GetGiftByCampaignGroupBrand(Cif, CampaignId, CardCode, lang);
                _logger.LogInformation($" >> GetGiftByCampaignGroupBrand Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException) && ex.Data["ErrorType"] != null && ex.Data["ErrorType"].ToString() == MasterCardConsts.ErrorTypeKey)
                {
                    var res = await _exceptionReponseService.GetExceptionFlexCardSchemaReponse(ex);
                    if (res != null && res.ErrorCode != 500)
                    {
                        _logger.LogInformation(ex, "MasterCard GetGiftByCampaignGroupBrand Error - " + JsonConvert.SerializeObject(ex));
                    }
                    else
                    {
                        _logger.LogError(ex, "MasterCard GetGiftByCampaignGroupBrand Error - " + JsonConvert.SerializeObject(ex));
                    }
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = res.ErrorCode, ErrorMessage = res.ErrorMessage });
                }
                else
                {
                    _logger.LogError(ex, "MasterCard GetGiftByCampaignGroupBrand Error - " + JsonConvert.SerializeObject(ex));
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = Int32.Parse(res.Code), ErrorMessage = res.Message });
                }
            }
        }
        [HttpGet]
        [Route("GetAllGiftRedeemed")]
        public async Task<ActionResult<MasterCardOutput<GiftRedeemedAndCardInfor>>> GetAllGiftRedeemed(string Cif, string lang = "vi")
        {
            try
            {
                _logger.LogInformation($" >> GetAllGiftRedeemed >> {Cif} - {lang}");
                var output = await _masterCardService.GetAllGiftRedeemed(Cif, lang);
                _logger.LogInformation($" >> GetAllGiftRedeemed Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException) && ex.Data["ErrorType"] != null && ex.Data["ErrorType"].ToString() == MasterCardConsts.ErrorTypeKey)
                {
                    var res = await _exceptionReponseService.GetExceptionFlexCardSchemaReponse(ex);
                    if (res != null && res.ErrorCode != 500)
                    {
                        _logger.LogInformation(ex, "MasterCard GetAllGiftRedeemed Error - " + JsonConvert.SerializeObject(ex));
                    }
                    else
                    {
                        _logger.LogError(ex, "MasterCard GetAllGiftRedeemed Error - " + JsonConvert.SerializeObject(ex));
                    }
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = res.ErrorCode, ErrorMessage = res.ErrorMessage });
                }
                else
                {
                    _logger.LogError(ex, "MasterCard GetAllGiftRedeemed Error - " + JsonConvert.SerializeObject(ex));
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = Int32.Parse(res.Code), ErrorMessage = res.Message });
                }
            }
        }
        [HttpGet]
        [Route("GetInforChallenge")]
        public async Task<ActionResult<ChallengeInforAfterUpdateOutput>> GetInforChallenge(string Cif, string CampaignId, string TransactionCode, string lang = "vi")
        {
            try
            {
                _logger.LogInformation($" >> GetInforChallenge >> Cif: {Cif} - CampaignId: {CampaignId} - TransactionCode: {TransactionCode}");
                var output = await _masterCardService.GetInforChallenge(Cif, CampaignId, TransactionCode, lang);
                _logger.LogInformation($" >> GetInforChallenge Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException) && ex.Data["ErrorType"] != null && ex.Data["ErrorType"].ToString() == MasterCardConsts.ErrorTypeKey)
                {
                    var res = await _exceptionReponseService.GetExceptionFlexCardSchemaReponse(ex);
                    if (res != null && res.ErrorCode != 500)
                    {
                        _logger.LogInformation(ex, "MasterCard GetInforChallenge Error - " + JsonConvert.SerializeObject(ex));
                    }
                    else
                    {
                        _logger.LogError(ex, "MasterCard GetInforChallenge Error - " + JsonConvert.SerializeObject(ex));
                    }
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = res.ErrorCode, ErrorMessage = res.ErrorMessage });
                }
                else if (ex.GetType() == typeof(AdapterException))
                {
                    _logger.LogError(ex, "MasterCard GetInforChallenge Error - " + JsonConvert.SerializeObject(ex));
                    var res = await _exceptionReponseService.GetExceptionAdapterReponse(ex);
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = res.statusCode, ErrorMessage = res.message });
                }
                else
                {
                    _logger.LogError(ex, "MasterCard GetInforChallenge Error - " + JsonConvert.SerializeObject(ex));
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = Int32.Parse(res.Code), ErrorMessage = res.Message });
                }
            }
        }
        [HttpPost]
        [Route("RedeemGift")]
        public async Task<ActionResult<MasterCardRedeemGiftOutput>> RedeemGift(MasterCardRedeemGiftInput input)
        {
            try
            {
                _logger.LogInformation($" >> RedeemGift >> {JsonConvert.SerializeObject(input)}");
                var output = await _masterCardService.RedeemGift(input);
                _logger.LogInformation($" >> RedeemGift Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException) && ex.Data["ErrorType"] != null && ex.Data["ErrorType"].ToString() == MasterCardConsts.ErrorTypeKey)
                {
                    var res = await _exceptionReponseService.GetExceptionFlexCardSchemaReponse(ex);
                    if (res != null && res.ErrorCode != 500)
                    {
                        _logger.LogInformation(ex, "MasterCard RedeemGift Error - " + JsonConvert.SerializeObject(ex));
                    }
                    else
                    {
                        _logger.LogError(ex, "MasterCard RedeemGift Error - " + JsonConvert.SerializeObject(ex));
                    }
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = res.ErrorCode, ErrorMessage = res.ErrorMessage });
                }
                else if (ex.GetType() == typeof(RewardException))
                {
                    _logger.LogError(ex, "MasterCard RedeemGift Error - " + JsonConvert.SerializeObject(ex));
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = res.Code, ErrorMessage = res.MessageDetail != null ? res.MessageDetail.ToString() : res.Message });
                }

                else
                {
                    _logger.LogError(ex, "MasterCard RedeemGift Error - " + JsonConvert.SerializeObject(ex));
                    if (ex.GetType() == typeof(LoyaltyException))
                    {
                        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                        return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = Int32.Parse(res.Code), ErrorMessage = res.Message });
                    }
                    else
                    {
                        return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = 500, ErrorMessage = ex.Message });
                    }
                }
            }
        }
        [HttpPost]
        [Route("RedeemMultiGift")]
        public async Task<ActionResult<MasterCardRedeemMultiGiftResponse>> RedeemMultiGift(MasterCardRedeemMultiGiftInput input)
        {
            try
            {
                _logger.LogInformation($" >> RedeemMultiGift >> {JsonConvert.SerializeObject(input)}");
                var output = await _masterCardService.RedeemMultiGift(input);
                _logger.LogInformation($" >> RedeemMultiGift Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException) && ex.Data["ErrorType"] != null && ex.Data["ErrorType"].ToString() == MasterCardConsts.ErrorTypeKey)
                {
                    var res = await _exceptionReponseService.GetExceptionFlexCardSchemaReponse(ex);
                    if (res != null && res.ErrorCode != 500)
                    {
                        _logger.LogInformation(ex, "MasterCard RedeemMultiGift Error - " + JsonConvert.SerializeObject(ex));
                    }
                    else
                    {
                        _logger.LogError(ex, "MasterCard RedeemMultiGift Error - " + JsonConvert.SerializeObject(ex));
                    }
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = res.ErrorCode, ErrorMessage = res.ErrorMessage });
                }
                else if (ex.GetType() == typeof(RewardException))
                {
                    _logger.LogError(ex, "MasterCard RedeemMultiGift Error - " + JsonConvert.SerializeObject(ex));
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = res.Code, ErrorMessage = res.MessageDetail != null ? res.MessageDetail.ToString() : res.Message });
                }
                else
                {
                    _logger.LogError(ex, "MasterCard RedeemMultiGift Error - " + JsonConvert.SerializeObject(ex));
                    if (ex.GetType() == typeof(LoyaltyException))
                    {
                        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                        return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = Int32.Parse(res.Code), ErrorMessage = res.Message });
                    }
                    else
                    {
                        return StatusCode(200, new MasterCardErrorResponse() { ErrorCode = 500, ErrorMessage = ex.Message });
                    }
                }
            }
        }
    }
}
