﻿using System.Collections.Generic;

namespace AKC.MobileAPI.Service.Helpers
{
    public class ActionTypeToMessageUtils
    {
        public static string GetActionTypeDescription(string actionType, string lang = "VI")
        {
            if (string.IsNullOrEmpty(actionType) || actionType.Trim() == "")
            {
                return "";
            }

            if (lang != "EN" && lang != "VI")
            {
                lang = "VI";
            }

            var dictionary = GetDictionary();
            var key = actionType + "_" + lang;
            return dictionary[key] ?? "";
        }

        private static Dictionary<string, string> GetDictionary()
        {
            var ret = new Dictionary<string, string>()
            {
                {"Action_EN", "Earn points"},
                {"Action_VI", "Cộng điểm tích lũy"},

                {"Adjust_EN", "Adjust points"},
                {"Adjust_VI", "Điều chỉnh điểm tích lũy"},

                {"AdjustMinus_EN", "Adjustment due to failed transaction"},
                {"AdjustMinus_VI", "Điều chỉnh điểm do GD thất bại"},

                {"AdjustPlus_EN", "Adjustment of plus points"},
                {"AdjustPlus_VI", "Điều chỉnh cộng điểm"},

                {"BatchManualGrant_EN", "Bonus points"},
                {"BatchManualGrant_VI", "Thưởng điểm"},

                {"CashedOut_EN", "Redeem to cash"},
                {"CashedOut_VI", "Đổi điểm thành tiền mặt"},

                {"CashOutFee_EN", "Fee for redeem to cash"},
                {"CashOutFee_VI", "Phí đổi điểm thành tiền mặt"},

                {"Claim_EN", "Redeem to cash from merchant"},
                {"Claim_VI", "Đổi điểm thành tiền mặt từ cửa hàng"},

                {"ClaimFee_EN", "Fee for redeem to cash from merchant"},
                {"ClaimFee_VI", "Phí đổi điểm thành tiền mặt từ cửa hàng"},

                {"Deposit_EN", "Grant Token"},
                {"Deposit_VI", "Cấp Token"},

                {"DepositFee_EN", "Fee of grant token"},
                {"DepositFee_VI", "Phí cấp token"},

                {"CreditBalance_EN", "Adjust points"},
                {"CreditBalance_VI", "Điều chỉnh điểm tích lũy"},

                {"Exchange_EN", "Redeem successfully"},
                {"Exchange_VI", "Đổi điểm thành công"},

                {"ExchangeAndUse_EN", ""},
                {"ExchangeAndUse_VI", ""},

                {"Expired_VI", "Điểm hết hạn"},
                {"Expired_EN", "Expired Points"},

                {"Order_EN", "Earn points"},
                {"Order_VI", "Cộng điểm tích lũy"},

                {"PayByToken_EN", "Use points at store"},
                {"PayByToken_VI", "Tiêu điểm tại cửa hàng"},

                {"Redeem_EN", "Redeem successfully"},
                {"Redeem_VI", "Đổi quà thành công"},

                {"ReturnAction_EN", "Adjustment due to failed transaction"},
                {"ReturnAction_VI", "Điều chỉnh điểm do GD thất bại"},

                {"ReturnFull_EN", "Adjustment due to failed transaction"},
                {"ReturnFull_VI", "Điều chỉnh điểm do GD thất bại"},

                {"ReturnPartial_EN", "Adjustment due to failed transaction"},
                {"ReturnPartial_VI", "Điều chỉnh điểm do GD thất bại"},

                {"ReturnPartnerPoint_EN", "Refund points to partners"},
                {"ReturnPartnerPoint_VI", "Hoàn trả điểm cho đối tác"},

                {"ReturnToken_EN", "Refund token"},
                {"ReturnToken_VI", "Hoàn lại token"},

                {"Revert_EN", "Adjustment due to failed transaction"},
                {"Revert_VI", "Điều chỉnh điểm do GD thất bại"},

                {"RevertCashOut_EN", "Adjust points"},
                {"RevertCashOut_VI", "Điều chỉnh điểm"},

                {"RevertCashOutFee_EN", "Fee refund"},
                {"RevertCashOutFee_VI", "Hoàn trả phí"},

                {"RevertExchange_EN", "Adjustment due to failed transaction"},
                {"RevertExchange_VI", "Điều chỉnh điểm"},

                {"RevertOrder_EN", "Adjustment due to failed transaction"},
                {"RevertOrder_VI", "Điều chỉnh điểm"},

                {"SingleManualGrant_EN", "Bonus points"},
                {"SingleManualGrant_VI", "Thưởng điểm"},

                {"Topup_EN", "Top up points from LynkiD eVoucher"},
                {"Topup_VI", "Nạp điểm từ LynkiD eVoucher"},

                {"Transfer_EN", "Transfer transaction"},
                {"Transfer_VI", "Giao dịch chuyển khoản"},

                {"BillPayment_EN", "Payment transaction"},
                {"BillPayment_VI", "Thanh toán hóa đơn"},

                {"RevertBillPayment_EN", "Adjust points"},
                {"RevertBillPayment_VI", "Điều chỉnh điểm"},

                {"RevertMLMPurchaseOrder_EN", "Refund points for merchant"},
                {"RevertMLMPurchaseOrder_VI", "Hoàn điểm lại cho merchant"},
            };
            return ret;
        }
    }
}