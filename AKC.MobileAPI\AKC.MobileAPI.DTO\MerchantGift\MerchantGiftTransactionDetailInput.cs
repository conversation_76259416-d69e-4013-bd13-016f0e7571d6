﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.MerchantGift
{
    public class MerchantGiftTransactionDetailInput
    {
        public string CifCode { get; set; }
        public string TransactionCode { get; set; }
    }

    public class MerchantGiftTransactionDetailInputDto
    {
        public string OwnerCodeFilter { get; set; }
        public string StatusFilter { get; set; }
        public string EGiftStatusFilter { get; set; }
        public DateTime? FromDateFilter { get; set; }
        public DateTime? ToDateFilter { get; set; }
        public string GiftTransactionCode { get; set; }
        public int MaxResultCount { get; set; }
        public int SkipCount { get; set; }
        public string Sorting { get; set; }
        public string Language { get; set; }
    }
}
