﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.MerchantGift
{
    public class MerchantGiftLocationShipInput
    {
        public int? IdFilter { get; set; }
        public string CodeFilter { get; set; }
        public string ParentCodeFilter { get; set; }
        public string LevelFilter { get; set; }
        public string NameFilter { get; set; }
        public string Sorting { get; set; }
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
    }

    public class MerchantGiftLocationShipByIdInput
    {
        public List<int> IdList { get; set; }
    }
}
