﻿using System;

namespace AKC.MobileAPI.DTO.Loyalty.GiftTransactions
{
    public class GetAllGiftTransactionByGiftCategoryChannelInput
    {
        public string Phone { get; set; }
        public string EgiftStatus { get; set; }
        public string Status { get; set; }
        public int SkipCount { get; set; } = 0;
        public int MaxResultCount { get; set; } = 10;
        public string Cif { get; set; }
        public string Email { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string RedeemSource { get; set; }
    }
}
