﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AKC.MobileAPI.DTO.Loyalty.MasterCard
{
    public class MasterCardGiftOutput
    {
        public GiftShortInforDto GiftInfor { get; set; }
        public List<ImageLinkDto> ImageLink { get; set; }
        public List<GiftUsageAddressShortDto> GiftUsageAddres { get; set; }
    }

    public class MasterCardGiftByCampaignOutput
    {
        public GiftRedeemedLoyaltyOuput GiftInfor { get; set; }
    }
    public class MasterCardGiftByCampaignGroupBrandOutput
    {
        public int? BrandId { get; set; }
        public string BrandName { get; set; }
        public List<GiftRedeemedLoyaltyOuput> GiftInfors { get; set; }
    }
    //public class MasterCardGiftRedeemOutput
    //{
    //    public GifRedeemInforDto GiftInfor { get; set; }
    //    public List<ImageLinkDto> ImageLink { get; set; }
    //    public List<GiftUsageAddressShortDto> GiftUsageAddress { get; set; }
    //}
    public class GiftShortInforDto
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Introduce { get; set; }
        public int? BrandId { get; set; }
        public string BrandName { get; set; }
        public string ThirdPartyBrandName { get; set; }
        public string Vendor { get; set; }
        public DateTime? EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public decimal RequiredCoin { get; set; }
        public string Status { get; set; }
        public decimal TotalQuantity { get; set; }
        public decimal UsedQuantity { get; set; }
        public decimal RemainingQuantity { get; set; }
        public decimal FullPrice { get; set; }
        public decimal DiscountPrice { get; set; }
        public bool IsEGift { get; set; }
        public string RegionCode { get; set; }
        public string ExpireDuration { get; set; }
        public int TotalWish { get; set; }
        public string VendorHotline { get; set; }
        public int? MerchantOrdinal { get; set; }
        public int? TotalRedeem { get; set; }
        public string VendorName { get; set; }
        public string VendorImage { get; set; }
        public string MerchantName { get; set; }
        public string MerchantAvatar { get; set; }
        public string BrandLinkLogo { get; set; }
        public string BrandDescription { get; set; }
        public string Condition { get; set; }
        public string ContactEmail { get; set; }
        public string ContactHotline { get; set; }
    }
    public class GifRedeemInforDto
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Introduce { get; set; }
        public string BrandName { get; set; }
        public string ThirdPartyBrandName { get; set; }
        public string GiftTransactionCode { get; set; }
        public string EgiftCode { get; set; }
        public DateTime? ExpiredDate { get; set; }
        public string Vendor { get; set; }
        public DateTime? EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public decimal RequiredCoin { get; set; }
        public string Status { get; set; }
        public decimal TotalQuantity { get; set; }
        public decimal UsedQuantity { get; set; }
        public decimal RemainingQuantity { get; set; }
        public decimal FullPrice { get; set; }
        public decimal DiscountPrice { get; set; }
        public bool IsEGift { get; set; }
        public string RegionCode { get; set; }
        public string ExpireDuration { get; set; }
        public int TotalWish { get; set; }
        public string VendorHotline { get; set; }
        public int? MerchantOrdinal { get; set; }
        public int? TotalRedeem { get; set; }
        public string VendorName { get; set; }
        public string VendorImage { get; set; }
        public string MerchantName { get; set; }
        public string MerchantAvatar { get; set; }
        public string BrandLinkLogo { get; set; }
        public string BrandDescription { get; set; }
        public string Condition { get; set; }
        public string ContactEmail { get; set; }
        public string ContactHotline { get; set; }
    }
    public class ImageLinkDto
    {
        public string Code { get; set; }
        public bool isActive { get; set; }
        public string FullLink { get; set; }
        public string Source { get; set; }
    }
    public class GiftUsageAddressShortDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }
        public string Latitude { get; set; }
        public string Longtitude { get; set; }
    }
    public class CampaignConfigInfor
    {
        public CampaignDto Campaign { get; set; }
        public List<BaseSettingDto> BaseSetting { get; set; }
    }
    public class BaseSettingConfigDto
    {
        public int CampaignId { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public DateTime? EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public string GiftGroupCode { get; set; }
        public string Rewarding { get; set; }
        public List<RewardingConfig> ListRewarding
        {
            get
            {
                return ListRewarding;
            }
            set
            {
                ListRewarding = !string.IsNullOrEmpty(Rewarding) ? JsonConvert.DeserializeObject<List<RewardingConfig>>(this.Rewarding) : value;
            }
        }

        public class RewardingConfig
        {
            public long? OrderAmountFrom { get; set; }
            public int? TargetTnx { get; set; }
            public long? PointRadio { get; set; }
            public long? CoinRadio { get; set; }
        }
    }

    public class CustomerInforByCardCode
    {
        public int CampaignId { get; set; }
        public string Cif { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
    }
}
