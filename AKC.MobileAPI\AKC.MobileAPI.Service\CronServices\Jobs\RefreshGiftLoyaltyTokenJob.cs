﻿using AKC.MobileAPI.Service.Loyalty;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.CronServices
{


    public class RefreshGiftLoyaltyTokenJob : CronJobService
    {
        private readonly ILogger<RefreshGiftLoyaltyTokenJob> _logger;
        private readonly IDistributedCache _cache;

        public RefreshGiftLoyaltyTokenJob(IScheduleConfig<RefreshGiftLoyaltyTokenJob> config,
            ILogger<RefreshGiftLoyaltyTokenJob> logger,
            IDistributedCache cache) : base(config.CronExpression, config.TimeZoneInfo)
        {
            _logger = logger;
            _cache = cache;
        }

        public override Task StartAsync(CancellationToken cancellationToken)
        {
            var delayToNextRun = CronHelper.GetDelayToNextRefreshToken(_expression, _timeZoneInfo);
            try
            {
                _logger.LogInformation("Job: Start RenewAccessToken LinkID");
                LoyaltyHelper.RenewAccessTokenLinkIDCacheValue(_cache, delayToNextRun);
            }
            catch (Exception ex)
            {
                _logger.LogError("Job: RenewAccessToken LinkID error" + JsonConvert.SerializeObject(ex));
            }
            return base.StartAsync(cancellationToken);
        }

        public override Task DoWork(CancellationToken cancellationToken)
        {
            try
            {
                var delayToNextRun = CronHelper.GetDelayToNextRefreshToken(_expression, _timeZoneInfo);
                _logger.LogInformation("Job: Start RenewAccessToken LinkID");
                LoyaltyHelper.RenewAccessTokenLinkIDCacheValue(_cache, delayToNextRun);
            }
            catch (Exception ex)
            {
                _logger.LogError("Job: RenewAccessToken error" + JsonConvert.SerializeObject(ex));
            }

            return Task.CompletedTask;
        }

        public override Task StopAsync(CancellationToken cancellationToken)
        {
            return base.StopAsync(cancellationToken);
        }
    }
}
