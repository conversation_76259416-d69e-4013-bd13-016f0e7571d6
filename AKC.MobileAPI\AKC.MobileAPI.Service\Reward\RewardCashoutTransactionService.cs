﻿using AKC.MobileAPI.DTO.Reward.CashoutTransaction;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardCashoutTransactionService : RewardBaseService, IRewardCashoutTransactionService
    {
        public RewardCashoutTransactionService(IConfiguration configuration) : base(configuration)
        {
        }

        public async Task<RewardCreateCashoutTransactionOutput> CreateCashoutTransaction(RewardCreateCashoutTransactionInput input)
        {
            var request = new RewardCreateCashoutTransactionDto()
            {
                NationalId = input.MemberCode,
                TokenAmount = input.TokenAmount,
                FeeAmount = input.FeeAmount,
                MerchantId = input.MerchantId
            };
            var result = await PostRewardAsync<RewardCreateCashoutTransactionOutputDto>(RewardApiUrl.TOPUP_CASHOUT_CREATE, request);
            return new RewardCreateCashoutTransactionOutput()
            {
                Items = new RewardCreateCashoutTransactionResult()
                {
                    MemberCode = result.Items.NationalId,
                    MemberId = result.Items.MemberId,
                    TokenAmount = result.Items.TokenAmount,
                    MoneyAmount = result.Items.MoneyAmount,
                    FeeAmount = result.Items.FeeAmount,
                    TokenTransactionId = result.Items.TokenTransactionId,
                },
                Message = result.Message,
                Result = result.Result,
            };
        }

        public async Task<RewardRevertCashoutTransactionOutput> RevertCashoutTransaction(RewardRevertCashoutTransactionInput input)
        {
            return await PostRewardAsync<RewardRevertCashoutTransactionOutput>(RewardApiUrl.TOPUP_CASHOUT_REVERT, input);
        }
    }
}
