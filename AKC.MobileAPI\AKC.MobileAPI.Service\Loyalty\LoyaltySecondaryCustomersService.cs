﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.MobileAPI;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltySecondaryCustomersService : BaseLoyaltyService, ILoyaltySecondaryCustomersService
    {
        public LoyaltySecondaryCustomersService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        /// <summary>
        /// Get Profile by code form loyalty.
        /// </summary>
        /// <param name="code">Member ID | (NationalId in Reward)</param>
        /// <returns></returns>
        public async Task<LoyaltyResponse<SecondaryCustomerDto>> GetProfileByCode(string code)
        {
            return await GetLoyaltyAsync<LoyaltyResponse<SecondaryCustomerDto>>(LoyaltyApiUrl.GET_PROFILE_BY_CODE, new { Code = code });
        }
        
        public async Task<LoyaltyResponse<CustomSecondaryCustomerInfoDto>> GetMemberInfoByCode(string code)
        {
            return await GetLoyaltyAsync<LoyaltyResponse<CustomSecondaryCustomerInfoDto>>(LoyaltyApiUrl.GET_MEMBER_INFO_BY_CODE, new { Code = code });
        }

        public async Task<LoyaltyResponse<LoyaltyViewPointWithGrantTypeOutput>> ViewPointWithGrantType(string code, bool simple = false)
        {
            return await GetLoyaltyAsync<LoyaltyResponse<LoyaltyViewPointWithGrantTypeOutput>>(LoyaltyApiUrl.VIEW_POINT_WITH_GRANTTYPE, new { MemberCode = code, SimpleMode = simple });
        }

        public async Task<LoyaltyResponse<LoyaltyVerifyReferralCodeOutputDto>> VerifyReferralCode(LoyaltyVerifyReferralCodeInput input)
        {
            var distributionChannelList = new string[] { };
            if (input.DistributionChannelList != null && input.DistributionChannelList.Length > 0) {
                distributionChannelList = input.DistributionChannelList;
                var duplicateKeys = distributionChannelList.GroupBy(x => x)
                  .Where(g => g.Count() > 1)
                  .Select(y => y.Key)
                  .ToList();
                if (duplicateKeys != null && duplicateKeys.Count > 0)
                {
                    throw new ArgumentException("DistributionChannelList cannot be duplicate data");
                }
            }
            var merchantId = Convert.ToInt32(_configuration.GetSection("Reward" + MerchantNameConfig.VPID + ":MerchantId").Value);
            var request = new LoyaltyVerifyReferralCodeInputDto()
            {
                TenantId = this.tenantId,
                MerchantId = merchantId,
                DistributionChannelList = distributionChannelList,
                MemberCode = input.NationalId,
                ReferenceAmount = input.ReferenceAmount,
                ReferralCode = input.ReferralCode,
            };
            return await PostLoyaltyAsync<LoyaltyResponse<LoyaltyVerifyReferralCodeOutputDto>>(LoyaltyApiUrl.VERIFY_REFERRAL_CODE, request);
        }

        public async Task<LoyaltyMemberCheckReferralCodeExistanceOutput> CheckReferralCodeExistance(LoyaltyMemberCheckReferralCodeExistanceInput input)
        {
            var result = await PostLoyaltyAsync<LoyaltyResponse<LoyaltyMemberCheckReferralCodeExistanceItems>>(LoyaltyApiUrl.CHECK_REFERRAL_CODE_EXISTANCE, input);
            return new LoyaltyMemberCheckReferralCodeExistanceOutput()
            {
                Result = 200,
                Messages = "Succcess",
                Items = result.Result,
                MessagesDetail = null,
            };
        }

        public async Task<LoyaltyResponse<string>> UpdateNotificationSetting(UpdateNotificationSettingInput input)
        {
            return await PutLoyaltyAsync<LoyaltyResponse<string>>(LoyaltyApiUrl.UPDATE_NOTIFICATION_SETTING, input);
        }
        
        public async Task<LoyaltyResponse<string>> UpdatePhoneNumber(UpdatePhoneNumberInput input)
        {
            return await PutLoyaltyAsync<LoyaltyResponse<string>>(LoyaltyApiUrl.MEMBER_UPDATE_PHONE_NUMBER, input);
        }
    }
}
