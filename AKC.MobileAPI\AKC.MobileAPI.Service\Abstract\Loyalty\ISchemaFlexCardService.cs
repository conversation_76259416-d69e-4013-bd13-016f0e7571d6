﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.DTO.Loyalty.SchemaFlexCard;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ISchemaFlexCardService
    {
        Task<GetListSchemaFlexCard> GetAll(string Cif, string CardType, string Language, int SkipCount = 0, int MaxResultCount = 10);
        Task<GetListHistorySchemaFlexCard> GetHistory(string Cif, string CardType, string Language, int SkipCount = 0, int MaxResultCount = 10);
        Task<SchemaFlexCardCreateOrUpdateOutput> Create(SchemaFlexCardCreateOrUpdate input);
        Task<SchemaFlexCardCreateOrUpdateOutput> Update(SchemaFlexCardCreateOrUpdate input);
    }
}
