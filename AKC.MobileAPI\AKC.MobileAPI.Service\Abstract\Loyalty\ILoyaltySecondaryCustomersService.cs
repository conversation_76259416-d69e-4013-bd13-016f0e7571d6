﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.MobileAPI;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltySecondaryCustomersService
    {
        Task<LoyaltyResponse<SecondaryCustomerDto>> GetProfileByCode(string code);
        Task<LoyaltyResponse<CustomSecondaryCustomerInfoDto>> GetMemberInfoByCode(string code);
        Task<LoyaltyResponse<string>> UpdateNotificationSetting(UpdateNotificationSettingInput code);
        Task<LoyaltyResponse<LoyaltyVerifyReferralCodeOutputDto>> VerifyReferralCode(LoyaltyVerifyReferralCodeInput input);
        Task<LoyaltyMemberCheckReferralCodeExistanceOutput> CheckReferralCodeExistance(LoyaltyMemberCheckReferralCodeExistanceInput input);
        Task<LoyaltyResponse<string>> UpdatePhoneNumber(UpdatePhoneNumberInput input);
        Task<LoyaltyResponse<LoyaltyViewPointWithGrantTypeOutput>> ViewPointWithGrantType(string code, bool simple);
    }
}
