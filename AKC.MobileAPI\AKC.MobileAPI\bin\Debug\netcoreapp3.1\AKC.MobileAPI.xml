<?xml version="1.0"?>
<doc>
    <assembly>
        <name>AKC.MobileAPI</name>
    </assembly>
    <members>
        <member name="M:AKC.MobileAPI.Controllers.Reward.RewardMemberController.AdminRemoveMemberConnection(AKC.MobileAPI.DTO.Loyalty.Member.RewardRemoveConnectionInput)">
            <summary>
            Tính năng cho portal 247 gỡ kết nối của member và merchant vpbank
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Reward.RewardMemberController.AutoConnectMember(AKC.MobileAPI.DTO.Reward.Member.RewardMemberAutoConnectMemberInput)">
            API cho bên portal247 gọi sang. Ko liên quan API cho NEO.
            LINKID-185
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Reward.RewardMemberController.AutoConnectMember2(AKC.MobileAPI.DTO.Reward.Member.RewardMemberAutoConnectMemberInput)">
            <PERSON><PERSON><PERSON> bản 2023, cần add cho flow của NEO, LINKID-794
            Xử lý tự động liên kết cho Cif mà có UserMapping.Status = 0
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Reward.RewardMemberController.AutoConnectMember3MasterCard(AKC.MobileAPI.DTO.Reward.Member.RewardMemberAutoConnectMember3Input)">
            Version 2024 - Tich hop voi MasterCard. Có nhận thêm Prefix làm tiền tố memcode, trả về membercode  so với bản V3 
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Reward.RewardMemberController.GetMemberInfoByCif(AKC.MobileAPI.DTO.Reward.Member.GetMemberInfoByCifInput)">
            Truyền CIF vào, sẽ nhận ra basic info của member ở loyalty
            Kèm trạng thái kết nối với LinkID (integer constants)
            Và List lịch sử kết nối
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Sme.SmeController.GetSmeInfo(AKC.MobileAPI.DTO.Sme.GetSmeInfoInput)">
            Trả về thông tin cơ bản của SME. Dữ liệu kết nối của Sme đó với LynkiD SME
            Và trả về count các member của SME này
        </member>
    </members>
</doc>
