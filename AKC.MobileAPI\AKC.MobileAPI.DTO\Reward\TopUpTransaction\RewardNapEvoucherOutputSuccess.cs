﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.TopUpTransaction
{
    public class RewardNapEvoucherOutputSuccess
    {
        public int Result { get; set; }
        public RewardNapEvoucherOutputSuccessChild Items { get; set; }
        public string Message { get; set; }
        public string MessageDetail { get; set; }
    }
    public class RewardNapEvoucherOutputSuccessChild
    {
        public decimal TokenAmount { get; set; }
        public string TokenTransactionId { get; set; }
        public string ExpiryDate { get; set; }
    }
}
