﻿using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.Reward.ExchangeTransaction;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.PartnerPointCaching;
using AKC.MobileAPI.DTO.ThirdParty.VPBank;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Abstract.ThirdParty;
using AKC.MobileAPI.Service.Constants.ThirdParty;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Loyalty;
using AKC.MobileAPI.Service.ThirdParty.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.ThirdParty
{
    public class ThirdPartyVPBankService : BaseThirdPartyVPBankService, IThirdPartyVPBankService
    {
        private readonly IRewardExchangeTransactionService _rewardExchangeTransactionService;
        private readonly ILoyaltyAuditLogService _loyaltyAuditLogService;
        private readonly IRewardMemberService _rewardMemberService;
        private readonly IExceptionReponseService _exceptionReponseService;
        public ThirdPartyVPBankService(
            IConfiguration configuration,
            ILogger<ThirdPartyVPBankService> logger,
            IRewardExchangeTransactionService rewardExchangeTransactionService,
            IRewardMemberService rewardMemberService,
            ILoyaltyAuditLogService loyaltyAuditLogService,
            IExceptionReponseService exceptionReponseService) : base(configuration, logger, loyaltyAuditLogService)
        {
            _rewardExchangeTransactionService = rewardExchangeTransactionService;
            _loyaltyAuditLogService = loyaltyAuditLogService;
            _rewardMemberService = rewardMemberService;
            _exceptionReponseService = exceptionReponseService;
        }

        public async Task<LoyaltyThirdPartyPointExchangeOutput> PointExchange(LoyaltyThirdPartyPointExchangeInput input, HttpContext context, string orderCode = null)
        {
            var vpidTransactionId = string.Empty;
            if (string.IsNullOrWhiteSpace(orderCode))
            {
                 vpidTransactionId = LoyaltyHelper.GenTransactionCode("PointExchange");
            } else
            {
                vpidTransactionId = orderCode;
            }
            var vpbankRequest = new ThirdPartyVPBankPointExchangeInput()
            {
                accessToken = input.AccessToken,
                exchangeAmount = input.ExchangeAmount.ToString(),
                memberCode = input.IdNumber,
                vpidTransactionId = vpidTransactionId,
            };

            if (!string.IsNullOrEmpty(input.NationalId))
                input.MemberCode = input.NationalId;
            var vpbankExchange = await SendPostAsync<ThirdPartyVPBankPointExchangeOutput>(VPBankApiUrl.POINT_EXCHANGE_OPERATION_NAME, vpbankRequest, context, input.MemberCode);
            var requestReward = new RewardCreateExchangeTransactionInput()
            {
                TransactionCode = vpidTransactionId,
                ExchangeAmount = input.ExchangeAmount,
                MemberCode = input.MemberCode,
                MerchantId = input.MerchantId,
                PartnerBindingTxId = vpbankExchange.loyaltyTransactionId,
            };

            try
            {
                var rewardExchange = await _rewardExchangeTransactionService.CreateExchangeTransactionIntegration(requestReward);

                return new LoyaltyThirdPartyPointExchangeOutput()
                {
                    Error = null,
                    Success = true,
                    Result = 200,
                    Items = new LoyaltyThirdPartyPointExchangeResult()
                    {
                        Transaction = new LoyaltyThirdPartyPointExchangeItem()
                        {
                            EquivalentTokenAmount = rewardExchange.Items.EquivalentTokenAmount,
                            ExchangeAmount = input.ExchangeAmount,
                            PartnerBindingTxId = rewardExchange.Items.PartnerBindingTxId,
                        }
                    },
                };
            }
            catch (Exception e)
            {
                var revertPoint = new ThirdPartyVPBankPointRevertPointInput()
                {
                    accessToken = input.AccessToken,
                    loyaltlyTransactionId = vpbankExchange.loyaltyTransactionId,
                    memberCode = input.IdNumber,
                    vpidTransactionId = vpidTransactionId,
                };
                await retryRevertToken(revertPoint, context);
                throw e;
            }
        }

        public async Task<LoyaltyThirdPartyPointViewOutput> PointView(LoyaltyThirdPartyPointViewInput input, HttpContext context)
        {
            var vpbankRequest = new ThirdPartyVPBankPointViewInput()
            {
                accessToken = input.AccessToken,
                memberCode = input.IdNumber,
            };
            var vpbankResult = await SendPostAsync<ThirdPartyVPBankPointViewOutput>(VPBankApiUrl.POINT_VIEW_OPERATION_NAME, vpbankRequest, context, input.MemberCode);
            return new LoyaltyThirdPartyPointViewOutput()
            {
                Success = true,
                Result = new LoyaltyThirdPartyPointViewResult()
                {
                    Member = new LoyaltyThirdPartyPointViewItem()
                    {
                        CoinBalance = decimal.Parse(vpbankResult.PointBalance),
                        MemberCode = vpbankResult.MemberCode,
                    }
                }
            };
        }

        public async Task<LoyaltyThirdPartyVerifyNationalIdOutput> VerifyNationalId(LoyaltyThirdPartyVerifyNationalIdInput input, HttpContext context)
        {
            var vpbankRequest = new ThirdPartyVPBankVerifyNationalIdInput()
            {
                isResendOTP = input.IsResendOTP,
                memberCode = input.IdNumber,
                OTPSession = input.OtpSession,
                phoneNumber = input.PhoneNumber,
            };
            if (!string.IsNullOrEmpty(input.NationalId))
                input.MemberCode = input.NationalId;
            await SendPostAsync<ThirdPartyVPBankVerifyNationalIdOutput>(VPBankApiUrl.VERIFY_NATIONAL_ID_OPERATION_NAME, vpbankRequest, context, input.MemberCode);
            return new LoyaltyThirdPartyVerifyNationalIdOutput()
            {
                Result = null,
                Success = true,
            };
        }

        public async Task<LoyaltyThirdPartyVerifyOTPOutput> VerifyOTP(LoyaltyThirdPartyVerifyOTPInput input, HttpContext context)
        {
            var vpbankRequest = new ThirdPartyVPBankVerifyOTPInput()
            {
                memberCode = input.IdNumber,
                OTPNumber = input.OtpNumber,
                OTPSession = input.OtpSession,
            };
            if (!string.IsNullOrEmpty(input.NationalId))
                input.MemberCode = input.NationalId;

            var vpbankResult = await SendPostAsync<ThirdPartyVPBankVerifyOTPOutput>(VPBankApiUrl.VERIFY_OTP_OPERATION_NAME, vpbankRequest, context, input.MemberCode);
            await _rewardMemberService.SaveRefreshToken(new RewardMemberSaveRefreshTokenInput()
            {
                MemberCode = input.MemberCode,
                MerchantId = input.MerchantId,
                RefreshToken = vpbankResult.RefreshToken,
            });
            var viewPointResult = await PointView(new LoyaltyThirdPartyPointViewInput()
            {
                AccessToken = vpbankResult.AccessToken,
                IdNumber = input.IdNumber,
                MerchantId = input.MerchantId,
                MemberCode = input.MemberCode,
                NationalId = input.NationalId,
            }, context);
            return new LoyaltyThirdPartyVerifyOTPOutput()
            {
                Success = true,
                Result = 200,
                Items = new LoyaltyThirdPartyVerifyOTPResult()
                {
                    AccessToken = vpbankResult.AccessToken,
                    RefreshToken = vpbankResult.RefreshToken,
                    Member = new LoyaltyThirdPartyVerifyOTPItem()
                    {
                        Name = vpbankResult.CustomerInfo.FullName,
                        PartnerBalance = viewPointResult.Result.Member.CoinBalance
                    }
                }
            };
        }

        public async Task<LoyaltyThirdPartyRevertPointOutput> RevertPoint(LoyaltyThirdPartyRevertPointInput input, HttpContext context)
        {
            var revertPoint = new ThirdPartyVPBankPointRevertPointInput()
            {
                accessToken = input.AccessToken,
                loyaltlyTransactionId = input.LoyaltlyTransactionId,
                memberCode = input.MemberCode,
                vpidTransactionId = input.VpidTransactionId,
            };
            await SendPostAsync<ThirdPartyVPBankPointRevertPointOutput>(VPBankApiUrl.REVERT_POINT_OPERATION_NAME, revertPoint, context, input.MemberCode);
            return new LoyaltyThirdPartyRevertPointOutput()
            {
                Error = null,
                Success = true,
                Message = "Success",
            };
        }

        private async Task<ThirdPartyVPBankPointRevertPointOutput> RevertPointInternal(ThirdPartyVPBankPointRevertPointInput input, HttpContext context)
        {
            try
            {
                var vpbankResult = await SendPostAsync<ThirdPartyVPBankPointRevertPointOutput>(VPBankApiUrl.REVERT_POINT_OPERATION_NAME, input, context, input.memberCode);
                return vpbankResult;
            }
            catch
            {
                return null;
            }
        }

        private async Task retryRevertToken(ThirdPartyVPBankPointRevertPointInput input, HttpContext context)
        {
            var retryNum = 3;
            while (retryNum != 0)
            {
                var result = await RevertPointInternal(input, context);
                if (result != null)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        public async Task<LoyaltyThirdPartyRequestAccessTokenOutput> RequestAccessToken(LoyaltyThirdPartyRequestAccessTokenInput input, HttpContext context)
        {
            if (!string.IsNullOrEmpty(input.NationalId))
                input.MemberCode = input.NationalId;
            var resultRefreshToken = await _rewardMemberService.GetRefreshToken(new RewardMemberGetRefreshTokenInput()
            {
                MemberCode = input.MemberCode,
                MerchantId = input.MerchantId
            });
            var vpbankRequest = new ThirdPartyVPBankRequestAccessTokenInput()
            {
                memberCode = resultRefreshToken.Items.IdCard,
                refreshToken = resultRefreshToken.Items.ItemPartner[0].RefreshToken,
            };
            var vpbankResult = await SendPostAsync<ThirdPartyVPBankRequestAccessTokenOutput>(VPBankApiUrl.REQUEST_ACCESS_TOKEN_OPERATION_NAME, vpbankRequest, context, input.MemberCode);
            return new LoyaltyThirdPartyRequestAccessTokenOutput()
            {
                Success = true,
                Result = 200,
                Message = "Success",
                Items = new LoyaltyThirdPartyRequestAccessTokenItems()
                {
                    AccessToken = vpbankResult.AccessToken,
                    IdNumber = resultRefreshToken.Items.IdCard,
                    MemberCode = resultRefreshToken.Items.MemberCode,
                }
            };
        }

        public async Task<RewardPartnerPoingCachingItems> UpdatePartnerCaching(LoyaltyThirdPartyVPBankUpdatePartnerCachingInput input, HttpContext context)
        {
            try
            {
                var request = new LoyaltyThirdPartyPointViewInput()
                {
                    AccessToken = input.AccessToken,
                    IdNumber = input.IdNumber,
                    MemberCode = input.MemberCode,
                };
                var response = await PointView(request, context);
                return new RewardPartnerPoingCachingItems()
                {
                    PointBalance = response.Result.Member.CoinBalance,
                    Status = "S",
                    MerchantId = input.MerchantId
                };
            }
            catch (Exception ex)
            {
                return new RewardPartnerPoingCachingItems()
                {
                    PointBalance = 0,
                    Status = "F",
                    MerchantId = input.MerchantId,
                    HaveException = true,
                    ExceptionCode = _exceptionReponseService.GetExceptionLoyaltyReponse(ex).Result.Code
            };
            }
        }

        public async Task<LoyaltyThirdPartyPointExchangeOutput> PointExchangeIntegration(LoyaltyThirdPartyPointExchangeInput input, HttpContext context, string orderCode = null)
        {
            var vpidTransactionId = string.Empty;
            if (string.IsNullOrWhiteSpace(orderCode))
            {
                vpidTransactionId = LoyaltyHelper.GenTransactionCode("PointExchange");
            }
            else
            {
                vpidTransactionId = orderCode;
            }
            var vpbankRequest = new ThirdPartyVPBankPointExchangeInput()
            {
                accessToken = input.AccessToken,
                exchangeAmount = input.ExchangeAmount.ToString(),
                memberCode = input.IdNumber,
                vpidTransactionId = vpidTransactionId,
            };

            if (!string.IsNullOrEmpty(input.NationalId))
                input.MemberCode = input.NationalId;
            try
            {
                var vpbankExchange = await SendPostAsync<ThirdPartyVPBankPointExchangeOutput>(VPBankApiUrl.POINT_EXCHANGE_OPERATION_NAME, vpbankRequest, context, input.MemberCode);

                return new LoyaltyThirdPartyPointExchangeOutput()
                {
                    Error = null,
                    Success = true,
                    Result = 200,
                    Items = new LoyaltyThirdPartyPointExchangeResult()
                    {
                        Transaction = new LoyaltyThirdPartyPointExchangeItem()
                        {
                            EquivalentTokenAmount = input.ExchangeAmount,
                            ExchangeAmount = input.ExchangeAmount,
                            PartnerBindingTxId = vpbankExchange.loyaltyTransactionId,
                        }
                    },
                };
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
