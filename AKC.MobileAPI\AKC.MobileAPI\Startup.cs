using AKC.MobileAPI.AuditLog;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.LoyaltyVendorGift;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Abstract.ThirdParty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.CronServices;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Loyalty;
using AKC.MobileAPI.Service.LoyaltyVendorGift;
using AKC.MobileAPI.Service.Reward;
using AKC.MobileAPI.Service.ThirdParty;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System;
using System.IO;
using System.Reflection;
using AKC.MobileAPI.Service.Abstract.Sme;
using AKC.MobileAPI.Service.Sme;

namespace AKC.MobileAPI
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
            //var path = Path.Combine(AppContext.BaseDirectory, @"App_Data/adminSDKKey.json");
            //FirebaseApp.Create(new AppOptions()
            //{
            //    Credential = GoogleCredential.FromFile(path),
            //});
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            // Config redis.
            services.AddStackExchangeRedisCache(options =>
            {
                options.InstanceName = Configuration.GetSection("Redis:InstanceName").Value;
                options.Configuration = Configuration.GetSection("Redis:Configuration").Value;
            });
            //services.AddDistributedMemoryCache();
            //var projectId = Configuration.GetSection("Firebase:ProjectId").Value;

            //var configuration = AccessConfigurationService.Instance.GetConfiguration();
            //var key = configuration.GetValue<string>("AppSettings:EncryptionKey");

            services.Configure<ForwardedHeadersOptions>(options =>
            {
                options.ForwardedHeaders =
                    ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto;
            });

            //var keyBytes = Convert.FromBase64String(key); // your key here

            //AsymmetricKeyParameter asymmetricKeyParameter = PublicKeyFactory.CreateKey(keyBytes);
            //RsaKeyParameters rsaKeyParameters = (RsaKeyParameters)asymmetricKeyParameter;
            //RSAParameters rsaParameters = new RSAParameters
            //{
            //    Modulus = rsaKeyParameters.Modulus.ToByteArrayUnsigned(),
            //    Exponent = rsaKeyParameters.Exponent.ToByteArrayUnsigned()
            //};
            //var rsa = new RSACryptoServiceProvider();
            //rsa.ImportParameters(rsaParameters);

            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                         .AddJwtBearer(options =>
                         {
                             options.TokenValidationParameters = new TokenValidationParameters
                             {
                                 ValidateIssuer = false,
                                 ValidateAudience = false,
                                 ValidateLifetime = false,
                                 ValidateIssuerSigningKey = false,
                                 ValidateActor = false,
                                 ValidateTokenReplay = false,

                             };

                             //options.Events = new JwtBearerEvents()
                             //{
                             //    OnMessageReceived = async context =>
                             //    {
                             //        try
                             //        {
                             //            //await FirebaseAuth.DefaultInstance.VerifyIdTokenAsync((context. as JwtSecurityToken).RawData, true);
                             //        }
                             //        catch
                             //        {
                             //            context.NoResult();
                             //            context.Response.Headers.Add("Token-Expired", "true");
                             //            context.Response.ContentType = "application/json";
                             //            context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                             //            await context.Response.WriteAsync("Un-Authorized");
                             //            throw;
                             //        }
                             //    },
                             //};
                         });

            //services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            // .AddJwtBearer(options =>
            // {
            //     options.Authority = $"https://securetoken.google.com/{projectId}";
            //     options.TokenValidationParameters = new TokenValidationParameters
            //     {
            //         ValidateIssuer = true,
            //         ValidIssuer = $"https://securetoken.google.com/{projectId}",
            //         ValidateAudience = true,
            //         ValidAudience = projectId,
            //         ValidateLifetime = true
            //     };
            //     options.Events = new JwtBearerEvents()
            //     {
            //         OnTokenValidated = async context =>
            //         {
            //             try
            //             {
            //                 await FirebaseAuth.DefaultInstance.VerifyIdTokenAsync((context.SecurityToken as JwtSecurityToken).RawData, true);
            //             }
            //             catch
            //             {
            //                 context.NoResult();
            //                 context.Response.Headers.Add("Token-Expired", "true");
            //                 context.Response.ContentType = "application/json";
            //                 context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
            //                 await context.Response.WriteAsync("Un-Authorized");
            //                 throw;
            //             }
            //         }
            //     };
            // });


            services.AddControllers();
            services.AddMemoryCache();

            if (bool.Parse(Configuration.GetSection("EnableSwagger").Value))
            {
                services.ConfigureSwaggerGen(options =>
                {
                    //UseFullTypeNameInSchemaIds replacement for .NET Core
                    options.CustomSchemaIds(x => x.FullName);
                });

                // Register the Swagger generator, defining 1 or more Swagger documents
                services.AddSwaggerGen(c =>
                {
                    c.SwaggerDoc("v1", new OpenApiInfo { Title = "VPOrgAPI", Version = "v1" });

                    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                    {
                        Description = @"JWT Authorization header using the Bearer scheme. \r\n\r\n 
                          Enter 'Bearer' [space] and then your token in the text input below.
                          \r\n\r\nExample: 'Bearer 12345abcdef'",
                        Name = "Authorization",
                        In = ParameterLocation.Header,
                        Type = SecuritySchemeType.ApiKey,
                        Scheme = "Bearer"
                    });

                    // Set the comments path for the Swagger JSON and UI.
                    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                    c.IncludeXmlComments(xmlPath);
                    c.OperationFilter<SwaggerAuthResponsesOperationFilter>();
                });
            }

            // Config Dependency Injection.
            //services.AddScoped<IUserService, UserService>();
            services.AddScoped<IRewardMemberService, RewardMemberService>();
            //services.AddScoped<ILoyaltyUsePointService, LoyaltyUsePointService>();
            services.AddScoped<ILoyaltyOrderService, LoyaltyOrderService>();
            //services.AddScoped<ILoyaltyRewardsService, LoyaltyRewardsService>();
            //services.AddScoped<ILoyaltyRewardsService, LoyaltyRewardsService>();
            //services.AddScoped<ILoyaltyGiftTransactionsService, LoyaltyGiftTransactionsService>();
            //services.AddScoped<ILoyaltyArticleService, LoyaltyArticleService>();
            //services.AddScoped<ILoyaltyGiftService, LoyaltyGiftService>();
            //services.AddScoped<ILoyaltyChallengeService, LoyaltyChallengeService>();
            services.AddScoped<IExceptionReponseService, ExceptionReponseService>();
            services.AddScoped<ILoyaltySecondaryCustomersService, LoyaltySecondaryCustomersService>();
            //services.AddScoped<ILoyaltyFAQService, LoyaltyFAQService>();
            services.AddScoped<ILoyaltyThirdPartyService, LoyaltyThirdPartyService>();
            //services.AddScoped<IRewardMerchantService, RewardMerchantService>();
            services.AddScoped<IRewardExchangeTransactionService, RewardExchangeTransactionService>();
            //services.AddScoped<IRewardGiveTokenService, RewardGiveTokenService>();
            services.AddScoped<IRewardGiftRedeemTransactionService, RewardGiftRedeemTransactionService>();
            //services.AddScoped<IRewardTopUpTransactionService, RewardTopUpTransactionService>();
            //services.AddScoped<IRewardPayByTokenTransactionService, RewardPayByTokenTransactionService>();
            services.AddScoped<IThirdPartyVPBankService, ThirdPartyVPBankService>();
            services.AddScoped<IThirdPartyDummyService, ThirdPartyDummyService>();
            services.AddScoped<IRewardPartnerPointCachingService, RewardPartnerPointCachingService>();
            //services.AddScoped<ILoyaltyNotificationHistoryService, LoyaltyNotificationHistoryService>();
            //services.AddScoped<IUploadImageSevice, UploadImageSevice>();
            //services.AddScoped<IItemManagementService, ItemManagementService>();
            //services.AddScoped<IGameManagementService, GamificationManagementService>();
            //services.AddScoped<IExchangeManagementService, ExchangeManagementService>();
            //services.AddScoped<IMemberManagementService, MemberManagementService>();
            //services.AddScoped<ILoyaltyAdjustService, LoyaltyAdjustService>();
            //services.AddScoped<IApiSMSService, ApiSMSService>();
            services.AddScoped<ILoyaltyAuditLogService, LoyaltyAuditLogService>();
            //services.AddScoped<ILoyaltyEGiftInforsService, LoyaltyEGiftInforsService>();
            //services.AddScoped<ILoyaltyLanguageService, LoyaltyLanguageService>(); 
            //services.AddScoped<ILoyaltyLocationService, LoyaltyLocationService>(); 
            //services.AddScoped<ILoyaltyLanguageService, LoyaltyLanguageService>();
            services.AddScoped<IStorageS3Service, StorageS3Service>();
            //services.AddScoped<IRewardCreateExchangeAndRedeemService, RewardCreateExchangeAndRedeemService>();
            //services.AddScoped<IRewardCashoutTransactionService, RewardCashoutTransactionService>();
            //services.AddScoped<IRewardPaymentFailService, RewardPaymentFailService>();
            services.AddScoped<ILoyaltyMemberService, LoyaltyMemberService>();
            services.AddScoped<ILoyaltyBillPaymentService, LoyaltyBillPaymentService>();
            services.AddScoped<IMerchantGiftService, MerchantGiftService>();
            services.AddScoped<ILinkIdLoyaltyVendorGiftService, LinkIdLoyaltyVendorGiftService>();
            services.AddScoped<ILoyaltyUtilsService, LoyaltyUtilsService>();
            services.AddScoped<ISchemaFlexCardService, SchemaFlexCardService>();
            services.AddScoped<IMasterCardService, MasterCardService>();
            services.AddScoped<IRewardBaseService, RewardBaseService>();
            services.AddScoped<IBaseAdapterService, BaseAdapterService>();
            services.AddScoped<IBusinessLoungeService, BusinessLoungeService>();
            services.AddScoped<ISmeService, SmeService>();
            services.AddScoped<IChallengeMafService, ChallengeMafService>();

            // Rabbit MQ
            //services.AddRabbit(Configuration);

            // Backgroud jobs
            services.AddCronJob<RefreshLoyaltyTokenJob>(c =>
            {
                c.TimeZoneInfo = TimeZoneInfo.Local;
                c.CronExpression = Configuration.GetSection("Loyalty:CronExpressionRefreshToken").Value;
            });

            // Backgroud refresh token LinkID
            services.AddCronJob<RefreshGiftLoyaltyTokenJob>(c =>
            {
                c.TimeZoneInfo = TimeZoneInfo.Local;
                c.CronExpression = Configuration.GetSection("LoyaltyLinkID:CronExpressionRefreshToken").Value;
            });
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILoggerFactory loggerFactory)
        {
            loggerFactory.AddLog4Net();

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            if (bool.Parse(Configuration.GetSection("EnableSwagger").Value))
            {
                // Enable middleware to serve generated Swagger as a JSON endpoint.
                app.UseSwagger();

                // Enable middleware to serve swagger-ui (HTML, JS, CSS, etc.),
                // specifying the Swagger JSON endpoint.
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Mobile API V1");
                    c.RoutePrefix = string.Empty;
                });
            }

            app.UseHttpsRedirection();

            app.UseRouting();


            app.AddValidateRS256TokenMiddleware();

            app.UseAuthentication();
            app.UseAuthorization();

            app.UseMiddleware<RequestLoginMiddleware>();
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}
