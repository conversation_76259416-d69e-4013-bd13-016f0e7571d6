﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class LoyaltyGetWishlistByMemberInput
    {
        [Required]
        public string MemberCode { get; set; }

        [Range(0, 100)]
        public int MaxRelatedItem { get; set; } = 1;

        public int SkipCount { get; set; }

        public int MaxResultCount { get; set; } = int.MaxValue;
    }
}
