﻿using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using AKC.MobileAPI.Jwt;

namespace JwtManager
{
    public class RsJwt
    {
        #region SigleTon
        private RsJwt() {  }

        private static RsJwt instance;

        public static RsJwt Instance { 
            get 
            {
                if (instance == null)
                {
                    instance = new RsJwt();
                    instance.KeySize = Helpers.KeySize.S256;
                }

                return instance;
            }
        }

        #endregion

        private Helpers.KeySize KeySize { get; set; }

        private string AlgorithmName
        {
            get
            {
                return "SHA" + (int)KeySize;
            }
        }

        public void Validate(string token, string key, string keyValue)
        {
            string[] parts = token.Split('.');
            string header = parts[0];
            string payload = parts[1];
            string signature = parts[2];
            try
            {
                if (AES.Decrypt(signature, key) != keyValue)
                {
                    throw new Exception("Invalid signature.");
                }
            }
            catch (Exception)
            {
                throw new Exception("Invalid signature.");
            }
        }
    }
}
