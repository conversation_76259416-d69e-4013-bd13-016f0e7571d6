﻿using System.Collections.Generic;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class GetListConnectedMerchantSimplifiedByMemberIdInput
    {
        public int MemberId { get; set; }
    }

    public class GetListConnectedMerchantSimplifiedByMemberIdOutput
    {
        public int MemberId { get; set; }
        public List<GetListConnectedMerchantSimplifiedByMemberIdOutputIn> Items { get; set; }
    }

    public class GetListConnectedMerchantSimplifiedByMemberIdOutputIn
    {
        public string ConnectionStatus { get; set; }
        public int MerchantId { get; set; }
    }
    
}