﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.BillPayment;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyBillPaymentService : BaseLoyaltyService, ILoyaltyBillPaymentService
    {
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly int _merchantId;
        public LoyaltyBillPaymentService(
            IConfiguration configuration,
            IDistributedCache cache,
            IExceptionReponseService exceptionReponseService
            ) : base(configuration, cache)
        {
            _exceptionReponseService = exceptionReponseService;
            _merchantId = Convert.ToInt32(_configuration.GetSection("LoyaltyLinkID:MerchantIdRedeem").Value);
        }

        public async Task<LoyaltyResponse<BillPaymentCheckBalanceOutput>> CheckBalance(BillPaymentCheckBalanceInput input)
        {
            try
            {
                var dto = new BillPaymentCheckBalanceInputDto()
                {
                    CifCode = input.CifCode,
                    MerchantId = _merchantId,
                    RequestAmount = input.RequestAmount,
                };
                return await PostLoyaltyAsync<LoyaltyResponse<BillPaymentCheckBalanceOutput>>(LoyaltyApiUrl.BILL_PAYMENT_CHECK_BALANCE, dto);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                if (res.Code == "-1")
                {
                    CommonHelper.GetErrorValidation(res.Message, GetMessageFromCode(res.Message));
                }
                throw ex;
            }
        }

        public async Task<LoyaltyResponse<string>> ComfirmUsingToken(BillPaymentConfirmUsingTokenInput input)
        {
            try
            {
                var dto = new BillPaymentConfirmUsingTokenInputDto()
                {
                    CifCode = input.CifCode,
                    MerchantId = _merchantId,
                    OrderCode = input.OrderCode,
                    TokenAmount = input.TokenAmount,
                };
                return await PostLoyaltyAsync<LoyaltyResponse<string>>(LoyaltyApiUrl.BILL_PAYMENT_CONFIRM_USING_TOKEN, dto);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                if (res.Code == "-1")
                {
                    CommonHelper.GetErrorValidation(res.Message, GetMessageFromCode(res.Message));
                }
                throw ex;
            }
        }

        public async Task<LoyaltyResponse<string>> RevertToken(BillPaymentRevertTokenInput input)
        {
            try
            {
                var request = new BillPaymentRevertTokenDto()
                {
                    MerchantId = _merchantId,
                    CifCode = input.CifCode,
                    OrginalOrderCode = input.OriginalOrderCode,
                    RevertTransactionCode = input.RevertTransactionCode,
                };
                return await PostLoyaltyAsync<LoyaltyResponse<string>>(LoyaltyApiUrl.BILL_PAYMENT_REVERT_TOKEN, request);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                if (res.Code == "-1")
                {
                    CommonHelper.GetErrorValidation(res.Message, GetMessageFromCode(res.Message));
                }
                throw ex;
            }
        }

        private string GetMessageFromCode(string code)
        {
            switch (code)
            {
                case "MemberNotEnoughToken":
                    return "Member not enough token";
                case "MemberNotExist":
                    return "LinkID member not exist";
                case "OriginalOrderNotExist":
                    return "Original order code not exist";
                case "OriginalOrderHasBeenReverted":
                    return "Original order code has been reverted";
                case "DuplicateOrderCode":
                    return "Duplicate order code";
                case "DuplicateRevertCode":
                    return "Duplicate revert code";
                default:
                    return "System error";
            }
        }
    }
}
