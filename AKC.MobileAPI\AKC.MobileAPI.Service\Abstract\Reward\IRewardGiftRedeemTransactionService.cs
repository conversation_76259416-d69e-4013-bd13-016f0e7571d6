﻿using AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction;
using AKC.MobileAPI.DTO.Reward.TopUpTransaction;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Reward
{
    public interface IRewardGiftRedeemTransactionService
    {
        Task<RewardCreateGiftRedeemTransactionResponse> CreateRedeem(RewardCreateGiftRedeemTransactionRequest request);
        Task<RewardRevertGiftRedeemTransactionResponse> RevertRedeem(RewardRevertGiftRedeemTransactionRequest request);
        Task<RewardNapEvoucherOutputSuccess> CreateTransForTopupVoucher(RewardNapEvoucherInput request);
    }
}
