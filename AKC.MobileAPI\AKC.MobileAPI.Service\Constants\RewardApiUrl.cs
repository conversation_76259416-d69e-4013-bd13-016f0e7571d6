﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.Service.Constants
{
    public class RewardApiUrl
    {
        public const string MEMBER_VIEW = "Integration/Member/View";
        public const string MEMBER_UPDATE = "Integration/Member/Update";
        public const string MEMBER_VIEWPOINT = "Integration/Member/ViewPoint";
        public const string MEMBER_CREATE = "Integration/Member/Create";
        public const string MEMBER_VERIFY_REFERRAL_CODE = "Integration/Member/VerifyReferralCode";
        public const string MEMBER_GET_TEMP_POINT_BY_ID = "Integration/TempPointTrans/GetByMemberID";
        public const string MEMBER_GET_TOKEN_TRANS_BY_ID = "Integration/TokenTrans/GetByMemberID";
        public const string GET_TOKEN_TRANS_SMECIF = "Integration/TokenTrans/GetBySmeCif";
        public const string FINDONE_TOKEN_TRANS_SMECIF = "Integration/TokenTrans/FindOneBySmeCif";
        public const string MERCHANT_EXCHANGE_GET_ALL = "Integration/Merchant/GetAll";
        public const string EXCHANGE_TRANSACTION_INTEGRATION_CREATE = "Integration/ExchangeIntergrationTransaction/Create";
        public const string EXCHANGE_TRANSACTION_INTEGRATION_REVERT = "Integration/ExchangeIntergrationTransaction/Revert";
        public const string EXCHANGE_TRANSACTION_INTEGRATION_CHECK_PHONE_NUMBER = "Integration/ExchangeIntergrationTransaction/CheckPhoneNumber";
        public const string MEMBER_GET_INFO = "Integration/Member/GetInfo";
        public const string MEMBER_ADD_POINT_USING_ORDINARY = "Integration/Member/AddPointUsingOrdinary";
        public const string MEMBER_GET_LIST_MERCHANT_CONNECTED = "Integration/Member/GetListExchangeMerchantConnected";
        public const string GIVE_TOKEN_USER_VERIFYING = "Integration/GiveToken/UserVerifying";
        public const string GIVE_TOKEN_TRANSFER_TRANSACTION = "Integration/GiveToken/TransferTransaction";
        public const string EXCHANGE_VERIFITION_OTP = "Integration/Verification/ExchangeVerifyOTP";
        public const string MEMBER_GET_TOKEN_BALANCE = "Integration/Member/GetBalanceByNationalId";
        public const string REDEEM_GIFT_TRANSACTION_CREATE = "Integration/RedeemTransaction/Create";
        public const string REDEEM_GIFT_TRANSACTION_REVERT = "Integration/RedeemTransaction/Return";
        public const string SME_REDEEM_GIFT_TRANSACTION_CREATE = "Integration/SmeRedeemTransaction/Create";
        public const string SME_REDEEM_GIFT_TRANSACTION_REVERT = "Integration/SmeRedeemTransaction/Return";
        public const string TOPUP_TRANSACTION_CREATE = "Integration/TopUpTransaction/Create";
        public const string PAY_BY_TOKEN_TRANSACTION_CREATE = "Integration/PayByTokenTransaction/Create";
        public const string PARTNER_POINT_CACHING_REQUEST_UPDATE = "Integration/PartnerPointCaching/RequestUpdateIntegration";
        public const string GET_ALL_MERCHANT = "Integration/Merchant/GetAllWithStore";
        public const string GET_ALL_MERCHANT_PAYMENT = "Integration/Merchant/GetAllMerchantPayment";
        public const string MEMBER_VERIFY_IDCARD = "Integration/Member/IsIdCardVerified";
        public const string MEMBER_VERIFY_PINCODE = "Integration/Member/VerifyPinCode";
        public const string MEMBER_HAS_PINCODE = "Integration/Member/HasPinCode";
        public const string MEMBER_CREATE_OR_UPDATE_PINCODE = "Integration/Member/CreateOrUpdatePinCode";
		public const string VERIFY_PROVIDER_ID_BY_PHONE_NUMBER = "Integration/Member/VerifyProviderIdByPhoneNumber";
        public const string UPDATE_PROVIDER = "Integration/Member/UpdateProvider";
        public const string MEMBER_VERIFY_OR_CREATE = "Integration/Member/VerifyOrCreate";
        public const string MEMBER_GET_REFRESH_TOKEN = "Integration/Member/GetRefreshToken";
        public const string MEMBER_SAVE_REFRESH_TOKEN = "Integration/Member/SaveRefreshToken";
        public const string MEMBER_VERIFY_AND_UPDATE_PROVIDER = "Integration/Member/VerifyCreateMemberAndUpdateProvider";
        public const string MEMBER_CREATE_REGISTER_LOG = "Integration/Member/CreateRegisterLogs";
        public const string MEMBER_UPDATE_PHONE_NUMBER = "Integration/Member/UpdatePhoneNumber";
        public const string MEMBER_ACCOUNT_HAVE_PHONE_NUMBER = "Integration/Member/AccountHavePhoneNumber";
        public const string MEMBER_FIRST_ACTION_MEMBER = "Integration/CommonFunction/FirstActionMember";
        public const string MEMBER_GET_LOGIN_BY_FIREBASE_ID = "Integration/Member/GetMemberLoginByFirebaseId";
        public const string MEMBER_GET_USAGE_PRIORITY = "Integration/Member/GetUsagePriority";
        public const string MEMBER_UPDATE_USAGE_PRIORITY = "Integration/Member/UpdateUsagePriority";
        public const string MEMBER_UPDATE_POINT_USAGE_TYPE = "Integration/Member/UpdatePointUsageType";
        public const string TOPUP_CASHOUT_CREATE = "Integration/IncentiveCashOut";
        public const string MEMBER_GET_CASHOUT_AND_TOPUP_INFO = "Integration/Member/GetCashoutAndTopupInfo";
        public const string TOPUP_CASHOUT_REVERT = "Integration/IncentiveCashOut/Revert";
        public const string PAYMENT_FAIL_CREATE = "Integration/PaymentFail/Create";
        public const string MEMBER_SEND_OTP_CONNECT = "Integration/Member/SendOtpForConnectMerchant";
        public const string MEMBER_VERIFY_OTP_CONNECT = "Integration/Member/VerifyOtpForConnectMerchant";
        public const string SaveRefreshTokenV2 = "Integration/Member/SaveRefreshTokenV2";
        public const string MEMBER_CONFIRM_CONNECT = "Integration/Member/ConfirmConnectMerchantByPhone";
        public const string MEMBER_VIEW_CREDIT_BALANCE_BYWALLETADDRESS = "Integration/Member/GetCreditBalanceByWalletAddress";
        public const string MEMBER_REMOVE_CONNECTION = "Integration/Member/RemoveConnection";

        //Vpbank-merchant
        public const string VIEW_POINT_WITH_GRANTTYPE = "Integration/Member/ViewPointWithGrantType";
        public const string ViewBalanceWithExpiringCoins = "Integration/Member/ViewBalanceWithExpiringCoins";
        public const string GET_WALLET_ADDRESS = "Integration/Member/GetWalletAddress";

        public const string GET_MEMBER_INFO = "Integration/Member/GetInfoWithCode";
        public const string MEMBER_REDEEM_TRANSACTION = "Integration/Member/CreateRedeemTransaction";
        public const string MEMBER_REVERT_TRANSACTION = "Integration/Member/RevertRedeemTransaction";
        public const string SEND_OTP_CONFIRM = "Integration/SmsProvider/SendOtpConfirm";
        public const string VERIFY_OTP_CONFIRM = "Integration/SmsProvider/VerifyOtpConfirm";
        public const string VERIFY_CREATE_REDDEM = "Integration/Member/VerifyRedeemTransaction";
        public const string VERIFY_EXPIRED_OTP = "Integration/SmsProvider/VerifyOtpExpired";
        public const string GET_CIF_BY_PHONE_NUMBER = "Integration/Member/GetCifByPhoneNumber";
        public const string SEND_OTP_MERCHANT_VIEW = "Integration/SmsProvider/sendOtpForConnectMerchantView";
        public const string VERIFY_OTP_MERCHANT_VIEW = "Integration/SmsProvider/verifyOtpForConnectMerchantView";
        public const string CreateUnconfirmedConnection = "Integration/Member/SaveRefreshTokenV3";
        public const string GetListConnectedMerchantSimplifiedByMemberId = "Integration/GetListConnectedMerchantSimplifiedByMemberId";
        public const string GetMemberCodeByPhoneNumber = "Integration/Member/GetMemberCodeByPhoneNumber";
        public const string CheckExistenceAndConnectionWithMerchant = "Integration/Member/CheckExistenceAndConnectionWithMerchant";
        public const string RewardSendAckAfterConnected = "Integration/RewardSendAckAfterConnected";
        //Master card
        public const string MASTER_CARD_GET_CARD_LIST = "Integration/CardTransaction/GetAll";
        public const string MASTER_CARD_REDEEM_CARD = "Integration/CardTransaction/Redeem";
        public const string MASTER_CARD_RETURN_CARD = "Integration/CardTransaction/Return";
        public const string CHALLENGEMAF_CARD_CREATE = "Integration/CardTransaction/Create";
        public const string CHALLENGEMAF_CARD_REVERT = "Integration/CardTransaction/Revert";
        public const string SME_CREATECONNECTION = "Integration/Member/CreateSmeConnection";
        public const string SME_ACK_CONNECTION = "Integration/Member/SendAckConnectSme";
        public const string SME_VIEWPOINT = "Integration/Sme/ViewPoint";

        public const string MEMBER_TOPUP_EVOUCHER_CREATE_TRANS = "Integration/TopUpTransactionLinkId/Create";

        public const string OFFCHAIN_GET_BALANCE_BY_WALLET = "Integration/Member/ViewBalanceByWalletAddress";
    }

    public class MerchantNameConfig
    {
        public const string VPID = "VPID";
        public const string VPBank = "VPBank";
    }
}
