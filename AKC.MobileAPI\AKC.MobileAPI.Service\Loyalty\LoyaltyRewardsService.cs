﻿using AKC.MobileAPI.DTO.Loyalty.Order;
using AKC.MobileAPI.DTO.Loyalty.Rewards;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyRewardsService : BaseLoyaltyService, ILoyaltyRewardsService
    {
        public LoyaltyRewardsService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<LoyaltyInputActionDtoOutput> InputAction(LoyaltyInputActionInput input)
        {
           return await PostLoyaltyAsync<LoyaltyInputActionDtoOutput>(LoyaltyApiUrl.REWARDS_INPUTACTION, input);
        }
    }
}
