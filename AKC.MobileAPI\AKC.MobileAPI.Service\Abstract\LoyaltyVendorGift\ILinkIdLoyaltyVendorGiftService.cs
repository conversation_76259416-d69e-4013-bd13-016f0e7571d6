﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Loyalty.LocationManagement;
using AKC.MobileAPI.DTO.Loyalty.Transaction;
using AKC.MobileAPI.DTO.MerchantGift;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.TopUpTransaction;
using AKC.MobileAPI.DTO.Sme;
using Microsoft.AspNetCore.Http;
using System;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.LoyaltyVendorGift
{
    public interface ILinkIdLoyaltyVendorGiftService
    {
        Task<LoyaltyResponseList<MerchantGiftLocationDto>> GetAllLocation(MerchantGiftLocationInputDto input);
        Task<MerchantGiftGetAllCategoryOutputDto> GetAllCategory(MerchantGiftGetAllCategoryInputDto input);
        Task<MerchantGiftGetAllCategoryOutputDto> GetAllCategory_v1(MerchantGiftGetAllCategoryInputDto input);
        Task<GetAllCategoryWithoutMemberCodeOutput> GetAllCategoryWithoutMemberCode(GetAllCategoryWithoutMemberCodeInput input);
        Task<LoyaltyResponseList<MerchantGiftGetAllGiftOutputDto>> GiftList(MerchantGiftGetAllGiftInputDto input);
        Task<MerchantGiftGetDetailOutputDto> GiftDetail(MerchantGiftGetDetailInputDto input);
        Task<MerchantGiftTransactionHistoryOutputDto> TransactionDetail(MerchantGiftTransactionDetailInputDto input);
        Task<MerchantGiftTransactionHistoryOutputDto> TransactionHistory(MerchantGiftTransactionDetailInputDto input);
        Task<TransactionHistorySmeOutput> TransactionHistorySme(TransactionHistorySmeInput input);
        Task<TransactionHistorySmeOutput> TransactionHistorySmeDetail(TransactionHistorySmeInput input);
        Task<MerchantGiftCreateRedeemOutputDto> CreateRedeem(int memberId, MerchantGiftCreateRedeemInputDto input);
        Task<GetAllLocationManagementDto> GetAllLocationShip(GetAllLocationInput input);
        Task<ViewLocationByIdsOutput> ViewLocationShipByIds(ViewLocationByIds input);
        Task<LoyaltyResponse<MerchantGiftVerifyCreateRedeemOutput>> VerifyCreateRedeem(MerchantGiftVerifyCreateRedeemInputDto input);
        Task<LoyaltyResponse<MerchantGiftGetCreateRedeemOutputDto>> GetCreateRedeemCache(MerchantGiftGetCreateRedeemInputDto input);
        Task<GetMemberCodeOutput> UserVerifying(GetMemberCodeInput input);
        Task<GiftTransferOutput> GiftTransfer(UpdateGiftTransactionsForTransferInput input);
        Task<LoyaltyResponseList<MerchantGiftGetAllGiftOutputDto>> GiftListWithoutMemberCode(MerchantGetGiftWithoutMemberCodeInput input);
        Task<MerchantGiftGetDetailOutputDto> GiftDetailWithoutMemberCode(MerchantGiftGetDetailWithoutMemberCodeInputDto input);
        Task<T> PostLoyaltyAsync<T>(string apiURL, object body = null, HttpContext request = null);
        Task UpdateErrorWhenCreateRedeem(UpdateErrorWhenCreateRedeemPayment input, bool successPaymentToken);
        Task retryRevertToken(RewardMemberRevertRedeemInput request, string url);
        Task GetErrorFromExeption(Exception ex, string transactionCode, bool successPaymentToken);
        Task<MerchantGiftTransactionHistoryOutputDto> TransactionHistoryForCardZone247(MerchantGiftTransactionDetailInputDto input);
        Task<RedeemVoucherOutput> UseEVoucherTopup(UseEVoucherTopupInput input);
        Task<RevertVoucherOutput> RevertEVoucherCode(RevertEVoucherInput input);
        Task<AdminCreateRedeemTransactionResponse> AdminCreateRedeemTransaction(AdminCreateRedeemTransactionRequest input);
        Task<CheckVoucherOutput> CheckEVoucherTopup(CheckVoucherInput input);
        Task<LoyaltyGiftGetMerchantIdFromGiftCodeOutput> GetMerchantIdFromGiftCode(LoyaltyGiftGetMerchantIdFromGiftCodeInput input);
        Task<AdminCreateRedeemTransactionResponse> AdminGiveGiftToMember(AdminCreateRedeemTransactionRequest input);
        Task<RedeemTransactionWithCashResponse> CreateRedeemTransactionWithCash(RedeemTransactionWithCashRequest request);
    }
}
