﻿using AKC.MobileAPI.DTO.Loyalty.AuditLog;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyAuditLogService : BaseLoyaltyService, ILoyaltyAuditLogService
    {
        public LoyaltyAuditLogService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }
        public async Task<CreateOperationLogOutput> CreateOperationLog(CreateOperationLogDto input)
        {
            return await PostLoyaltyAsync<CreateOperationLogOutput>(LoyaltyApiUrl.CREATE_OPERATION_LOG, input);
        }
    }
}

