﻿using AKC.MobileAPI.DTO.Loyalty.Transaction;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/Transaction")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class TransactionController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyUtilsService _transactionService;
        private readonly IExceptionReponseService _exceptionReponseService;
        public TransactionController(
            ILogger<TransactionController> logger,
            ILoyaltyUtilsService transactionService,
            IExceptionReponseService exceptionReponseService)
        {
            _logger = logger;
            _transactionService = transactionService;
            _exceptionReponseService = exceptionReponseService;
        }

        [HttpPost]
        [Route("GetAllBirthDayTransaction")]
        public async Task<ActionResult<GetAllBirthDayTransactionOutput>> GetAllBirthDayTransaction(GetAllBirthDayTransactionInput input)
        {
            try
            {
                _logger.LogInformation($">> GetAllBirthDayTransaction >> Input >> {JsonConvert.SerializeObject(input)}");
                var result = await _transactionService.GetAllBirthDayTransaction(input);
                _logger.LogInformation($">> GetAllBirthDayTransaction >> Output >> {JsonConvert.SerializeObject(result)}");
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetAllBirthDayTransaction Error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionFlexCardSchemaReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }
    }
}
