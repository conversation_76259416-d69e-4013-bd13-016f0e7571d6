﻿using AKC.MobileAPI.DTO.Loyalty.ChallengeMaf;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.CronServices;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Loyalty;
using Cronos;
using FirebaseAdmin.Messaging;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.LoyaltyVendorGift.LinkID
{
    public class BaseLinkIdLoyaltyVendorGiftService
    {
        protected readonly HttpClient _client = new HttpClient();
        protected readonly IConfiguration _configuration;
        protected readonly string baseURL;
        protected readonly int tenantId;
        protected readonly string defaultUserName;
        protected readonly string defaultPassowrd;
        protected readonly IDistributedCache _cache;
        private readonly ILogger<BaseLinkIdLoyaltyVendorGiftService> _logger;

        public BaseLinkIdLoyaltyVendorGiftService(
            IConfiguration configuration,
            IDistributedCache cache,
            ILogger<BaseLinkIdLoyaltyVendorGiftService> logger
            )
        {
            _client.Timeout = TimeSpan.FromSeconds(300);
            _configuration = configuration;
            baseURL = _configuration.GetSection("LoyaltyLinkID:RemoteURL").Value;
            tenantId = Convert.ToInt32(_configuration.GetSection("LoyaltyLinkID:TenantId").Value);
            defaultUserName = _configuration.GetSection("LoyaltyLinkID:Username").Value;
            defaultPassowrd = _configuration.GetSection("LoyaltyLinkID:Password").Value;
            _cache = cache;
            _logger = logger;
        }

        /// <summary>
        /// Perform a GET request to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> GetLoyaltyAsync<T>(string apiURL, object query = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var requestURL = $"{baseURL}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Get
            };

            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            var token = GetAccessToken();
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            req.RequestUri = new Uri(requestURL);
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    _logger.LogError($"Call API {apiURL} with error response {rawData}");
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();


                // Convert response to result object which is a instance of 'T'.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }

        /// <summary>
        /// Perform a DELETE obj to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> DeleteLoyaltyAsync<T>(string apiURL, object query = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var requestURL = $"{baseURL}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Delete
            };

            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            var token = GetAccessToken();
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            req.RequestUri = new Uri(requestURL);
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    _logger.LogError($"Call API {apiURL} with error response {rawData}");
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();


                // Convert response to result object which is a instance of 'T'.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }

        /// <summary>
        /// Convert a object to query string format.
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public string GetQueryString(object obj)
        {
            // Get all properties on the object
            var properties = obj.GetType().GetProperties()
                .Where(x => x.CanRead)
                .Where(x => x.GetValue(obj, null) != null)
                .Select(x => new { x.Name, Value = x.GetValue(obj, null) })
                .ToList();

            // Get names for all IEnumerable properties (excl. string)
            var propertyNames = properties
                .Where(x => !(x.Value is string) && x.Value is IEnumerable)
                .Select(x => x.Name)
                .ToList();

            // Concat all IEnumerable properties into a comma separated string
            foreach (var key in propertyNames)
            {
                var objectOfKey = properties.FirstOrDefault(x => x.Name == key);
                var valueType = objectOfKey.Value.GetType();
                var valueElemType = valueType.IsGenericType
                                        ? valueType.GetGenericArguments()[0]
                                        : valueType.GetElementType();
                if (valueElemType.IsPrimitive || valueElemType == typeof(string))
                {
                    var enumerable = objectOfKey.Value as IEnumerable;

                    properties.Remove(objectOfKey);

                    foreach (var item in enumerable)
                    {
                        properties.Add(new { Name = key, Value = item });
                    }
                }
            }

            // Concat all key/value pairs into a string separated by ampersand
            return string.Join("&", properties
                .Select(x => string.Concat(
                    Uri.EscapeDataString(x.Name), "=",
                    Uri.EscapeDataString(x.Value.ToString()))));
        }

        public async Task<T> PostLoyaltyAsync<T>(string apiURL, object body = null, HttpContext request = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }
            var Client_Request_Address = request != null && request.Request.Headers.ContainsKey("Client-Request-Address") ?
                                            request?.Request.Headers["Client-Request-Address"].ToString() : request?.Connection.RemoteIpAddress.ToString();
            req.Headers.Add("Client-Request-Address", Client_Request_Address);
            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", GetAccessToken());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized
                //Recall API when GatewayTimeout
                if (response.StatusCode == HttpStatusCode.GatewayTimeout)
                {
                    throw new TimeoutException("Request loyalty time out!");
                }
                //End Recall API when GatewayTimeout
                if (response.IsSuccessStatusCode == false)
                {
                    _logger.LogError($"Call API {apiURL} with error response {rawData}");
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();

                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);

                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<T> PutLoyaltyAsync<T>(string apiURL, object body = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Put
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }

            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", GetAccessToken());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();

                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);

                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }

        /// <summary>
        /// Get a new accessToken form Loyalty.
        /// </summary>
        /// <returns></returns>
        private string GetAccessToken(bool mustResetCache = false)
        {
            try
            {
                var token = _cache.GetString(CommonConstants.ACCESSS_TOKEN_CACHE_KEY_LINKID);
                var configuration = AccessConfigurationService.Instance.GetConfiguration();
                var expression = CronExpression.Parse(configuration.GetSection("LoyaltyLinkID:CronExpressionRefreshToken").Value);
                var timeZoneInfo = TimeZoneInfo.Local;

                // Request body
                if (string.IsNullOrEmpty(token) || mustResetCache)
                {
                    return LoyaltyHelper.RenewAccessTokenLinkIDCacheValue(_cache, CronHelper.GetDelayToNextRefreshToken(expression, timeZoneInfo), mustResetCache);
                }
                return token;
            }
            catch (Exception e)
            {
                var ex = new LoyaltyException();
                var error = new LynkiDResponseExp()
                {
                    success = false,
                    error = new ErrorExpObject()
                    {
                        code = "3012",
                        message = e.Message
                    }
                };
                ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
                throw ex;
            }
        }

        //Clone a HttpRequest
        private HttpRequestMessage CloneHttpRequest(HttpRequestMessage req)
        {
            HttpRequestMessage clone = new HttpRequestMessage(req.Method, req.RequestUri);

            clone.Content = req.Content;
            clone.Version = req.Version;

            foreach (KeyValuePair<string, object> prop in req.Properties)
            {
                clone.Properties.Add(prop);
            }

            foreach (KeyValuePair<string, IEnumerable<string>> header in req.Headers)
            {
                clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            return clone;
        }

        public async Task<T> PostLoyaltyAsyncV2<T>(string apiURL, object body = null, HttpContext request = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }
            var Client_Request_Address = request != null && request.Request.Headers.ContainsKey("Client-Request-Address") ?
                                            request?.Request.Headers["Client-Request-Address"].ToString() : request?.Connection.RemoteIpAddress.ToString();
            req.Headers.Add("Client-Request-Address", Client_Request_Address);
            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", GetAccessToken());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");

            var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
            try
            {
                var response = await _client.SendAsync(req, cts.Token);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone, cts.Token);
                    rawData = await response.Content.ReadAsStringAsync();

                    // If still unauthorized after retry, throw specific exception
                    if (response.StatusCode == HttpStatusCode.Unauthorized)
                    {
                        _logger.LogError($"API {apiURL} returned Unauthorized after token refresh: {rawData}");
                        throw new LoyaltyUnauthorizedException(apiURL, rawData);
                    }
                }
                //End Recall API when Unauthorized

                //Handle Gateway Timeout
                if (response.StatusCode == HttpStatusCode.GatewayTimeout)
                {
                    _logger.LogError($"API {apiURL} returned Gateway Timeout: {rawData}");
                    throw new LoyaltyGatewayTimeoutException(apiURL);
                }
                //End Handle Gateway Timeout

                // Handle other HTTP error status codes
                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError($"API {apiURL} returned {(int)response.StatusCode} {response.StatusCode}: {rawData}");

                    switch (response.StatusCode)
                    {
                        case HttpStatusCode.BadRequest:
                            throw new LoyaltyBadRequestException(apiURL, rawData);
                        case HttpStatusCode.Unauthorized:
                            throw new LoyaltyUnauthorizedException(apiURL, rawData);
                        case HttpStatusCode.GatewayTimeout:
                            throw new LoyaltyGatewayTimeoutException(apiURL);
                        case HttpStatusCode.InternalServerError:
                            var ex = new LoyaltyException();
                            ex.Data.Add("ErrorData", rawData);
                            throw ex;
                        default:
                            throw new LoyaltyHttpException(response.StatusCode, apiURL, rawData);
                    }
                }

                // Get response result
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (OperationCanceledException ex) when (cts.Token.IsCancellationRequested)
            {
                _logger.LogError(ex, $"API {apiURL} request was cancelled");
                throw new LoyaltyCancelledException(apiURL, ex);
            }
            catch (TaskCanceledException ex)
            {
                // Check if it's a timeout (not explicitly cancelled)
                if (ex.CancellationToken == cts.Token)
                {
                    _logger.LogError(ex, $"API {apiURL} request timed out after 30 seconds");
                    throw new LoyaltyTimeoutException(apiURL, ex, TimeSpan.FromSeconds(30));
                }
                else
                {
                    _logger.LogError(ex, $"API {apiURL} request was cancelled");
                    throw new LoyaltyCancelledException(apiURL, ex);
                }
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, $"Network error occurred while calling API {apiURL}");
                throw new LoyaltyNetworkException(apiURL, ex);
            }
            catch (WebException ex)
            {
                _logger.LogError(ex, $"Web exception occurred while calling API {apiURL}");
                throw new LoyaltyNetworkException(apiURL, ex);
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, $"JSON deserialization error for API {apiURL}");
                throw new LoyaltyException($"Failed to deserialize response from API {apiURL}: {ex.Message}", ex);
            }
            catch (LoyaltyException ex)
            {
                throw ex;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Unexpected error occurred while calling API {apiURL}");
                throw new Exception($"Unexpected error occurred while calling API {apiURL}: {ex.Message}", ex);
            }
        }
    }
}
