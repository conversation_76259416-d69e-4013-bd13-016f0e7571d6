﻿using System;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class ViewSmePointRequest
    {
        public string SmeCode { get; set; }
        public int? Id { get; set; }
    }

    public class ViewSmePointResponse
    {
        public int Result { get; set; }
        public string MessageDetail { get; set; }
        public string Message { get; set; }
        public ViewSmePointResponseItem items { get; set; }
    }

    public class ViewSmePointResponseItem
    {
        public int Id { get; set; }
        public decimal? ExpiringTokenAmount { get; set; }
        public int TokenBalance { get; set; }
        public DateTime? ExpiringDate { get; set; }
        public string MemberName { get; set; }
        public string Avatar { get; set; }
        public string SmeCode { get; set; }
    }
}