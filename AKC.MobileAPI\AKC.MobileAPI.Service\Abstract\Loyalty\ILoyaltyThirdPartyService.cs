﻿using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyThirdPartyService
    {
        Task<LoyaltyThirdPartyPointViewOutput> PointView(LoyaltyThirdPartyPointViewInput input, HttpContext context);
        Task<LoyaltyThirdPartyVerifyNationalIdOutput> VerifyNationalId(LoyaltyThirdPartyVerifyNationalIdInput input, HttpContext context);
        Task<LoyaltyThirdPartyVerifyOTPOutput> VerifyOTP(LoyaltyThirdPartyVerifyOTPInput input, HttpContext context);
        Task<LoyaltyThirdPartyPointExchangeOutput> PointExchange(LoyaltyThirdPartyPointExchangeInput input, HttpContext context, string orderCode = null);
        Task<LoyaltyThirdPartyRevertPointOutput> RevertPoint(LoyaltyThirdPartyRevertPointInput input, HttpContext context);
        Task<LoyaltyThirdPartyRequestAccessTokenOutput> RequestAccessToken(LoyaltyThirdPartyRequestAccessTokenInput input, HttpContext context);
        Task<LoyaltyThirdPartyUpdatePartnerCachingOutput> UpdatePartnerCaching(LoyaltyThirdPartyUpdatePartnerCachingInput input, HttpContext context);
        Task<LoyaltyThirdPartyPointExchangeOutput> PointExchangeIntegration(LoyaltyThirdPartyPointExchangeInput input, HttpContext context, string orderCode = null);
    }
}
