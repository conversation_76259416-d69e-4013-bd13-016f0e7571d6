﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberConfirmConnectInput
    {
        public string LinkID_PhoneNumber { get; set; }
        public string Action { get; set; }
        
        public int? ConnectSource { get; set; }
    }

    public class RewardMemberConfirmConnectDto
    {
        public int MerchantId { get; set; }
        public string PhoneNumber { get; set; }
        public string Action { get; set; }
        public string ConnectSource { get; set; }
    }

    public static class StatusConnectMerchant
    {
        public static string Confirmed = "Confirmed";
        public static string Unconfirmed = "Unconfirmed";
        public static string Removed = "Removed";
    }
}
