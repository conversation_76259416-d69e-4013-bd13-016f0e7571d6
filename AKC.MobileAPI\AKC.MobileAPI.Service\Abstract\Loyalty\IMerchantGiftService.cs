﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Loyalty.LocationManagement;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.Loyalty.Transaction;
using AKC.MobileAPI.DTO.MerchantGift;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface IMerchantGiftService
    {
        Task<LoyaltyResponseList<MerchantGiftLocationOutput>> GetAllLocation(MerchantGiftLocationInput input);
        Task<LoyaltyResponseList<MerchantGiftGetAllCategoryOutput>> GetAllCategory(MerchantGiftGetAllCategoryInput input);
        Task<LoyaltyResponseList<MerchantGiftGetAllCategoryOutput>> GetAllCategoryFor247(MerchantGiftGetAllCategoryInput input);
        Task<LoyaltyResponseList<MerchantGiftGetAllGiftOutput>> GiftList(MerchantGiftGetAllGiftInput input);
        Task<LoyaltyResponse<MerchantGiftGetDetailOutput>> GiftDetail(MerchantGiftGetDetailInput input);
        Task<LoyaltyResponseList<MerchantGiftTransactionHistoryOutput>> TransactionHistory(MerchantGiftTransactionHistoryInput input);
        Task<LoyaltyResponse<MerchantGiftTransactionDetailOutput>> TransactionDetail(MerchantGiftTransactionDetailInput input);
        Task<MerchantGiftCreateRedeemOutput> CreateRedeem(MerchantGiftCreateRedeemInput input);
        Task<GetAllLocationManagementDto> GetAllLocationShip(MerchantGiftLocationShipInput input);
        Task<ViewLocationByIdsOutput> ViewLocationShipByIds(MerchantGiftLocationShipByIdInput input);
        Task<MerchantGiftSendOtpCreateRedeemOutput> SendOtpCreateRedeem(MerchantGiftSendOtpCreateRedeemInput input);
        Task<AdminBuyGiftForMemberOutput> AdminBuyGiftForMember(AdminBuyGiftForMemberInput input);
        Task<LoyaltyResponseList<MerchantGiftTransactionHistoryOutput>> TransactionHistoryForCardZone247(MerchantGiftTransactionHistoryInput input);
        Task<LoyaltyResponseList<MerchantGiftTransactionHistoryOutput>> GetVouchersCardZone247(MerchantGiftTransactionHistoryInput input);
        Task<RedeemVoucherOutput> MarkUseEGift(MarkUseEGiftInput input);
        Task<AdminCreateRedeemTransactionResponse> AdminCreateRedeemTransaction(AdminCreateRedeemTransactionRequest input);
        Task<AdminGiveGiftToMemberResponse> AdminGiveGiftToMember(AdminGiveGiftToMemberRequest input);
        Task<CheckAndGetMemberInfoOutput> CheckAndGetMemberInfo(GetLinkIdMemberByCifCodeInput input);
    }
}
