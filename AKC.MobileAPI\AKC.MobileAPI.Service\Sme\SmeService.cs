﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.LocationManagement;
using AKC.MobileAPI.DTO.MerchantGift;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Sme;
using AKC.MobileAPI.Service.Abstract.LoyaltyVendorGift;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Abstract.Sme;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using AKC.MobileAPI.Service.Loyalty;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Service.Sme
{
    public class SmeService : BaseSmeService, ISmeService
    {
        private readonly ILogger<SmeService> _logger;
        private readonly IRewardMemberService _memberService;
        private readonly ILinkIdLoyaltyVendorGiftService _linkIdLoyaltyService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly string SME_PARTNER_CODE = "NEOBiz";
        private readonly int _tenantId;

        public SmeService(
            IConfiguration configuration,
            ILogger<SmeService> log,
            ILinkIdLoyaltyVendorGiftService linkIdLoyaltyService,
            IRewardMemberService rw,
            IExceptionReponseService xRs,
            IDistributedCache cache) : base(configuration, cache)
        {
            _logger = log;
            _memberService = rw;
            _linkIdLoyaltyService = linkIdLoyaltyService;
            _exceptionReponseService = xRs;
            _tenantId = Convert.ToInt32(_configuration.GetSection("SmeUtilApi:TenantId").Value);
        }

        public async Task<UtilGetSmeInfoOutput> GetSmeInfo(GetSmeInfoInput input)
        {
            if (string.IsNullOrEmpty(input.SmeCif))
            {
                return new UtilGetSmeInfoOutput()
                {
                    code = SmeErrorCodes.CIFREQUIRED, message = "Cif is required"
                };
            }
            // Make call to Util API to get its info
            var res = await GetAsync<UtilGetSmeInfoOutput>("Sme/GetSmeInfo", input);
            return res;
        }

        public async Task<SmeViewPointOutput> ViewPoint(SmeViewPointInput input)
        {
            if (string.IsNullOrEmpty(input.SmeCif))
            {
                return new SmeViewPointOutput()
                {
                    code = SmeErrorCodes.CIFREQUIRED, message = "Cif is required"
                };
            }
            var sessionId = RequestGenerator();
            _logger.LogInformation($"[{sessionId}] ViewPoint  CIF#{input.SmeCif}");
            // Call sang UTIL truyền cif để lấy Wallet để gọi lên operator lấy số dư
            var utilRes = await GetAsync<UtilGetSmeInfoOutput>("Sme/GetSmeInfo", input);
            if (utilRes == null || utilRes.ErrorCode == SmeErrorCodes.SME_DOES_NOT_EXIST)
            {
                this._logger.LogError(" >> SME ViewPoint >> Cif not exist");
                return new SmeViewPointOutput()
                {
                    code = SmeErrorCodes.SME_DOES_NOT_EXIST, message = "Cif does not exist"
                };
            }

            if (utilRes.ErrorCode == "15")
            {
                this._logger.LogError(" >> SME ViewPoint >> Sme status is inactive");
                return new SmeViewPointOutput()
                {
                    code = SmeErrorCodes.SME_STATUS_IS_INACTIVE, message = "Sme status is inactive"
                };
            }
            _logger.LogInformation(" >> utilRes >> " + JsonConvert.SerializeObject(utilRes));
            if (utilRes != null && string.IsNullOrEmpty(utilRes.LinkIDSmeWallet) == false && utilRes.LinkIDSmeId.HasValue)
            {
                // Make call to operator
                try
                {
                    var balance = await _memberService.ViewSmePoint(new ViewSmePointRequest()
                    {
                        Id = utilRes.LinkIDSmeId
                    });
                    return new SmeViewPointOutput()
                    {
                        SmeCif = utilRes.Cif, LynkiDWallet = utilRes.LinkIDSmeWallet, code = SmeErrorCodes.Success,
                        Balance = balance.items.TokenBalance, ExpiringBalance = balance.items.ExpiringTokenAmount,
                        ExpiringDate = balance.items.ExpiringDate, message = "Success"
                    };
                }
                catch (Exception e)
                {
                    _logger.LogError(" >> ViewPoint Error when call to operator >> " + e.Message + "- " + e.StackTrace);
                    throw e;
                }
            }

            if (utilRes.ErrorCode == "01")
            {
                return new SmeViewPointOutput()
                {
                    code = SmeErrorCodes.SME_DOES_NOT_EXIST, message = "Sme Cif not exist"
                };
            }

            if (utilRes.ConnectionStatus == "No" || utilRes.ConnectionStatus == null)
            {
                return new SmeViewPointOutput()
                {
                    code = SmeErrorCodes.SME_NoLYNKIDCONNECTION, message = "Member does not have an active connection to LynkiD"
                };
            }

            var res = SmeErrorCodes.UtilErrorCodeConverter(utilRes.ErrorCode);
            return new SmeViewPointOutput()
            {
                code = res.code,
                message = res.message
            };
        }

        public async Task<SmeCateListOutput> GetAllCategory(SmeCateListInput input)
        {
            _logger.LogInformation(" >> SME GetAllCategory >> " + JsonConvert.SerializeObject(input));
            var cacheKey = "SME_GET_ALL_CATEGORY_" + input.SkipCount + "_" + input.MaxResultCount;
            var cachedObj = await _cache.GetStringAsync(cacheKey);
            if (string.IsNullOrWhiteSpace(input.CodeFilter) && string.IsNullOrWhiteSpace(input.NameFilter) &&
                !string.IsNullOrEmpty(cachedObj))
            {
                _logger.LogInformation(" >> SME GetAllCategory. Cache hit. Returning...");
                try
                {
                    var ret = JsonConvert.DeserializeObject<SmeCateListOutput>(cachedObj);
                    return ret;
                }
                catch (Exception e)
                {
                    _logger.LogError(" >> SME GetAllCategory. Cache hit but cannot parse!" + e.Message + e.StackTrace);
                    await _cache.RemoveAsync(cacheKey);
                }
            }
            _logger.LogInformation(" >> SME GetAllCategory. Cache missed. Querying LinkiD Gift Store...");
            // API: GetAllGiftCategoryWithoutMemberCode
            var loyRes = await _linkIdLoyaltyService.GetAllCategoryWithoutMemberCode(new GetAllCategoryWithoutMemberCodeInput()
            {
                CodeFilter = input.CodeFilter,
                NameFilter = input.NameFilter,
                MaxResultCount = input.MaxResultCount ?? 10,
                SkipCount = input.SkipCount ?? 0,
                PartnerCode = SME_PARTNER_CODE,
                StatusFilter = "A",
            });
            var res = new SmeCateListOutput()
            {
                code = SmeErrorCodes.Success,
                message = "Success",
                TotalCount = loyRes.TotalCount,
                items = loyRes.Items.Select(x => new MerchantGiftGetAllCategoryOutput()
                {
                    Code = x.GiftCategory.Code,
                    Name = x.GiftCategory.Name,
                    Link = x.GiftCategory.ImageLink?.Link ?? "",
                    FullLink = x.GiftCategory.ImageLink?.FullLink ?? "",
                    Description = x.GiftCategory.Description,
                    Id = x.GiftCategory.Id,
                    ParentCode = x.GiftCategory.ParentCode
                }).ToList()
            };
            // save to cache and return
            if (string.IsNullOrWhiteSpace(input.CodeFilter) && string.IsNullOrWhiteSpace(input.NameFilter))
            {
                await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(res), new DistributedCacheEntryOptions()
                    .SetAbsoluteExpiration(TimeSpan.FromMinutes(5)));
            }
            return res;
        }

        public async Task<SmeGetListTokenTransOutput> GetListTokenTrans(SmeGetListTokenTransInput input)
        {
            _logger.LogInformation(" >> SME GetListTokenTrans >> " + JsonConvert.SerializeObject(input));
            if (string.IsNullOrEmpty(input.SmeCif))
            {
                return new SmeGetListTokenTransOutput()
                {
                    code = SmeErrorCodes.CIFREQUIRED, message = "Cif is required"
                };
            }
            var cacheKey = "SME_GET_TOKENTX_" + CommonHelper.ComputeHash(input);
            var cachedObj = await _cache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(cachedObj))
            {
                _logger.LogInformation(" >> SME GetListTokenTrans. Cache hit. Returning...");
                try
                {
                    var ret = JsonConvert.DeserializeObject<SmeGetListTokenTransOutput>(cachedObj);
                    return ret;
                }
                catch (Exception e)
                {
                    _logger.LogError(" >> SME GetListTokenTrans. Cache hit but cannot parse!" + e.Message + e.StackTrace);
                    await _cache.RemoveAsync(cacheKey);
                }
            }
            _logger.LogInformation(" >> SME GetListTokenTrans. Cache missed. Querying Reward Network...");
            input.SkipCount = (!input.SkipCount.HasValue || input.SkipCount < 0) ? 0 : input.SkipCount; // Default 0
            input.MaxResultCount = (!input.MaxResultCount.HasValue || input.MaxResultCount <= 0 || input.MaxResultCount > 100)? 50 : input.MaxResultCount; // Default 50, ko cho > 100 va <= 0
            var smeObj = await GetSmeInfo(new GetSmeInfoInput() { SmeCif = input.SmeCif });
            if (smeObj == null || smeObj.ErrorCode == SmeErrorCodes.SME_DOES_NOT_EXIST)
            {
                this._logger.LogError(" >> SME GetListTokenTrans >> Cif not exist");
                return new SmeGetListTokenTransOutput()
                {
                    code = SmeErrorCodes.SME_DOES_NOT_EXIST, message = "Cif does not exist"
                };
            }

            if (smeObj.ErrorCode == "15")
            {
                this._logger.LogError(" >> SME GetListTokenTrans >> Sme status is inactive");
                return new SmeGetListTokenTransOutput()
                {
                    code = SmeErrorCodes.SME_STATUS_IS_INACTIVE, message = "Sme status is inactive"
                };
            }

            if (!smeObj.LinkIDSmeId.HasValue && string.IsNullOrEmpty(smeObj.LinkIDSmeWallet))
            {
                this._logger.LogError(" >> SME GetListTokenTrans >> Cif does not have connection");
                return new SmeGetListTokenTransOutput()
                {
                    code = SmeErrorCodes.SME_NoLYNKIDCONNECTION, message = "SME Does Not Have Active Connection To LynkiD"
                };
            }
            var linkidMemberId = smeObj.LinkIDSmeId;
            var RNRES = await _memberService.GetRewardAsync<RewardMemberTokenTransGetByIdOutput>(RewardApiUrl.GET_TOKEN_TRANS_SMECIF, 
                new 
                {
                    SmeId = linkidMemberId, skipCount = input.SkipCount, maxResultCount = input.MaxResultCount,
                    FromDateFilter = (input.FromDate ?? DateTime.UtcNow.AddDays(-30)).ToString("yyyy-MM-ddTHH:mm:ss.000"), 
                    ToDateFilter = (input.ToDate ?? DateTime.UtcNow).ToString("yyyy-MM-ddTHH:mm:ss.999"), ActionTypeFilter = input.Type
                });

            var result = RNRES.Items.Select(x => new SmeSingleTokenTransInfo()
            {
                ActionCode = x.ActionCode,
                ActionType = x.ActionType,
                ActionCodeDetail = x.ActionCodeDetail,
                TokenAmount = x.TokenAmount,
                UserAddress = x.UserAddress,
                Time = x.Time,
                BusinessTime = x.BusinessTime,
                ExpiryDate = x.ExpiryDate,
                OrderCode = x.OrderCode,
                TxId = x.TokenTransID
            }).ToList();
            List<int> glstCampaignIdNoCache = new List<int>();

            foreach (var item in result)
            {
                if(item.ActionCode == "Invoice")
                {
                    var campaignId = item.ActionCodeDetail.Split("|")[1];
                    var cachedObjCampaign = await _cache.GetStringAsync($"CAMAPIGN_NAME_{campaignId}");
                    if(!string.IsNullOrEmpty(cachedObjCampaign))
                    {
                        var campaign = JsonConvert.DeserializeObject<CampaignShortInfor>(cachedObjCampaign);
                        item.LoyaltyCampaignName = campaign.CampaginName;
                    }
                    else
                    {
                        glstCampaignIdNoCache.Add(int.Parse(campaignId));
                    }    
                }    
            }

            if(glstCampaignIdNoCache.Count > 0)
            {
                var getListCampaignShortInput = new GetListCampaignShortInforInput { ListId = glstCampaignIdNoCache, TenantId = _tenantId };
                var glstCampaignName = await PostAsync<GetListCampaignShortInforOutput>("Campaign/GetListCampaingShortInfor", getListCampaignShortInput);
                foreach(var item in glstCampaignName.ListCamgpaignShortInfor)
                {
                    await _cache.SetStringAsync($"CAMAPIGN_NAME_{item.Id}", JsonConvert.SerializeObject(item), new DistributedCacheEntryOptions()
               .SetAbsoluteExpiration(TimeSpan.FromDays(1)));
                }
                foreach (var item in result)
                {
                    if (item.ActionCode == "Invoice" && string.IsNullOrEmpty(item.LoyaltyCampaignName))
                    {
                        var campaignId = item.ActionCodeDetail.Split("|")[1];
                        item.LoyaltyCampaignName = glstCampaignName.ListCamgpaignShortInfor.Find(x => x.Id == int.Parse(campaignId))?.CampaginName;
                    }
                }
            }    

            var res = new SmeGetListTokenTransOutput()
            {
                message = "Success",
                code = SmeErrorCodes.Success,
                TotalCount = RNRES.TotalCount, 
                items = result
            };
            await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(res), new DistributedCacheEntryOptions()
                .SetAbsoluteExpiration(TimeSpan.FromSeconds(30)));
            return res;
        }

        public async Task<FindOneTokenTransOutput> FindOneTokenTrans(FindOneTokenTransInput input)
        {
            _logger.LogInformation(" >> SME FindOneTokenTrans >> " + JsonConvert.SerializeObject(input));
            if (string.IsNullOrEmpty(input.SmeCif))
            {
                return new FindOneTokenTransOutput()
                {
                    code = SmeErrorCodes.CIFREQUIRED, message = "Cif is required"
                };
            }
            if (string.IsNullOrEmpty(input.TxId))
            {
                return new FindOneTokenTransOutput()
                {
                    code = SmeErrorCodes.TOKENTRASN_Required, message = "Token Trans Id Required"
                };
            }

            var cacheKey = "SME_GET_SINGLE_TOKENTX_" + CommonHelper.ComputeHash(input);
            var cachedObj = await _cache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(cachedObj))
            {
                _logger.LogInformation(" >> SME FindOneTokenTrans. Cache hit. Returning...");
                try
                {
                    var ret = JsonConvert.DeserializeObject<FindOneTokenTransOutput>(cachedObj);
                    return ret;
                }
                catch (Exception e)
                {
                    _logger.LogError(" >> SME FindOneTokenTrans. Cache hit but cannot parse!" + e.Message + e.StackTrace);
                    await _cache.RemoveAsync(cacheKey);
                }
            }
            var smeObj = await GetSmeInfo(new GetSmeInfoInput() { SmeCif = input.SmeCif });
            if (smeObj == null || smeObj.ErrorCode == SmeErrorCodes.SME_DOES_NOT_EXIST)
            {
                this._logger.LogError(" >> SME FindOneTokenTrans >> Cif not exist");
                return new FindOneTokenTransOutput()
                {
                    code = SmeErrorCodes.SME_DOES_NOT_EXIST, message = "Cif not valid"
                };
            }
            if (smeObj.ErrorCode == "15")
            {
                this._logger.LogError(" >> SME FindOneTokenTrans >> Sme status is inactive");
                return new FindOneTokenTransOutput()
                {
                    code = SmeErrorCodes.SME_STATUS_IS_INACTIVE, message = "Sme status is inactive"
                };
            }
            var smeId = smeObj.LinkIDSmeId;
            try
            {
                RewardMemberTokenTransGetByIdItems RNRES;
                try
                {
                    RNRES = await _memberService.GetRewardAsync<RewardMemberTokenTransGetByIdItems>(RewardApiUrl.FINDONE_TOKEN_TRANS_SMECIF, 
                        new 
                        {
                            SmeId = smeId, TokenTransId = input.TxId
                        });
                }
                catch (Exception e)
                {
                    return new FindOneTokenTransOutput()
                    {
                        code = SmeErrorCodes.TOKENTRASN_NOT_FOUND, message = "Token Transaction Not Found"
                    };
                }
                _logger.LogInformation(" >> SME FindOneTokenTrans >> TokenTrans found: " + JsonConvert.SerializeObject(RNRES));
                if (RNRES == null)
                {
                    return new FindOneTokenTransOutput()
                    {
                        code = SmeErrorCodes.TOKENTRASN_NOT_FOUND, message = "Token Transaction Not Found"
                    };
                }

                var campaignName = "";
                if (RNRES.ActionCodeDetail != null && RNRES.ActionCodeDetail.StartsWith("C|"))
                {
                    var x = RNRES.ActionCodeDetail.Split("|");
                    if (x.Length > 1)
                    {
                        var campIdStr = x[1];
                        int campId;
                        if (int.TryParse(campIdStr, out campId))
                        {
                            if (campId > 0)
                            {
                                try
                                {
                                    var resCmpN = await GetAsync<UtilGetCampaignName>("Sme/GetCampaignName", new {CampaignId = campId});
                                    campaignName = resCmpN?.CampaignName;
                                }
                                catch (Exception e)
                                {
                                   _logger.LogError(" Error calling UTIL API to get campName >>" + e.Message + "-" + e.StackTrace);
                                }
                            }
                        }
                    }
                }
                var res = new FindOneTokenTransOutput()
                {
                    message = "Success",
                    code = SmeErrorCodes.Success,
                    LoyaltyCampaignName = campaignName,
                    TxMainInfo = new SmeSingleTokenTransInfo()
                    {
                        ActionCode = RNRES.ActionCode,
                        ActionType = RNRES.ActionType,
                        ActionCodeDetail = RNRES.ActionCodeDetail,
                        TokenAmount = RNRES.TokenAmount,
                        UserAddress = RNRES.UserAddress, Time = RNRES.Time, BusinessTime = RNRES.BusinessTime, ExpiryDate = RNRES.ExpiryDate,
                        OrderCode = RNRES.OrderCode, TxId = RNRES.TokenTransID
                    }
                };
                await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(res), new DistributedCacheEntryOptions()
                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(30)));
                return res;
            }
            catch (Exception e)
            {
                _logger.LogError("Error while access RN to get trans >> " + e.Message + " - " + e.StackTrace);
                return new FindOneTokenTransOutput()
                {
                    code = SmeErrorCodes.UnknownError, message = "Error Happened"
                };
            }
        }

        public async Task<SmeRedeemGiftOutput> RedeemGift(SmeRedeemGiftInput input)
        {
            // Validate
            if (string.IsNullOrEmpty(input.SmeCif))
            {
                return new SmeRedeemGiftOutput()
                {
                    code = SmeErrorCodes.CIFREQUIRED, message = "Cif is required"
                };
            }
            if (string.IsNullOrEmpty(input.GiftCode))
            {
                return new SmeRedeemGiftOutput()
                {
                    code = SmeErrorCodes.SME_REDEEM_GIFTCODE_REQUIRED, message = "GiftCode is required"
                };
            }
            if (input.Quantity <= 0)
            {
                return new SmeRedeemGiftOutput()
                {
                    code = SmeErrorCodes.SME_REDEEM_QUANTITYNOTZERO, message = "Quantity must be positive integer"
                };
            }
            
            var smeObj = await GetSmeInfo(new GetSmeInfoInput() { SmeCif = input.SmeCif });
            if (smeObj == null || smeObj.ErrorCode == SmeErrorCodes.SME_DOES_NOT_EXIST)
            {
                this._logger.LogError(" >> SME RedeemGift >> Cif not exist");
                return new SmeRedeemGiftOutput()
                {
                    code = SmeErrorCodes.SME_DOES_NOT_EXIST, message = "Cif not valid", SmeCif = input.SmeCif, Egifts = null,
                    GiftTransactionCode = null
                };
            }
            if (smeObj.ErrorCode == "15")
            {
                this._logger.LogError(" >> SME RedeemGift >> Sme status is inactive");
                return new SmeRedeemGiftOutput()
                {
                    code = SmeErrorCodes.SME_STATUS_IS_INACTIVE, message = "Sme status is inactive"
                };
            }
            this._logger.LogInformation(">> smeObj found in db: >> " + JsonConvert.SerializeObject(smeObj));
            if (smeObj.ConnectionStatus == "No")
            {
                this._logger.LogError(" >> SME RedeemGift >> Cif does not have active connection to LynkiD");
                return new SmeRedeemGiftOutput()
                {
                    code = SmeErrorCodes.SME_NoLYNKIDCONNECTION, message = "Cif does not have active connection to LynkiD",
                    SmeCif = input.SmeCif, Egifts = null,
                    GiftTransactionCode = null
                };
            }
            // Check số dư
            var linkidSmeCode = ""; // Membercode sẽ dùng để đổi quà
            if (input.Amount >= 0)
            {
                var checkBalance = await _memberService.ViewSmePoint(new ViewSmePointRequest() { Id = smeObj.LinkIDSmeId });
                if (checkBalance?.items == null)
                {
                    this._logger.LogError(" >> SME RedeemGift >> response of checkBalance is not valid");
                    return new SmeRedeemGiftOutput()
                    {
                        code = SmeErrorCodes.UnknownError, message = "Cannot get information from LynkID"
                    };
                }

                if (checkBalance.items.TokenBalance < input.Amount)
                {
                    this._logger.LogError(" >> SME RedeemGift >> response of checkBalance, insufficient amount: " + JsonConvert.SerializeObject(checkBalance));
                    return new SmeRedeemGiftOutput()
                    {
                        code = SmeErrorCodes.SME_NotEnoughToken, message = "Insufficient Token Amount"
                    };
                }

                linkidSmeCode = checkBalance.items.SmeCode;
            }
            // Trừ token trên RN
            var transactionCode = (!string.IsNullOrWhiteSpace(input.TransactionCode)) 
                ? input.TransactionCode
                : LoyaltyHelper.GenTransactionCodeV2("NEOBiz");
            input.TransactionCode = transactionCode;

            //var linkidMerchantId = Convert.ToInt32(_configuration.GetSection("LoyaltyLinkID:MerchantIdRedeem").Value);
            var merchantIdFromGiftCode = await _linkIdLoyaltyService.GetMerchantIdFromGiftCode(new LoyaltyGiftGetMerchantIdFromGiftCodeInput()
            {
                GiftCode = input.GiftCode
            });

            if (merchantIdFromGiftCode == null || !merchantIdFromGiftCode.Success || merchantIdFromGiftCode.Result == null || !merchantIdFromGiftCode.Result.MerchantId.HasValue)
            {
                CommonHelper.GetErrorValidation("1030", "Cannot find merchant");
            }
            var linkidMerchantId = merchantIdFromGiftCode.Result.MerchantId.Value;

            var cts = new CancellationTokenSource();
            //  Tạo bảng ghi CreateGiftRedeemTransaction
            var loyaltyRedeemRequest = new MerchantGiftCreateRedeemInputDto()
            {
                Description = input.Description,
                GiftCode = input.GiftCode,
                MemberCode = linkidSmeCode,
                Quantity = input.Quantity,
                TotalAmount = input.Amount,
                Date = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                TransactionCode = transactionCode,
                OtpSession = "",
                VpoCifCode = input.SmeCif,
                RedeemSource = "NEOBiz",
                MerchantIdRedeem = linkidMerchantId
            };
            var createRedeemRequest = await _linkIdLoyaltyService.PostLoyaltyAsync<VerifyAndCreateRedeemOrderOutput>(LoyaltyApiUrl.VERIFY_OR_CREATE_REDEEM_ORDER, loyaltyRedeemRequest);
            if ((!createRedeemRequest.Success || (createRedeemRequest.Success && !createRedeemRequest.Result.IsSuccess)))
            {
                this._logger.LogError(" >> SME RedeemGift >> Error createRedeemRequest: " + JsonConvert.SerializeObject(createRedeemRequest));
                var temp = SmeErrorCodes.LinkIDLoyatltyErrorCode(SmeErrorCodes.SME_CANNOT_REDEEM_AT_THIS_TIME);
                // Can not redeem at this time
                return new SmeRedeemGiftOutput()
                {
                    code = temp.code, message = temp.message,
                };
            }
            var successPaymentToken = false;            
            var hasReverted = false;
            var requestRevert = new RewardMemberRevertRedeemInput()
            {
                MerchantId = merchantId,
                MemberId = smeObj.LinkIDSmeId ?? 0 ,
                OrderCode = transactionCode,
            };
            try
            {
                var resultRedeemReward = await _memberService.PostRewardAsync<RewardMemberRedeemOutput>(RewardApiUrl.SME_REDEEM_GIFT_TRANSACTION_CREATE, new
                {
                    MemberId = smeObj.LinkIDSmeId, MerchantId = linkidMerchantId, OrderCode  = transactionCode, TotalRequestedAmount = input.Amount
                }, MerchantNameConfig.VPID);
                // var resultRedeemReward = new RewardMemberRedeemOutput()
                // {
                //     message = "", result = 200, items = new RewardMemberRedeemOutputItemDto() {TokenAmount = 0, TokenTransID = "idtokentransnsnsnsns"}
                // };
                if (resultRedeemReward.result == 202)
                {
                    successPaymentToken = true;
                    _logger.LogError($"Create redeem reward with status 202 {JsonConvert.SerializeObject(resultRedeemReward)}");
                
                }
                requestRevert.OriginalTokenTransID = resultRedeemReward.items.TokenTransID;
                requestRevert.TokenAmount = resultRedeemReward.items.TokenAmount;
                successPaymentToken = true;
                var result = new MerchantGiftCreateRedeemOutputDto()
                {
                    Result = new MerchantGiftCreateRedeemTransactionDto()
                    {
                        SuccessedRedeem = false,
                    }
                };
                result = await _linkIdLoyaltyService.PostLoyaltyAsync<MerchantGiftCreateRedeemOutputDto>(LoyaltyApiUrl.MERCHANT_GIFT_CREATE_REDEEM, loyaltyRedeemRequest);
                if (!string.IsNullOrWhiteSpace(result.Result.Exception) || !result.Result.SuccessedRedeem)
                {
                    if (!result.Result.Timeout)
                    {
                        hasReverted = true;
                        await _linkIdLoyaltyService.retryRevertToken(requestRevert, RewardApiUrl.SME_REDEEM_GIFT_TRANSACTION_REVERT);
                    }
                    await _linkIdLoyaltyService.UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = input.TransactionCode,
                        ErrorCode = result.Result.Exception,
                        ErrorMessage = result.Result.Messages
                    }, successPaymentToken);
                    CommonHelper.GetErrorValidation(result.Result.Exception == "1032" ? "1032" : CommonHelper.GetCodeRedeemFromCode(result.Result.Exception),
                        result.Result.Exception == "1032" ? "Timeout when redeem in Gift Engine" : CommonHelper.GetMessageRedeemFromCode(result.Result.Exception));
                }

                if (result.Result.SuccessedRedeem)
                {
                    // Done
                    return new SmeRedeemGiftOutput()
                    {
                        SmeCif = input.SmeCif, code = SmeErrorCodes.Success, message = "Success",
                        GiftTransactionCode = input.TransactionCode,
                        Egifts = result.Result.Items.Select(x => new SmeRedeemGiftOutputInner()
                        {
                            Description = x.EGift?.Description, Status = x.EGift?.Status, ExpiredDate = x.EGift?.ExpiredDate, EGiftCode = x.EGift?.Code,
                            QRCode = x.EGift?.QRCode
                        }).ToList()
                    };
                }

                throw new Exception();

            }
            catch (WebException ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    if (res.Code != "0" && res.Code != "SystemError" && !hasReverted)
                    {
                        await _linkIdLoyaltyService.retryRevertToken(requestRevert, RewardApiUrl.SME_REDEEM_GIFT_TRANSACTION_REVERT);
                    }
                }
                await _linkIdLoyaltyService.GetErrorFromExeption(ex, input.TransactionCode, successPaymentToken);
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    if (res.Code != "0" && res.Code != "SystemError" && !hasReverted)
                    {
                        await _linkIdLoyaltyService.retryRevertToken(requestRevert, RewardApiUrl.SME_REDEEM_GIFT_TRANSACTION_REVERT);
                    }
                }

                await _linkIdLoyaltyService.GetErrorFromExeption(ex, input.TransactionCode, successPaymentToken);
                if (!cts.Token.IsCancellationRequested)
                {
                    await _linkIdLoyaltyService.UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = input.TransactionCode,
                        ErrorCode = "408",
                        ErrorMessage = "Timed Out with: " + ex.Message,
                    }, successPaymentToken);
                    throw new Exception("Timed Out with: ", ex);
                }
                else
                {
                    await _linkIdLoyaltyService.UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = input.TransactionCode,
                        ErrorCode = "504",
                        ErrorMessage = "Cancelled for some other reason: " + ex.Message,
                    }, successPaymentToken);
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    if (res.Code == "RedeemRewardTransactionStatus202")
                    {
                        await _linkIdLoyaltyService.UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                        {
                            TransactionCode = input.TransactionCode,
                            ErrorCode = "202",
                            ErrorMessage = "" + res.MessageDetail,
                        }, successPaymentToken);
                        throw new Exception("" + res.MessageDetail);
                    }
                }

                // Nếu không phải exception của reward thì mới revert
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    if (res.Code != "0" && res.Code != "SystemError" && !hasReverted)
                    {
                        await _linkIdLoyaltyService.retryRevertToken(requestRevert, RewardApiUrl.SME_REDEEM_GIFT_TRANSACTION_REVERT);
                    }
                }
                await _linkIdLoyaltyService.GetErrorFromExeption(ex, input.TransactionCode, successPaymentToken);
                throw ex;
            }
        }

        public async Task<SmeGetRedeemTransOutput> GetRedeemTransaction(SmeGetRedeemTransInput input)
        {
            if (input.SkipCount.HasValue == false || input.SkipCount.Value < 0)
            {
                input.SkipCount = 0;
            }
            if (input.MaxResultCount.HasValue == false || input.MaxResultCount.Value <= 10)
            {
                input.MaxResultCount = 10;
            }
            if (string.IsNullOrEmpty(input.SmeCif))
            {
                return new SmeGetRedeemTransOutput()
                {
                    code = SmeErrorCodes.CIFREQUIRED, message = "Cif is required"
                };
            }

            if (input.GiftTypeFilter != null && input.GiftTypeFilter.Trim() != "P" &&
                input.GiftTypeFilter.Trim() != "EGIFT")
            {
                return new SmeGetRedeemTransOutput()
                {
                    code = SmeErrorCodes.GIFTYPE_VALUE_IS_NOTVALID, message = "GiftType param is not valid. Please leave it blank or set it to P or EGIFT"
                };
            }
            // Check Cif existence and validate connection
            var smeObj = await GetSmeInfo(new GetSmeInfoInput() { SmeCif = input.SmeCif });
            if (smeObj == null || smeObj.ErrorCode == SmeErrorCodes.SME_DOES_NOT_EXIST)
            {
                this._logger.LogError(" >> SME GetRedeemTransaction >> Cif not exist");
                return new SmeGetRedeemTransOutput()
                {
                    code = SmeErrorCodes.SME_DOES_NOT_EXIST, message = "Cif not valid"
                };
            }
            if (smeObj.ErrorCode == "15")
            {
                this._logger.LogError(" >> SME GetRedeemTransaction >> Sme status is inactive");
                return new SmeGetRedeemTransOutput()
                {
                    code = SmeErrorCodes.SME_STATUS_IS_INACTIVE, message = "Sme status is inactive"
                };
            }
            this._logger.LogInformation(">> GetRedeemTransaction smeObj found in db: >> " + JsonConvert.SerializeObject(smeObj));
            if (smeObj.ConnectionStatus == "No")
            {
                this._logger.LogError(" >> SME GetRedeemTransaction >> Cif does not have active connection to LynkiD");
                return new SmeGetRedeemTransOutput()
                {
                    code = SmeErrorCodes.SME_NoLYNKIDCONNECTION, message = "Cif does not have active connection to LynkiD"
                };
            }
            var checkBalance = await _memberService.ViewSmePoint(new ViewSmePointRequest() { Id = smeObj.LinkIDSmeId });
            if (checkBalance?.items == null)
            {
                this._logger.LogError(" >> SME GetRedeemTransaction >> response of checkBalance is not valid");
                return new SmeGetRedeemTransOutput()
                {
                    code = SmeErrorCodes.UnknownError, message = "Cannot get information from LynkID"
                };
            }

            var ownerCode = checkBalance.items.SmeCode;
            // Make call to Loyalty
            var list = await _linkIdLoyaltyService.TransactionHistorySme(new TransactionHistorySmeInput()
            {
                SkipCount = input.SkipCount.Value, MaxResultCount = input.MaxResultCount.Value,
                GiftTransactionCode = "", StatusFilter = input.StatusFilter, EGiftStatusFilter = input.EGiftStatusFilter,
                FromDateFilter = input.FromDate, OwnerCodeFilter = ownerCode, ToDateFilter = input.ToDate, GiftTypeFilter = input.GiftTypeFilter
            });
            _logger.LogInformation(" >> SME GetRedeemTransaction >> res from loyalty >> Is null? " + (list == null));
            if (list == null || list.Result == null)
            {
                return new SmeGetRedeemTransOutput()
                {
                    code = SmeErrorCodes.UnknownError, message = "Error Happened"
                };
            }
            // call to loyalty
            return new SmeGetRedeemTransOutput()
            {
                code = SmeErrorCodes.Success, message = "Success", 
                TotalCount = list.Result.TotalCount, Items = list.Result.Items.Select(x => new SmeGetRedeemTransOutputInner()
                {
                    GiftTransaction = new SmeGiftTransactionDto()
                    {
                        Code =  x.GiftTransaction.Code,
                        Coin =  x.GiftTransaction.Coin,
                        Date =  x.GiftTransaction.Date,
                        Description =  x.GiftTransaction.Description,
                        Id =  x.GiftTransaction.Id,
                        Introduce =  x.GiftTransaction.Introduce,
                        Quantity =  x.GiftTransaction.Quantity,
                        Status =  x.GiftTransaction.Status,
                        BuyerCode =  x.GiftTransaction.BuyerCode,
                        OwnerCode =  x.GiftTransaction.OwnerCode,
                        GiftCode =  x.GiftTransaction.GiftCode,
                        GiftName =  x.GiftTransaction.GiftName,
                        TotalCoin =  x.GiftTransaction.TotalCoin,
                        TransactionCode =  x.GiftTransaction.TransactionCode,
                        QRCode =  x.GiftTransaction.QRCode,
                        CodeDisplay =  x.GiftTransaction.CodeDisplay,
                        GiftId =  x.GiftTransaction.GiftId,
                        TransferTime =  x.GiftTransaction.TransferTime,
                        SerialNo =  x.GiftTransaction.SerialNo,
                        LinkShippingInfo =  x.GiftTransaction.LinkShippingInfo,
                    }, ImageLinks = x.ImageLinks.Select((y) => y.Link).ToList(), VendorInfo = x.VendorInfo,
                    EGift = x.EGift == null ? new SmeRedeemGiftOutputInner() : new SmeRedeemGiftOutputInner()
                    {
                        Description = x.EGift.Description, Status = x.EGift.Status, EGiftCode = x.EGift.Code, ExpiredDate = x.EGift.ExpiredDate,
                        UsedStatus = x.EGift.UsedStatus
                    }
                } ).ToList()
            };
        }

        private SmeGiftTransactionDto ConvertTransactionDto(GetGiftTransSmeItem x)
        {
            return new SmeGiftTransactionDto()
            {
                Code = x.GiftTransaction.Code,
                Coin = x.GiftTransaction.Coin,
                Date = x.GiftTransaction.Date,
                Description = x.GiftTransaction.Description,
                Id = x.GiftTransaction.Id,
                Introduce = x.GiftTransaction.Introduce,
                Quantity = x.GiftTransaction.Quantity,
                Status = x.GiftTransaction.Status,
                BuyerCode = x.GiftTransaction.BuyerCode,
                OwnerCode = x.GiftTransaction.OwnerCode,
                GiftCode = x.GiftTransaction.GiftCode,
                GiftName = x.GiftTransaction.GiftName,
                TotalCoin = x.GiftTransaction.TotalCoin,
                TransactionCode = x.GiftTransaction.TransactionCode,
                QRCode = x.GiftTransaction.QRCode,
                CodeDisplay = x.GiftTransaction.CodeDisplay,
                GiftId = x.GiftTransaction.GiftId,
                TransferTime = x.GiftTransaction.TransferTime,
                SerialNo = x.GiftTransaction.SerialNo,
                LinkShippingInfo = x.GiftTransaction.LinkShippingInfo,
            };
        }
        public async Task<GetTxDetailOutput> GetTxDetail(GetTxDetailInput input)
        {
            // Check Cif existence and validate connection
            if (string.IsNullOrEmpty(input.SmeCif))
            {
                return new GetTxDetailOutput()
                {
                    code = SmeErrorCodes.CIFREQUIRED, message = "Cif is required"
                };
            }
            if (string.IsNullOrEmpty(input.GiftTransactionCode))
            {
                return new GetTxDetailOutput()
                {
                    code = SmeErrorCodes.SME_TX_DETAIL_TXCODEREQUIRED, message = "Transaction Code is required"
                };
            }
            var smeObj = await GetSmeInfo(new GetSmeInfoInput() { SmeCif = input.SmeCif });
            if (smeObj == null || smeObj.ErrorCode == SmeErrorCodes.SME_DOES_NOT_EXIST)
            {
                this._logger.LogError(" >> SME GetTxDetail >> Cif not exist");
                return new GetTxDetailOutput()
                {
                    code = SmeErrorCodes.SME_DOES_NOT_EXIST, message = "Cif not valid"
                };
            }
            if (smeObj.ErrorCode == "15")
            {
                this._logger.LogError(" >> SME GetTxDetail >> Sme status is inactive");
                return new GetTxDetailOutput()
                {
                    code = SmeErrorCodes.SME_STATUS_IS_INACTIVE, message = "Sme status is inactive"
                };
            }
            this._logger.LogInformation(">> GetTxDetail smeObj found in db: >> " + JsonConvert.SerializeObject(smeObj));
            if (smeObj.ConnectionStatus == "No")
            {
                this._logger.LogError(" >> SME GetTxDetail >> Cif does not have active connection to LynkiD");
                return new GetTxDetailOutput()
                {
                    code = SmeErrorCodes.SME_NoLYNKIDCONNECTION, message = "Cif does not have active connection to LynkiD"
                };
            }
            var checkBalance = await _memberService.ViewSmePoint(new ViewSmePointRequest() { Id = smeObj.LinkIDSmeId });
            if (checkBalance?.items == null)
            {
                this._logger.LogError(" >> SME GetTxDetail >> response of checkBalance is not valid");
                return new GetTxDetailOutput()
                {
                    code = SmeErrorCodes.UnknownError, message = "Cannot get information from LynkID"
                };
            }

            var ownerCode = checkBalance.items.SmeCode;
            var res = await _linkIdLoyaltyService.TransactionHistorySmeDetail(new TransactionHistorySmeInput()
            {
                OwnerCodeFilter = ownerCode, GiftTransactionCode = input.GiftTransactionCode
            });
            this._logger.LogInformation(" >> GetTxDetail res from Loyalty >> " + JsonConvert.SerializeObject(res));
            if (res?.Result == null || res.Result.Items.Count == 0)
            {
                return new GetTxDetailOutput()
                {
                    code = SmeErrorCodes.SME_DETAIL_TXNOTEXIST, message = "Transaction Code and Owner are not match",
                };
            }
            
            return new GetTxDetailOutput
            {
                code = SmeErrorCodes.Success, message = "Success",
                GiftTransaction = ConvertTransactionDto(res.Result.Items[0]),
                EGift = new SmeRedeemGiftOutputInner()
                {
                    EGiftCode = res.Result.Items[0].EGift?.Code,
                    UsedStatus = res.Result.Items[0].EGift?.UsedStatus,
                    UsageCheck = res.Result.Items[0].EGift?.UsageCheck ?? false,
                    QRCode = res.Result.Items[0].GiftTransaction.QRCode,
                    ExpiredDate = res.Result.Items[0].EGift?.ExpiredDate,
                    Status = res.Result.Items[0].EGift?.Status,
                    
                },
                ImageLinks = res.Result.Items[0].ImageLinks.Select((y) => y.Link).ToList(),
                VendorInfo =  res.Result.Items[0].VendorInfo, GiftUsageAddress = res.Result.Items[0].GiftUsageAddress
            };
        }
        public async Task<CreateSmeConnectionOutput> CreateConnection(CreateSmeConnectionInput input)
        {
            var sessionId = RequestGenerator();
            var response = new CreateSmeConnectionOutput()
            {
                code = "00",
                message = "Success"
            };
            if (string.IsNullOrEmpty(input.SmeCif))
            {
                response.code = SmeErrorCodes.CIFREQUIRED;
                response.message = "Cif is required";
                return response;
            }
            _logger.LogInformation($"[{sessionId}] Creating connection for  CIF#{input.SmeCif}");
            // Quick validation
            var smeCheck = await GetSmeInfo(new GetSmeInfoInput() { SmeCif = input.SmeCif });
            if (smeCheck == null || smeCheck.Id == null)
            {
                _logger.LogInformation($"[{sessionId}] There is no existing CIF#{input.SmeCif}. Creating one...");
                // if (input.ExtraData == null)
                // {
                //     input.ExtraData = new ExtraDataForConnect();
                // }
                // Make sure TaxNumber / License Number / Start Business Date / extraData are required
                if (string.IsNullOrEmpty(input.LicenseNumber))
                {
                    response.code = SmeErrorCodes.SME__CREATECONN__LICENSEREQUIRED;
                    response.message = "License number is required";
                    return response;
                }
                if (string.IsNullOrEmpty(input.LicenseNumber))
                {
                    response.code = SmeErrorCodes.SME__CREATECONN__TAXREQUIRED;
                    response.message = "Tax number is required";
                    return response;
                }

                DateTime? stbd = null;
                if (string.IsNullOrWhiteSpace(input.StartBusinessDate))
                {
                    // response.code = SmeErrorCodes.SME__CREATECONN__DoBREQUIRED;
                    // response.message = "StartBusinessDate is required";
                    // return response;
                    // OK, allow it to be empty
                    input.StartBusinessDate = null;
                    stbd = null;
                }
                else
                {
                    // Có giá trị thì sẽ validate
                    DateTime parsedDate;
                    var isValidFormat = DateTime.TryParseExact(input.StartBusinessDate, "yyyy-MM-dd", CultureInfo.InvariantCulture, 
                        System.Globalization.DateTimeStyles.None, out parsedDate);
                    if (!isValidFormat || parsedDate.Date > DateTime.UtcNow.Date)
                    {
                        response.code = SmeErrorCodes.SME__CREATECONN__DoB_invalid;
                        response.message = "StartBusinessDate invalid value";
                        return response;
                    }

                    stbd = parsedDate;
                }

                if (input.ExtraData == null)
                {
                    response.code = SmeErrorCodes.SME__CREATECONN__ExtraDataREQUIRED;
                    response.message = "Extra Data is required to create new SME";
                    return response;
                }
                
                
                var createRs = await PostAsync<UtilCreateSmeOutput>("Sme/CreateSme", new CreateSmeInput()
                {
                    SmeInfo = new CreateSmeInputSme()
                    {
                        Address = input.ExtraData.Address, ContactEmail = input.ExtraData.ContactEmail, ContactPhone = input.ExtraData.ContactPhone, EnglishName = "",
                        FullName = input.ExtraData.FullName, LicenseNumber = input.LicenseNumber, ShortName = input.ExtraData.ShortName,
                        SmeCif = input.SmeCif, TaxNumber = input.TaxNumber,
                        BusinessStartDate = stbd?.AddHours(8) ?? stbd
                    },
                    ListMemberInfo = new List<CreateSmeInputMember>()
                });
                _logger.LogInformation($"[{sessionId}] Done creation! Res = {JsonConvert.SerializeObject(createRs)}");
                if (createRs.ErrorCode != "00")
                {
                    var res = SmeErrorCodes.UtilErrorCodeConverter(createRs.ErrorCode);
                    response.code = res.code;
                    response.message = res.message;
                    return response;
                }
                // Get again to use
                smeCheck = await GetSmeInfo(new GetSmeInfoInput() { SmeCif = input.SmeCif });
            }
            else
            {
                if (smeCheck.ErrorCode == "15")
                {
                    this._logger.LogError(" >> SME GetListTokenTrans >> Sme status is inactive");
                    return new CreateSmeConnectionOutput()
                    {
                        code = SmeErrorCodes.SME_STATUS_IS_INACTIVE, message = "Sme status is inactive"
                    };
                }
                // Dữ liệu của SME (khi existing) thì ghi lại input kẻo sai dữ liệu
                input.LicenseNumber = smeCheck.LicenseNumber;
                input.TaxNumber = smeCheck.TaxNumber;
            }

            if (smeCheck != null && smeCheck.ConnectionStatus == "Yes")
            {
                response.code = SmeErrorCodes.SME_IS_INACONNECTION;
                response.message = "SME is in connection with the LynkiD SME ID " + smeCheck.LinkIDSmeId;
                return response;
            }
            
            // Create Connection in operator
            _logger.LogInformation($"[{sessionId}] Making call to OPERATOR, merchantid = {merchantId}, LoyaltyMemberCode = {input.SmeCif}");
            var resOperator = await _memberService.RewardCreateSmeConnection(new RewardCreateSmeConnectionInput()
            {
                SmeCif = input.SmeCif,
                MerchantId = merchantId,
                LicenseNumber = input.LicenseNumber,
                TaxNumber = input.TaxNumber,
                ConnectSource = SME_PARTNER_CODE,
                LoyaltyInfo = new LoyaltyInfo4Connect()
                {
                    Address = smeCheck.Address, ContactEmail = smeCheck.ContactEmail,
                    ContactPhone = smeCheck.ContactPhone, FullName = smeCheck.FullName, ShortName = smeCheck.ShortName, StartBusinessDate = smeCheck.StartBusinessDate
                }
            });
            _logger.LogInformation($"[{sessionId}] Making call to OPERATOR, res = {JsonConvert.SerializeObject(resOperator)}");
            var idToAck = resOperator.IdForAck ?? 0;

            // Create Connection for the SME
            _logger.LogInformation($"[{sessionId}] Making call to UTIL API");
            var utilRes = await PostAsync<UtilCreateSmeConnectionOutput>("Sme/CreateSmeConnection",
                new UtilCreateSmeConnectionInput()
                {
                    LIDSmeId = resOperator.LIDSmeId,
                    SmeCif = input.SmeCif,
                    MerchantId = merchantId,
                    LIDSmeCode = resOperator.LIDSmeCode,
                    LIDWalletAddress = resOperator.LIDWalletAddress,
                    RedeemSource = SME_PARTNER_CODE,
                    TenantId = tenantId,
                });
            if (utilRes == null || utilRes.ErrorCode != "00")
            {
                _logger.LogInformation($"[{sessionId}] Making call to UTIL API not successful! Res = {JsonConvert.SerializeObject(utilRes)}");
                var res = SmeErrorCodes.UtilErrorCodeConverter(utilRes?.ErrorCode ?? "100");
                response.code = res.code;
                response.message = res.message;
                return response;
            }
            _logger.LogInformation($"[{sessionId}] Making ACK call to UTIL API. AckId = {idToAck}");

            await _memberService.RewardAckSmeConnection(new RewardAckSmeConnectionInput()
            {
                LoyaltyMemberCode = input.SmeCif,
                MerchantId = merchantId,
                LoyaltyMemberLicenseNumber = input.LicenseNumber,
                AckId = idToAck
            });
            _logger.LogInformation($"[{sessionId}] AckId = {idToAck}! DONE ACK");

            response.code = "00";
            response.Result = new SmeConnectResult()
            {
                SmeId = utilRes.SmeId,
                LynkiDSmeId = resOperator.LIDSmeId,
            };
            _logger.LogInformation($"[{sessionId}] Done! Returning {JsonConvert.SerializeObject(response)}");
            return response;
        }



        private string RequestGenerator()
        {
            const int length = 20; // Length of the random string
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"; // Characters to choose from
            var random = new Random();
            var stringBuilder = new StringBuilder();

            for (var i = 0; i < length; i++)
            {
                var index = random.Next(chars.Length);
                stringBuilder.Append(chars[index]);
            }
            return stringBuilder.ToString();
        }

        public async Task<GetListAddressShipSmeOutput> GetListAddressShip(GetListAddressShipSmeInput input)
        {
            if (input.MaxResultCount <= 0)
            {
                input.MaxResultCount = 10;
            }
            if (input.SkipCount <= 0)
            {
                input.SkipCount = 0;
            }
            // LinkID đang xài location ship từ vendor urbox
            var vendorType = "Urbox";
            var req = new GetAllLocationInput()
            {
                CodeFilter = input.CodeFilter,
                IdFilter = input.IdFilter,
                LevelFilter = input.LevelFilter,
                MaxResultCount = input.MaxResultCount ?? 50,
                NameFilter = input.NameFilter,
                ParentCodeFilter = input.ParentCodeFilter,
                SkipCount = input.SkipCount ?? 0,
                VendorType = vendorType,
            };
            _logger.LogInformation($"SME_GetListAddressShip_Request:{JsonConvert.SerializeObject(req)}");
            var response = await _linkIdLoyaltyService.GetAllLocationShip(req);
            _logger.LogInformation($"SME_GetListAddressShip_Response:{JsonConvert.SerializeObject(response)}");
            if (response != null && response.Result != null)
            {
                return new GetListAddressShipSmeOutput()
                {
                    TotalCount = response.Result.TotalCount,
                    Items = response.Result.Items,
                    code = SmeErrorCodes.Success,
                    message = "Success",
                };
            }
            else
            {
                return new GetListAddressShipSmeOutput()
                {
                    TotalCount = 0,
                    Items = null,
                    code = SmeErrorCodes.UnknownError,
                    message = response?.Error ?? "Error Happened"
                };
            }
        }

        public async Task<GiftLocationSmeOutput> GetAllLocation(GiftLocationSmeInput input)
        {
            var result = new GiftLocationSmeOutput();
            if (input.MaxResultCount <= 0)
            {
                input.MaxResultCount = 10;
            }
            if (input.SkipCount <= 0)
            {
                input.SkipCount = 0;
            }
            var request = new MerchantGiftLocationInputDto()
            {
                MaxResultCount = input.MaxResultCount ?? 10,
                SkipCount = input.SkipCount ?? 0,
            };
            var response = await _linkIdLoyaltyService.GetAllLocation(request);
            if (response != null && response.Result != null)
            {
                var items = response.Result.Items.Select(x => new MerchantGiftLocationOutput()
                {
                    Code = x.Region?.Code,
                    Id = x.Region?.Id,
                    Name = x.Region?.Name,
                }).ToList();
                result = new GiftLocationSmeOutput()
                {
                    TotalCount = response.Result.TotalCount,
                    Items = items,
                    code = SmeErrorCodes.Success
                };
            }
            else
            {
                result = new GiftLocationSmeOutput()
                {
                    code = SmeErrorCodes.UnknownError,
                    message = response.Error
                };
            }
            return result;
        }

        public async Task<GiftListSmeOutput> GiftListWithoutMemberCode(GiftListSmeInput input)
        {
            var result = new GiftListSmeOutput();
            if (input.MaxResultCount <= 0)
            {
                input.MaxResultCount = 10;
            }
            if (input.SkipCount <= 0)
            {
                input.SkipCount = 0;
            }

            if (!string.IsNullOrEmpty(input.Keyword) && input.Keyword.Trim().Length < 3)
            {
                return new GiftListSmeOutput()
                {
                    code = SmeErrorCodes.SME_SearchKeyWordIsTooShort,
                    message = "Search keyword is too short"
                };
            }
            var request = new MerchantGetGiftWithoutMemberCodeInput()
            {
                Filter = input.Keyword,
                BrandIdFilter = input.BrandId,
                FromCointFilter = input.PriceFrom,
                ToCoinFilter = input.PriceTo,
                FullGiftCategoryCodeFilter = input.CategoryCode,
                MaxResultCount = input.MaxResultCount,
                SkipCount = input.SkipCount,
                IsEGiftFilter = true,
                PartnerCode = SME_PARTNER_CODE
            };

            var response = await _linkIdLoyaltyService.GiftListWithoutMemberCode(request);
            if (response != null && response.Result != null)
            {
                var items = response.Result.Items.Select(item => new MerchantGiftGetAllGiftOutput()
                {
                    Id = item.GiftInfor?.Id,
                    Code = item.GiftInfor?.Code,
                    CategoryCode = item.GiftInfor?.FullGiftCategoryCode,
                    Description = item.GiftInfor?.Introduce,
                    ImageLinks = (item.ImageLink == null || item.ImageLink.Count == 0) ? new List<MerchantGiftImageLinkShortDto>()
                        : item.ImageLink.Select(x => new MerchantGiftImageLinkShortDto()
                        {
                            FullLink = x.FullLink,
                            Link = x.Link,
                        }).ToList(),
                    InStock = item.GiftInfor?.RemainingQuantity,
                    Name = item.GiftInfor?.Name,
                    RequiredCoin = item.GiftInfor?.RequiredCoin,
                    ShortDescription = item.GiftInfor?.Introduce,
                    IsEgift = item.GiftInfor?.IsEGift,
                    Vendor = item.GiftInfor?.Vendor,
                    TotalWish = item.GiftInfor?.TotalWish ?? 0,
                    BrandName = item.GiftInfor?.BrandName,
                    ExpireDuration = item.GiftInfor?.ExpireDuration,
                }).ToList();
                result = new GiftListSmeOutput()
                {
                    TotalCount = response.Result.TotalCount,
                    Items = items,
                    code = SmeErrorCodes.Success,
                    message = "Success"
                };
            }
            else
            {
                result = new GiftListSmeOutput()
                {
                    code = SmeErrorCodes.UnknownError,
                    message = response.Error
                };
            }
            return result;
        }

        public async Task<GiftDetailSmeOutput> GiftDetail(GiftDetailSmeInput input)
        {
            var result = new GiftDetailSmeOutput();
            if (input.GiftId.HasValue == false)
            {
                result.code = SmeErrorCodes.SME_GiftId_IsRequired;
                result.message = "GiftId Is Required";
                return result;
            }
            var request = new MerchantGiftGetDetailWithoutMemberCodeInputDto
            {
                GiftId = input.GiftId.Value,
                PartnerCode = SME_PARTNER_CODE
            };

            var response = await _linkIdLoyaltyService.GiftDetailWithoutMemberCode(request);


            if (response.Result != null && response.Result.ErrorCode == null && response.Result.GiftInfor != null)
            {
                var item = response.Result;

                var images = (item.ImageLink == null || item.ImageLink.Count == 0) ? new List<MerchantGiftImageLinkShortDto>() : item.ImageLink.Select(x => new MerchantGiftImageLinkShortDto()
                {
                    FullLink = x.FullLink,
                    Link = x.Link,
                }).ToList();

                result = new GiftDetailSmeOutput()
                {
                    code = SmeErrorCodes.Success,
                    message = "Success",
                    GiftDetail = new GiftDetailSmeOutputInner()
                    {
                        Id = item.GiftInfor.Id,
                        Code = item.GiftInfor.Code,
                        CategoryCode = item.GiftInfor?.FullGiftCategoryCode,
                        Description = item.GiftInfor?.Introduce,
                        ImageLinks = images,
                        InStock = item.GiftInfor?.RemainingQuantity,
                        Name = item.GiftInfor?.Name,
                        RequiredCoin = item.GiftInfor?.RequiredCoin,
                        IsEgift = item.GiftInfor?.IsEGift,
                        VendorHotline = item.GiftInfor?.VendorHotline,
                        Vendor = item.GiftInfor?.Vendor,
                        BrandAddress = item.GiftInfor?.BrandAddress,
                        BrandDescription = item.GiftInfor?.BrandDescription,
                        BrandName = item.GiftInfor?.BrandName,
                        BrandLinkLogo = item.GiftInfor?.BrandLinkLogo,
                        VendorDescription = item.GiftInfor?.VendorDescription,
                        VendorImage = item.GiftInfor?.VendorImage
                    },
                };
                this._logger.LogInformation(" >> Get gift detail >> Happycase!");
                return result;
            }

            if (response.Result == null)
            {
                result.code = SmeErrorCodes.UnknownError;
                result.message = "Unknown error";
                return result;
            }

            var loyError  = SmeErrorCodes.LinkIDLoyatltyErrorCode(response.Result.ErrorCode);
            result.code = loyError.code;
            result.message = loyError.message;
            return result;
        }
    }
}