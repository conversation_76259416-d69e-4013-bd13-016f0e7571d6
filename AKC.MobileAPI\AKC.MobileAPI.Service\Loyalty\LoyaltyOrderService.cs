﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Order;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyOrderService : BaseLoyaltyService, ILoyaltyOrderService
    {
        public LoyaltyOrderService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<LoyaltyPurchaseAgentOutput> PurchaseAgent(LoyaltyPurchaseAgentInput input)
        {
            return await PostLoyaltyAsync<LoyaltyPurchaseAgentOutput>(LoyaltyApiUrl.ORDER_PURCHASEAGENT, input);
        }

        public async Task<LoyaltyResponse<string>> UsePoint(LoyaltyUsePointInput input)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<string>>(LoyaltyApiUrl.ORDER_USEPOINT, input);
        }
    }
}
