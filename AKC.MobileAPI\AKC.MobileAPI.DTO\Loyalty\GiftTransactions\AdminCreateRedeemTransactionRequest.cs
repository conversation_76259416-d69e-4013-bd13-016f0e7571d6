﻿using System.ComponentModel.DataAnnotations;

namespace AKC.MobileAPI.DTO.Loyalty.GiftTransactions
{
    public class AdminCreateRedeemTransactionRequest
    {
        public string Cif { get; set; }
        public string GiftCode { get; set; }
        public string GiftName { get; set; }
        public string OfferType { get; set; }
        [Required]
        public int? Quantity { get; set; }
        [Required]
        public decimal TotalAmount { get; set; }
        [Required]
        public string Date { get; set; }
        public string RedeemSource { get; set; }
        public int? MerchantIdRedeem { get; set; }
        public string TransactionCode { get; set; }
        public string MemberCode { get; set; }
    }
}
