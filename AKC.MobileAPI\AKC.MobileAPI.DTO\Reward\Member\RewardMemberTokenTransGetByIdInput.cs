﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberTokenTransGetByIdInput
    {
        public string Sorting { get; set; }
        public int? MaxResultCount { get; set; }
        public int? SkipCount { get; set; }
        public string MemberCode { get; set; }
        public DateTime? FromDateFilter { get; set; }
        public DateTime? ToDateFilter { get; set; }
    }
}
