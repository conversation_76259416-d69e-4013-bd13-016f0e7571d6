﻿using AKC.MobileAPI.DTO.Base;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Member
{
    public class TransactionDto
    {
        public string Code { get; set; }
        [JsonConverter(typeof(CustomDateTimeConverter))]
        public DateTime? CreationTime { get; set; }
        [JsonConverter(typeof(CustomDateTimeConverter))]
        public DateTime? BusinessTime { get; set; }
        public string MemberCode { get; set; }
        public decimal TokenAmount { get; set; }
        public string OriginalOrders { get; set; }
        public string Status { get; set; }
        
        public string FromWalletAddress { get; set; }
        public string ToWalletAddress { get; set; }
        public string ActionType { get; set; }
        public string ActionCode { get; set; }
        public string ActionTypeDescription { get; set; }
    }
}
