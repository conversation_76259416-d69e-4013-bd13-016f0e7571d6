D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AKC.MobileAPI.deps.json
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AKC.MobileAPI.runtimeconfig.json
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AKC.MobileAPI.runtimeconfig.dev.json
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\appsettings.Development.json
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\appsettings.json
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\App_Data\adminSDKKey.json
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AKC.MobileAPI.exe
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\xunit.runner.visualstudio.dotnetcore.testadapter.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\xunit.runner.reporters.netcoreapp10.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\xunit.runner.utility.netcoreapp10.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\log4net-audit.config
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\log4net.config
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AKC.MobileAPI.Test.deps.json
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AKC.MobileAPI.Test.runtimeconfig.json
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AKC.MobileAPI.Test.runtimeconfig.dev.json
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AKC.MobileAPI.Test.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AKC.MobileAPI.Test.pdb
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AspNetCore.TotpGenerator.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AWSSDK.Core.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AWSSDK.S3.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Azure.Core.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Azure.Storage.Blobs.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Azure.Storage.Common.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Castle.Core.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Crc32.NET.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Cronos.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\FirebaseAdmin.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Gelf.Extensions.Logging.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Google.Api.Gax.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Google.Api.Gax.Rest.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Google.Apis.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Google.Apis.Auth.PlatformServices.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Google.Apis.Auth.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Google.Apis.Core.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\log4net.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\System.Net.Http.Formatting.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\System.Web.Http.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Authentication.JwtBearer.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Bcl.AsyncInterfaces.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.VisualStudio.CodeCoverage.Shim.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Data.SqlClient.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Relational.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.SqlServer.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Caching.Abstractions.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Abstractions.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Binder.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.EnvironmentVariables.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.FileExtensions.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Json.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.FileProviders.Physical.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.FileSystemGlobbing.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Hosting.Abstractions.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.Abstractions.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.Log4Net.AspNetCore.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Options.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Primitives.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Identity.Client.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.JsonWebTokens.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Logging.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Tokens.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.IO.RecyclableMemoryStream.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.OpenApi.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.TestPlatform.CoreUtilities.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.TestPlatform.PlatformAbstractions.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.VisualStudio.TestPlatform.ObjectModel.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.TestPlatform.CommunicationUtilities.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.TestPlatform.CrossPlatEngine.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.TestPlatform.Utilities.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.VisualStudio.TestPlatform.Common.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\testhost.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Minio.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Newtonsoft.Json.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Newtonsoft.Json.Bson.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\BouncyCastle.Crypto.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\QRCoder.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Quartz.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\RabbitMQ.Client.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\RestSharp.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.Swagger.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerGen.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerUI.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\System.Configuration.ConfigurationManager.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\System.Data.SqlClient.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\System.IdentityModel.Tokens.Jwt.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\System.Interactive.Async.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\System.Reactive.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\System.Reactive.Linq.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\System.Runtime.Caching.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\System.Security.Cryptography.ProtectedData.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\System.Xml.XPath.XmlDocument.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.WindowsAzure.Storage.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\xunit.abstractions.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\xunit.assert.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\xunit.core.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\xunit.execution.dotnet.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\cs\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\cs\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\de\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\de\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\es\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\es\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\fr\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\fr\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\it\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\it\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\ja\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\ja\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\ko\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\ko\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\pl\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\pl\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\pt-BR\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\pt-BR\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\ru\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\ru\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\tr\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\tr\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\zh-Hans\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\zh-Hans\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\zh-Hant\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\zh-Hant\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\cs\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\cs\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\cs\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\de\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\de\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\de\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\es\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\es\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\es\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\fr\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\fr\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\fr\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\it\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\it\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\it\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\ja\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\ja\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\ja\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\ko\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\ko\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\ko\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\pl\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\pl\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\pl\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\pt-BR\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\pt-BR\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\pt-BR\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\ru\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\ru\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\ru\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\tr\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\tr\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\tr\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\zh-Hans\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\zh-Hans\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\zh-Hans\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\zh-Hant\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\zh-Hant\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\zh-Hant\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\runtimes\unix\lib\netcoreapp2.1\Microsoft.Data.SqlClient.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\runtimes\win\lib\netcoreapp2.1\Microsoft.Data.SqlClient.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\runtimes\win-arm64\native\sni.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\runtimes\win-x64\native\sni.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\runtimes\win-x86\native\sni.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\runtimes\unix\lib\netcoreapp2.1\System.Data.SqlClient.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\runtimes\win\lib\netcoreapp2.1\System.Data.SqlClient.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\runtimes\unix\lib\netcoreapp2.0\System.Runtime.Caching.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\runtimes\win\lib\netcoreapp2.0\System.Runtime.Caching.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\runtimes\win\lib\netstandard2.0\System.Security.Cryptography.ProtectedData.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AKC.MobileAPI.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AKC.MobileAPI.DTO.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AKC.MobileAPI.Service.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AKC.RabbitMQ.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AKC.MobileAPI.pdb
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AKC.MobileAPI.xml
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AKC.MobileAPI.DTO.pdb
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AKC.MobileAPI.Service.pdb
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\AKC.RabbitMQ.pdb
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\obj\Debug\netcoreapp3.1\AKC.MobileAPI.Test.csproj.AssemblyReference.cache
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\obj\Debug\netcoreapp3.1\AKC.MobileAPI.Test.GeneratedMSBuildEditorConfig.editorconfig
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\obj\Debug\netcoreapp3.1\AKC.MobileAPI.Test.AssemblyInfoInputs.cache
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\obj\Debug\netcoreapp3.1\AKC.MobileAPI.Test.AssemblyInfo.cs
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\obj\Debug\netcoreapp3.1\AKC.MobileAPI.Test.csproj.CoreCompileInputs.cache
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\obj\Debug\netcoreapp3.1\AKC.Mobi.2C531713.Up2Date
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\obj\Debug\netcoreapp3.1\AKC.MobileAPI.Test.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\obj\Debug\netcoreapp3.1\AKC.MobileAPI.Test.pdb
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\obj\Debug\netcoreapp3.1\AKC.MobileAPI.Test.genruntimeconfig.cache
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Caching.StackExchangeRedis.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\Pipelines.Sockets.Unofficial.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\StackExchange.Redis.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\System.ComponentModel.Annotations.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\System.IO.Pipelines.dll
D:\working\vporg-api\AKC.MobileAPI\AKC.MobileAPI.Test\bin\Debug\netcoreapp3.1\System.Runtime.CompilerServices.Unsafe.dll
