﻿using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.Reward.Member;
using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Member
{
    public class RewardViewCreditBalanceInput
    {
        public string WalletAddress { get; set; }
    }

    public class RewardViewCreditBalanceOutput
    {
        public int Result { get; set; }
        public GetCreditBalanceByWalletAddressItem Items { get; set; }
        public string MessageDetail { get; set; }
        public string Message { get; set; }
    }

    public class GetCreditBalanceByWalletAddressItem
    {
        public string WalletAddress { get; set; }
        public decimal CreditBalance { get; set; }
    }
    public class RewardViewPointWithGrantTypeOutput
    {
        public int Result { get; set; }
        public RewardViewPointWithGrantTypeDetail Items { get; set; }
        public string MessageDetail { get; set; }
        public string Message { get; set; }
    }

    public class RewardViewPointWithGrantTypeDetail
    {
        public string WalletAddress { get; set; }
        public decimal TotalToken { get; set; }
        public List<ViewCoinResponse> GrantTypeBalance { get; set; }
    }
    
    public class ViewBalanceByWalletAddressOutput
    {
        public int Result { get; set; }
        public ViewBalanceByWalletAddressDetail Items { get; set; }
        public string MessageDetail { get; set; }
        public string Message { get; set; }
    }

    public class ViewBalanceByWalletAddressDetail
    {
        public decimal Balance { get; set; }
        public decimal? ExpiringAmount { get; set; }
        public string ExpiringDate { get; set; }
    }
}
