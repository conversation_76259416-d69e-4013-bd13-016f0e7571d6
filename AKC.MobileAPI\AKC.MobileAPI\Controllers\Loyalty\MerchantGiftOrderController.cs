﻿using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Loyalty.Transaction;
using AKC.MobileAPI.DTO.MerchantGift;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.LoyaltyVendorGift;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/Orders")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class MerchantGiftOrderController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IMerchantGiftService _merchantGiftService;
        private readonly ILoyaltyUtilsService _loyaltyUtilsService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILinkIdLoyaltyVendorGiftService _linkIdLoyaltyVendorGiftService;
        public MerchantGiftOrderController(
            ILogger<MerchantGiftController> logger,
            IMerchantGiftService merchantGiftService,
            ILoyaltyUtilsService loyaltyUtilsService,
            IExceptionReponseService exceptionReponseService,
            ILinkIdLoyaltyVendorGiftService linkIdLoyaltyVendorGiftService)
        {
            _logger = logger;
            _merchantGiftService = merchantGiftService;
            _loyaltyUtilsService = loyaltyUtilsService;
            _exceptionReponseService = exceptionReponseService;
            _linkIdLoyaltyVendorGiftService = linkIdLoyaltyVendorGiftService;
        }

        [HttpGet]
        [Route("TransactionHistory")]
        public async Task<ActionResult<LoyaltyResponseList<MerchantGiftTransactionHistoryOutput>>> TransactionHistory([FromQuery] MerchantGiftTransactionHistoryInput input)
        {
            try
            {
                return await _merchantGiftService.TransactionHistory(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Merchant gift get transaction history - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpGet]
        [Route("TransactionDetail")]
        public async Task<ActionResult<LoyaltyResponse<MerchantGiftTransactionDetailOutput>>> TransactionDetail([FromQuery] MerchantGiftTransactionDetailInput input)
        {
            try
            {
                return await _merchantGiftService.TransactionDetail(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Merchant gift get transaction detail - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("CreateTransaction")]
        public async Task<ActionResult<MerchantGiftSendOtpCreateRedeemOutput>> SendOtpCreateTransaction(MerchantGiftSendOtpCreateRedeemInput input)
        {
            try
            {
                var result = await _merchantGiftService.SendOtpCreateRedeem(input);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Send otp redeem transaction " + input.CifCode + "_Error" + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("AdminBuyGiftForMember")]
        public async Task<ActionResult<AdminBuyGiftForMemberOutput>> AdminBuyGiftForMember(AdminBuyGiftForMemberInput input)
        {
            try
            {
                _logger.LogInformation(">> AdminBuyGiftForMember >> " + JsonConvert.SerializeObject(input));
                var result = await _merchantGiftService.AdminBuyGiftForMember(input);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogInformation("AdminBuyGiftForMember " + input.CifCode + "_Error" + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("ConfirmOtpCreateTransaction")]
        public async Task<ActionResult<MerchantGiftCreateRedeemOutput>> CreateRedeemTransaction(MerchantGiftCreateRedeemInput input)
        {
            try
            {
                var result = await _merchantGiftService.CreateRedeem(input);
                _logger.LogInformation("redeem transaction " + input.CifCode + "_Response" + JsonConvert.SerializeObject(result));
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogInformation("redeem transaction " + input.CifCode + "_Error" + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpGet]
        [Route("GetVouchersCardZone247")]
        public async Task<ActionResult<LoyaltyResponseList<MerchantGiftTransactionHistoryOutput>>> GetVouchersCardZone247([FromQuery] MerchantGiftTransactionHistoryInput input)
        {
            try
            {
                _logger.LogInformation($"GetVouchersCardZone247_Request:{JsonConvert.SerializeObject(input)}");
                return await _merchantGiftService.GetVouchersCardZone247(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Merchant gift get GetVouchersCardZone247 - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("AdminCreateRedeemTransaction")]
        public async Task<ActionResult<AdminCreateRedeemTransactionResponse>> AdminCreateRedeemTransaction([FromBody] AdminCreateRedeemTransactionRequest request)
        {
            try
            {
                _logger.LogInformation($"AdminCreateRedeemTransaction_Input:{JsonConvert.SerializeObject(request)}");
                var result = await _merchantGiftService.AdminCreateRedeemTransaction(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"AdminCreateRedeemTransaction - {JsonConvert.SerializeObject(ex)}");
                return BadRequest(new { ErrorCode = "400", Message = ex.Message });
            }
        }

        [HttpPost]
        [Route("AdminGiveGiftToMember")]
        public async Task<ActionResult<AdminGiveGiftToMemberResponse>> AdminGiveGiftToMember([FromBody] AdminGiveGiftToMemberRequest request)
        {
            try
            {
                _logger.LogInformation($"AdminGiveGiftToMember_Input:{JsonConvert.SerializeObject(request)}");
                var result = await _merchantGiftService.AdminGiveGiftToMember(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"AdminGiveGiftToMember - {JsonConvert.SerializeObject(ex)}");
                return BadRequest(new { ErrorCode = "400", Message = ex.Message });
            }
        }

        [HttpPost]
        [Route("GetAllGiftRedeemTransactionByGiftCategoryChannel_V1")]
        public async Task<ActionResult<GetAllGiftTransactionByGiftCategoryChannelResponse>> GetAllGiftRedeemTransactionByGiftCategoryChannel_V1([FromBody] GetAllGiftTransactionByGiftCategoryChannelInput request)
        {
            try
            {
                _logger.LogInformation($"GetAllGiftRedeemTransactionByGiftCategoryChannel_V1_Input:{JsonConvert.SerializeObject(request)}");
                var result = await _loyaltyUtilsService.GetAllGiftRedeemTransactionByGiftCategoryChannel_V1(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"GetAllGiftRedeemTransactionByGiftCategoryChannel_V1 - {JsonConvert.SerializeObject(ex)}");
                return BadRequest(new { ErrorCode = "400", Message = ex.Message });
            }
        }

        [HttpPost]
        [Route("redeem-with-cash")]
        public async Task<ActionResult<RedeemTransactionWithCashResponse>> RedeemTransactionWithCard([FromBody] RedeemTransactionWithCashRequest request)
        {
            try
            {
                _logger.LogInformation($"RedeemTransactionWithCard_Input:{JsonConvert.SerializeObject(request)}");
                var result = await _linkIdLoyaltyVendorGiftService.CreateRedeemTransactionWithCash(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"RedeemTransactionWithCard - {JsonConvert.SerializeObject(ex)}");
                if (ex.GetType() == typeof(RewardDataExceptionResponse))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }
    }
}

