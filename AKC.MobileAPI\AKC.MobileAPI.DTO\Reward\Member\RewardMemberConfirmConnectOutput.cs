﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberConfirmConnectOutput
    {
        public int Result { get; set; }
        public string TargetUrl { get; set; }
        public Dictionary<string, int> ExtraInfo { get; set; }
        public bool Success { get; set; } = true;
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class RewardMemberConfirmConnectOutputDto
    {
        public int Result { get; set; }
        public string Message { get; set; }
        public RewardMemberConfirmConnectItemDto Item { get; set; }
    }

    public class RewardMemberConfirmConnectItemDto
    {
        public int MemberId { get; set; }
        public string WalletAddress { get; set; }
        public string MemberCode { get; set; }
    }
}
