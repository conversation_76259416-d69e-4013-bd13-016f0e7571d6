﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
	public class GetByIdAndRelatedGiftOutput
	{
		public ListResultGetByIdAndRelatedGift Result { get; set; }
		public string TargetUrl { get; set; }
		public bool Success { get; set; }
		public string Error { get; set; }
		public bool UnAuthorizedRequest { get; set; }
		public bool __abp { get; set; }
	}
	
	public class ListResultGetByIdAndRelatedGift
	{
        public GiftShortInforDto GiftInfor { get; set; }

        public List<ImageLinkDto> ImageLink { get; set; }

        public List<GiftShortInforForView> RelatedGiftInfor { get; set; }
		public string ErrorCode { get; set; }
	}
}
