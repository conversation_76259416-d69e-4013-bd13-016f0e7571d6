﻿using AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Reward
{
    public interface IRewardCreateExchangeAndRedeemService
    {
        Task<RewardCreateExchangeAndRedeemTransactionOutput> CreateExchangeAndRedeemTransaction(RewardCreateExchangeAndRedeemTransactionInput input, HttpContext httpContext);
    }
}
