﻿using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.CronServices;
using AKC.MobileAPI.Service.Exceptions;
using Cronos;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class BaseAdapterService : IBaseAdapterService
    {

        protected readonly HttpClient _client = new HttpClient();
        protected readonly IConfiguration _configuration;
        private readonly IDistributedCache _cache;
        protected readonly string baseURL;
        protected readonly string defaultUserName;
        protected readonly string defaultPassowrd;
        public BaseAdapterService(IConfiguration configuration, IDistributedCache cache)
        {
            _client.Timeout = TimeSpan.FromSeconds(100);
            _configuration = configuration;
            _cache = cache;
            baseURL = _configuration.GetSection("AdapterMasterCardAPI:RemoteURL").Value;
            defaultUserName = _configuration.GetSection("AdapterMasterCardAPI:Username").Value;
            defaultPassowrd = _configuration.GetSection("AdapterMasterCardAPI:Password").Value;
        }
        /// <summary>
        /// Perform a GET request to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> GetAdapterAsync<T>(string apiURL, object query = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }
            var requestURL = $"{baseURL}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }
            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Get
            };
            var token = GetAccessToken();
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            req.RequestUri = new Uri(requestURL);
            var cts = new CancellationTokenSource();
            try
            {
                //_logger.LogInformation("Call API Adapter request: " + JsonConvert.SerializeObject(req));
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();
                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    //_logger.LogError("Call API Adapter Error: " + rawData);
                    var ex = new AdapterException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }
                //_logger.LogInformation("Call API Adapter response: " + rawData);
                response.EnsureSuccessStatusCode();
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }
        /// <summary>
        /// Perform a DELETE obj to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> DeleteAdapterAsync<T>(string apiURL, object query = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var requestURL = $"{baseURL}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Delete
            };

            var token = GetAccessToken();
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            req.RequestUri = new Uri(requestURL);
            var cts = new CancellationTokenSource();
            try
            {
                //_logger.LogInformation("Call API Adapter request: " + JsonConvert.SerializeObject(req));
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();
                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized
                if (response.IsSuccessStatusCode == false)
                {
                    //_logger.LogError("Call API Adapter Error: " + rawData);
                    var ex = new AdapterException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }
                //_logger.LogInformation("Call API Adapter response: " + rawData);
                response.EnsureSuccessStatusCode();
                // Convert response to result object which is a instance of 'T'.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }
        /// <summary>
        /// Convert a object to query string format.
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        private string GetQueryString(object obj)
        {
            var properties = obj.GetType().GetProperties()
                .Where(x => x.CanRead)
                .Where(x => x.GetValue(obj, null) != null)
                .Select(x => new { x.Name, Value = x.GetValue(obj, null) })
                .ToList();

            // Get names for all IEnumerable properties (excl. string)
            var propertyNames = properties
                .Where(x => !(x.Value is string) && x.Value is IEnumerable)
                .Select(x => x.Name)
                .ToList();
            // Concat all IEnumerable properties into a comma separated string
            foreach (var key in propertyNames)
            {
                var objectOfKey = properties.FirstOrDefault(x => x.Name == key);
                var valueType = objectOfKey.Value.GetType();
                var valueElemType = valueType.IsGenericType
                                        ? valueType.GetGenericArguments()[0]
                                        : valueType.GetElementType();
                if (valueElemType.IsPrimitive || valueElemType == typeof(string))
                {
                    var enumerable = objectOfKey.Value as IEnumerable;

                    properties.Remove(objectOfKey);

                    foreach (var item in enumerable)
                    {
                        properties.Add(new { Name = key, Value = item });
                    }
                    //properties[key] = string.Join($"&{key}=", enumerable.Cast<object>());
                }
            }

            // Concat all key/value pairs into a string separated by ampersand
            return string.Join("&", properties
                .Select(x => string.Concat(
                    Uri.EscapeDataString(x.Name), "=",
                    Uri.EscapeDataString(x.Value.ToString()))));
        }
        public async Task<T> PostAdapterAsync<T>(string apiURL, object body = null, HttpContext request = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }
            var Client_Request_Address = request != null && request.Request.Headers.ContainsKey("Client-Request-Address") ?
                                            request?.Request.Headers["Client-Request-Address"].ToString() : request?.Connection.RemoteIpAddress.ToString();
            req.Headers.Add("Client-Request-Address", Client_Request_Address);
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", GetAccessToken());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                //_logger.LogInformation("Call API Adapter request: " + JsonConvert.SerializeObject(req));
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();
                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);
                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    //_logger.LogError("Call API Adapter Error: " + rawData);
                    var ex = new AdapterException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }
                //_logger.LogInformation("Call API Adapter response: " + rawData);
                response.EnsureSuccessStatusCode();
                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<T> PutAdapterAsync<T>(string apiURL, object body = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Put
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }

            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", GetAccessToken());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                //_logger.LogInformation("Call API Adapter request: " + JsonConvert.SerializeObject(req));
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();
                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);
                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    //_logger.LogError("Call API Adapter Error: " + rawData);
                    var ex = new AdapterException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }
                //_logger.LogInformation("Call API Adapter response: " + rawData);
                response.EnsureSuccessStatusCode();
                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }
        /// <summary>
        /// Get a new accessToken form Loyalty.
        /// </summary>
        /// <returns></returns>
        private string GetAccessToken(bool mustResetCache = false)
        {
            var token = _cache.GetString(CommonConstants.ACCESSS_TOKEN_MASTER_CARD_CACHE_KEY);
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var expression = CronExpression.Parse(configuration.GetSection("AdapterMasterCardAPI:CronExpressionRefreshToken").Value);
            var timeZoneInfo = TimeZoneInfo.Local;
            // Request body
            if (string.IsNullOrEmpty(token) || mustResetCache)
            {
                return RenewAccessTokenCacheValue(_cache, CronHelper.GetDelayToNextRefreshToken(expression, timeZoneInfo), mustResetCache);
            }
            return token;
        }
        private string RenewAccessTokenCacheValue(IDistributedCache cache, TimeSpan delay, bool isForceRenew = false)
        {
            return isForceRenew || string.IsNullOrEmpty(cache.GetString(CommonConstants.ALLOW_RENEW_MASTER_CARD_TOKEN))
                ? RenewCacheAndGetToken(cache, CommonConstants.ALLOW_RENEW_MASTER_CARD_TOKEN, CommonConstants.ACCESSS_TOKEN_MASTER_CARD_CACHE_KEY, LoginAdapter(baseURL, defaultUserName, defaultPassowrd), delay)
                : cache.GetString(CommonConstants.ACCESSS_TOKEN_MASTER_CARD_CACHE_KEY);
        }
        private string RenewCacheAndGetToken(IDistributedCache cache,
           string allowRenewByJobCacheKey,
           string accessTokenCacheKey,
           AdapterLoginResponse tokenInfo,
           TimeSpan delay)
        {
            // Cache flag allow refresh token (for cluster case).
            var cacheAllowRenewTokenFlagCacheOptions = new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromSeconds(delay.TotalSeconds - 1));
            // Not allow refresh token until allowRenewByJobFlag expired
            cache.SetString(allowRenewByJobCacheKey, allowRenewByJobCacheKey, cacheAllowRenewTokenFlagCacheOptions);
            var cacheAccessTokenOptions = new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromSeconds(3600));
            cache.SetString(accessTokenCacheKey, tokenInfo?.access_token, cacheAccessTokenOptions);
            // Set accesstoken after renew.
            return tokenInfo.access_token;
        }
        private AdapterLoginResponse LoginAdapter(string baseURL, string userName, string password)
        {
            var body = JsonConvert.SerializeObject(new
            {
                username = userName,
                password = password
            }, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            });
            // Setup request.
            var req = new HttpRequestMessage { Method = HttpMethod.Post };
            req.RequestUri = new Uri($"{baseURL}/auth/login");
            req.Content = new StringContent(body, Encoding.UTF8, "application/json");
            // Get Response.
            HttpClient _client = new HttpClient();
            var response = _client.SendAsync(req).Result;
            var rawData = response.Content.ReadAsStringAsync().Result;
            // Make sure success status code.
            response.EnsureSuccessStatusCode();
            // Get respone result.
            return JsonConvert.DeserializeObject<AdapterLoginResponse>(rawData);
        }
        //Clone a HttpRequest
        private HttpRequestMessage CloneHttpRequest(HttpRequestMessage req)
        {
            HttpRequestMessage clone = new HttpRequestMessage(req.Method, req.RequestUri);

            clone.Content = req.Content;
            clone.Version = req.Version;

            foreach (KeyValuePair<string, object> prop in req.Properties)
            {
                clone.Properties.Add(prop);
            }

            foreach (KeyValuePair<string, IEnumerable<string>> header in req.Headers)
            {
                clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            return clone;
        }
    }
}
