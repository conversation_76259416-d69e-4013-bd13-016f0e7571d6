﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Text;

namespace AKC.MobileAPI.Service.Exceptions
{
    public class LoyaltyException : Exception
    {
        public LoyaltyException()
        {
        }

        public LoyaltyException(string message)
            : base(message)
        {
        }

        public LoyaltyException(string message, Exception inner)
            : base(message, inner)
        {
        }
    }

    /// <summary>
    /// Exception thrown when API request times out
    /// </summary>
    public class LoyaltyTimeoutException : LoyaltyException
    {
        public string ApiUrl { get; }
        public TimeSpan? Timeout { get; }

        public LoyaltyTimeoutException(string apiUrl, TimeSpan? timeout = null)
            : base($"Request to {apiUrl} timed out{(timeout.HasValue ? $" after {timeout.Value.TotalSeconds} seconds" : "")}")
        {
            ApiUrl = apiUrl;
            Timeout = timeout;
        }

        public LoyaltyTimeoutException(string apiUrl, Exception innerException, TimeSpan? timeout = null)
            : base($"Request to {apiUrl} timed out{(timeout.HasValue ? $" after {timeout.Value.TotalSeconds} seconds" : "")}", innerException)
        {
            ApiUrl = apiUrl;
            Timeout = timeout;
        }
    }

    /// <summary>
    /// Exception thrown when API returns Gateway Timeout (504)
    /// </summary>
    public class LoyaltyGatewayTimeoutException : LoyaltyException
    {
        public string ApiUrl { get; }

        public LoyaltyGatewayTimeoutException(string apiUrl)
            : base($"Gateway timeout occurred for API: {apiUrl}")
        {
            ApiUrl = apiUrl;
        }

        public LoyaltyGatewayTimeoutException(string apiUrl, Exception innerException)
            : base($"Gateway timeout occurred for API: {apiUrl}", innerException)
        {
            ApiUrl = apiUrl;
        }
    }

    /// <summary>
    /// Exception thrown when API request is cancelled
    /// </summary>
    public class LoyaltyCancelledException : LoyaltyException
    {
        public string ApiUrl { get; }

        public LoyaltyCancelledException(string apiUrl)
            : base($"Request to {apiUrl} was cancelled")
        {
            ApiUrl = apiUrl;
        }

        public LoyaltyCancelledException(string apiUrl, Exception innerException)
            : base($"Request to {apiUrl} was cancelled", innerException)
        {
            ApiUrl = apiUrl;
        }
    }
    /// <summary>
    /// Exception thrown when API returns HTTP error status codes
    /// </summary>
    public class LoyaltyHttpException : LoyaltyException
    {
        public HttpStatusCode StatusCode { get; }
        public string ApiUrl { get; }
        public string ResponseContent { get; }

        public LoyaltyHttpException(HttpStatusCode statusCode, string apiUrl, string responseContent)
            : base($"API {apiUrl} returned {(int)statusCode} {statusCode}: {responseContent}")
        {
            StatusCode = statusCode;
            ApiUrl = apiUrl;
            ResponseContent = responseContent;
        }

        public LoyaltyHttpException(HttpStatusCode statusCode, string apiUrl, string responseContent, Exception innerException)
            : base($"API {apiUrl} returned {(int)statusCode} {statusCode}: {responseContent}", innerException)
        {
            StatusCode = statusCode;
            ApiUrl = apiUrl;
            ResponseContent = responseContent;
        }
    }

    /// <summary>
    /// Exception thrown when API returns Unauthorized (401)
    /// </summary>
    public class LoyaltyUnauthorizedException : LoyaltyHttpException
    {
        public LoyaltyUnauthorizedException(string apiUrl, string responseContent)
            : base(HttpStatusCode.Unauthorized, apiUrl, responseContent)
        {
        }

        public LoyaltyUnauthorizedException(string apiUrl, string responseContent, Exception innerException)
            : base(HttpStatusCode.Unauthorized, apiUrl, responseContent, innerException)
        {
        }
    }

    /// <summary>
    /// Exception thrown when API returns Bad Request (400)
    /// </summary>
    public class LoyaltyBadRequestException : LoyaltyHttpException
    {
        public LoyaltyBadRequestException(string apiUrl, string responseContent)
            : base(HttpStatusCode.BadRequest, apiUrl, responseContent)
        {
        }

        public LoyaltyBadRequestException(string apiUrl, string responseContent, Exception innerException)
            : base(HttpStatusCode.BadRequest, apiUrl, responseContent, innerException)
        {
        }
    }

    /// <summary>
    /// Exception thrown when network-related errors occur
    /// </summary>
    public class LoyaltyNetworkException : LoyaltyException
    {
        public string ApiUrl { get; }

        public LoyaltyNetworkException(string apiUrl, Exception innerException)
            : base($"Network error occurred while calling API: {apiUrl}", innerException)
        {
            ApiUrl = apiUrl;
        }
    }
}
