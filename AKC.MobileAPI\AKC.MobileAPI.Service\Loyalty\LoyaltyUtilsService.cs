﻿using System.Threading.Tasks;
using System;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.LoyaltyVpbank;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Loyalty.Transaction;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyUtilsService : BaseLoyaltyUtilService, ILoyaltyUtilsService
    {
        public LoyaltyUtilsService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }
        public async Task<CreateUnConfirmConnectionOutput> CreateUnConfirmConnection(CreateUnConfirmConnectionInput input)
        {
            return await PostLoyaltyAsync<CreateUnConfirmConnectionOutput>(LoyaltyApiUrl.CreateUnConfirmConnection, input);
        }

        public async Task<CreateConfirmedConnectionOutput> CreateConfirmedConnection(
            CreateConfirmedConnectionInput input)
        {
            return await PostLoyaltyAsync<CreateConfirmedConnectionOutput>(LoyaltyApiUrl.CreateConfirmedConnection, input);
        }

        public async Task<GetMemberInfoByCifOutput> GetMemberInfoByCif(GetMemberInfoByCifInput input)
        {
            return await PostLoyaltyAsync<GetMemberInfoByCifOutput>(LoyaltyApiUrl.GetMemberInfoByCif, input);
        }

        public async Task<GetMemberInfoByCifForAdapterOutput> GetMemberInfoByCifForAdapter(
            GetMemberInfoByCifForAdapterInput input)
        {
            return await PostLoyaltyAsync<GetMemberInfoByCifForAdapterOutput>(LoyaltyApiUrl.GetMemberInfoByCif, input);
        }

        public async Task<UpdateMemberFromAdapterOutput> UpdateMemberFromAdapter(UpdateMemberFromAdapterInput input)
        {
            return await PostLoyaltyAsync<UpdateMemberFromAdapterOutput>(LoyaltyApiUrl.UpdateMemberFromAdapter, input);
        }

        public async Task<AdminCreateRedeemTransactionResponse> AdminCreateRedeemTransaction(AdminCreateRedeemTransactionRequest input)
        {
            return await PostLoyaltyAsync<AdminCreateRedeemTransactionResponse>(LoyaltyApiUrl.AdminCreateRedeemTransaction, input);
        }

        public async Task<GetAllBirthDayTransactionOutput> GetAllBirthDayTransaction(GetAllBirthDayTransactionInput input)
        {
            return await PostLoyaltyAsync<GetAllBirthDayTransactionOutput>(LoyaltyApiUrl.GetAllBirthDayTransaction, input);
        }

        public async Task<AdminCreateRedeemTransactionResponse> AdminGiveGiftToMember(AdminCreateRedeemTransactionRequest input)
        {
            return await PostLoyaltyAsync<AdminCreateRedeemTransactionResponse>(LoyaltyApiUtilsUrl.AdminGiveGiftToMember, input);
        }

        public async Task<GetAllGiftTransactionByGiftCategoryChannelResponse> GetAllGiftRedeemTransactionByGiftCategoryChannel_V1(GetAllGiftTransactionByGiftCategoryChannelInput input)
        {
            return await PostLoyaltyAsync<GetAllGiftTransactionByGiftCategoryChannelResponse>(LoyaltyApiUtilsUrl.GetAllGiftRedeemTransactionByGiftCategoryChannel, input);
        }
    }
}