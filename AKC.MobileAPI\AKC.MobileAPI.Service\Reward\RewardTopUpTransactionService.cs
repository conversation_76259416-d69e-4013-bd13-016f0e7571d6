﻿using AKC.MobileAPI.DTO.Reward.TopUpTransaction;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardTopUpTransactionService : RewardBaseService, IRewardTopUpTransactionService
    {
        public RewardTopUpTransactionService(IConfiguration configuration) : base(configuration)
        {
        }

        public async Task<RewardCreateTopUpTransactionOutput> CreateTopUpTransaction(RewardCreateTopUpTransactionInput input)
        {
            var request = new RewardCreateTopUpTransactionInputDto()
            {
                NationalId = input.MemberCode,
                TokenAmount = input.TokenAmount,
                PaymentTransactionId = input.PaymentTransactionId,
                PaymentTransactionStatus = input.PaymentTransactionStatus,
                RequestDate = input.RequestDate,
                MerchantId = input.MerchantId
            };
            return await PostRewardAsync<RewardCreateTopUpTransactionOutput>(RewardApiUrl.TOPUP_TRANSACTION_CREATE, request);
        }
    }
}
