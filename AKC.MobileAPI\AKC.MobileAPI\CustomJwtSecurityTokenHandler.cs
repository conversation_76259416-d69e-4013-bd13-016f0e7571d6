﻿//using Microsoft.AspNetCore.Http;
//using Microsoft.IdentityModel.Tokens;
//using System;
//using System.Collections.Generic;
//using System.IdentityModel.Tokens.Jwt;
//using System.Linq;
//using System.Security.Claims;
//using System.Threading.Tasks;

//namespace AKC.MobileAPI
//{
//    public class CustomJwtSecurityTokenHandler : ISecurityTokenValidator
//    {
//        public IHttpContextAccessor _httpContextAccessor { get; set; }

//        public CustomJwtSecurityTokenHandler()
//        {
//            _tokenHandler = new JwtSecurityTokenHandler();

//        }

//        public bool CanValidateToken => throw new NotImplementedException();

//        public int MaximumTokenSizeInBytes { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }

//        public bool CanReadToken(string securityToken)
//        {
//            throw new NotImplementedException();
//        }

//        public ClaimsPrincipal ValidateToken(string securityToken, TokenValidationParameters validationParameters, out SecurityToken validatedToken)
//        {
//            throw new NotImplementedException();
//        }
//    }
//}
