﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.ChallengeMaf
{
    public class LynkiDResponseList<T>
    {
        public PagedResultDto<T> Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool? UnAuthorizedRequest { get; set; }
        public bool? __abp { get; set; }
    }

    public class PagedResultDto<T>
    {
        public List<T> items { get; set; }
        public int totalCount { get; set; }
    }

    public class LynkiDResponse<T>
    {
        public T Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool? UnAuthorizedRequest { get; set; }
        public bool? __abp { get; set; }
    }

    public class LynkiDResponseExp
    {
        public string result { get; set; }
        public string rargetUrl { get; set; }
        public bool success { get; set; }
        public ErrorExpObject error { get; set; }
        public bool? unAuthorizedRequest { get; set; }
        public bool? __abp { get; set; }
    }

    public class ErrorExpObject
    {
        public object code { get; set; }
        public string message { get; set; }
    }

    public class OperatorResponseExp
    {
        public int status { get; set; }
        public OperatorErrorExpObject result { get; set; }
    }
    public class OperatorErrorExpObject
    {
        public string code { get; set; }
        public string message { get; set; }
    }
}
