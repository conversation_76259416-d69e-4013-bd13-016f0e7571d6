using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using AKC.MobileAPI.DTO.Loyalty.LocationManagement;
using AKC.MobileAPI.DTO.Loyalty.MasterCard;
using AKC.MobileAPI.DTO.MerchantGift;
using AKC.MobileAPI.DTO.Reward.Member;
using ImageLinkDto = AKC.MobileAPI.DTO.MerchantGift.ImageLinkDto;

namespace AKC.MobileAPI.DTO.Sme
{
    public class SmeHasPaginationFilters
    {
        public int? SkipCount { get; set; }
        public int? MaxResultCount { get; set; }
    }
    public class SmeGetListTransInputCallToRN : RewardMemberTokenTransGetByIdInput
    {
        public int? MemberId { get; set; }
    }
    public class SmeGetListTokenTransInput: SmeHasPaginationFilters
    {
        public string SmeCif { get; set; }
        public string Type { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }

    public class FindOneTokenTransInput
    {
        public string SmeCif { get; set; }
        public string TxId { get; set; }
    }

    public class FindOneTokenTransOutput : BaseSmeOutput
    {
        public SmeSingleTokenTransInfo TxMainInfo { get; set; }
        public string LoyaltyCampaignName { get; set; }
    }

    public class UtilGetCampaignName
    {
        public string CampaignName { get; set; }
        public string ErrorCode { get; set; }
    }
    public class SmeGetListTokenTransOutput : BaseSmeOutput
    {
        public List<SmeSingleTokenTransInfo> items { get; set; }
        public int TotalCount { get; set; }
    }

    public class SmeSingleTokenTransInfo
    {
        public string TxId { get; set; }
        public DateTime? Time { get; set; }
        public DateTime? BusinessTime { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string OrderCode { get; set; }
        public string ActionCode { get; set; }
        public string ActionCodeDetail { get; set; }        
        // public string ActionName { get; set; }
        // public string FromWalletAddress { get; set; }
        // public string ToWalletAddress { get; set; }
        // public int StoreId { get; set; }
        // public string StoreName { get; set; }
        public string UserAddress { get; set; }
        public string ActionType { get; set; }
        public decimal TokenAmount { get; set; }

        public string LoyaltyCampaignName { get; set; }
    }
    public class SmeCateListOutput : BaseSmeOutput
    {
        public List<MerchantGiftGetAllCategoryOutput> items { get; set; }
        public int TotalCount { get; set; }
    }

    public class GetAllCategoryWithoutMemberCodeOutput : BaseSmeOutput
    {
        public int TotalCount { get; set; }

        public List<MerchantGiftGetAllCategoryOutputItemDto> Items { get; set; }
    }
    public class GetAllCategoryWithoutMemberCodeInput: SmeHasPaginationFilters
    {
        public string CodeFilter { get; set; }
        public string NameFilter { get; set; }
        public string StatusFilter { get; set; }
        public string PartnerCode { get; set; }
    }
    public class SmeCateListInput: SmeHasPaginationFilters
    {
        public string CodeFilter { get; set; }
        public string NameFilter { get; set; }
    }

    public class SmeViewPointOutput: BaseSmeOutput
    {
        public string SmeCif { get; set; }
        public string LynkiDWallet { get; set; }
        public decimal Balance { get; set; }
        public decimal? ExpiringBalance { get; set; }
        public DateTime? ExpiringDate { get; set; }
    }
    public class SmeViewPointInput
    {
        [Required]
        public string SmeCif { get; set; }
    }

    public class GetSmeInfoInput
    {
        [Required]
        public string SmeCif { get; set; }
    }

    public class GetSmeInfoOutput : BaseSmeOutput
    {
        public int? Id { get; set; }
        public string LicenseNumber { get; set; }
        public string TaxNumber { get; set; }
        public string Cif { get; set; }
        public string Address { get; set; }
        public string FullName { get; set; }
        public string ShortName { get; set; }
        public DateTime? StartBusinessDate { get; set; }
        public string ContactEmail { get; set; }
        public string ContactPhone { get; set; }
        public string ConnectionStatus { get; set; }
        public int? LinkIDSmeId { get; set; }
        public string LinkIDSmeWallet { get; set; }
        public int? MemberCount { get; set; }
    }

    public class UtilGetSmeInfoOutput : GetSmeInfoOutput
    {
        public string ErrorCode { get; set; }
    }

    public class CreateSmeConnectionInput
    {
        public string SmeCif { get; set; }
        public string LicenseNumber { get; set; }
        public string TaxNumber { get; set; }
        public string StartBusinessDate { get; set; }
        public ExtraDataForConnect ExtraData { get; set; }
    }

    // More data that we ask NEOBiz to send
    public class ExtraDataForConnect
    {
        public string Address { get; set; }
        public string FullName { get; set; }
        public string ShortName { get; set; }
        public string ContactEmail { get; set; }
        public string ContactPhone { get; set; }
    }

    public class CreateSmeConnectionOutput : BaseSmeOutput
    {
        public SmeConnectResult Result { get; set; }
    }

    public class SmeConnectResult
    {
        public int SmeId { get; set; }
        public int LynkiDSmeId { get; set; }
    }


    public class RewardAckSmeConnectionInput
    {
        public string LoyaltyMemberCode { get; set; }
        public string LoyaltyMemberLicenseNumber { get; set; }
        public int MerchantId { get; set; }
        public int AckId { get; set; }
    }

    public class RewardAckSmeConnectionOutput: BaseSmeOutput
    {
        
    }
    public class RewardCreateSmeConnectionInput
    {
        public string SmeCif  { get; set; }
        public string LicenseNumber  { get; set; }
        public string TaxNumber  { get; set; }
        public int MerchantId { get; set; }
        public string ConnectSource { get; set; }
        public LoyaltyInfo4Connect LoyaltyInfo { get; set; }
    }
    public class LoyaltyInfo4Connect
    {
        public string FullName { get; set; }

        public string ShortName { get; set; }

        public string Address { get; set; }

        public string ContactEmail { get; set; }

        public string ContactPhone { get; set; }

        public DateTime? StartBusinessDate { get; set; }
    }

    public class RewardCreateSmeConnectionOutput
    {
        public string LoyaltyMemberCode { get; set; }
        public int? IdForAck { get; set; }
        public int LIDSmeId { get; set; }
        public string LIDWalletAddress { get; set; }
        public string LIDSmeCode { get; set; }
    }

    public class RewardCreateSmeConnectionOutputWrapper
    {
        public RewardCreateSmeConnectionOutput Item { get; set; }
        public string Message { get; set; }
        public int Result { get; set; }
    }

    public class GetListAddressShipSmeInput: SmeHasPaginationFilters
    {
        public int? IdFilter { get; set; }
        public string CodeFilter { get; set; }
        public string ParentCodeFilter { get; set; }
        public string LevelFilter { get; set; }
        public string NameFilter { get; set; }
    }

    public class GetListAddressShipSmeOutput : BaseSmeOutput
    {
        public int TotalCount { get; set; }

        public List<GetAllLocationItemDto> Items { get; set; }
    }

    public class GiftLocationSmeInput: SmeHasPaginationFilters
    {
    }

    public class GiftLocationSmeOutput : BaseSmeOutput
    {
        public List<MerchantGiftLocationOutput> Items { get; set; }
        public int TotalCount { get; set; }
    }

    public class GiftListSmeInput: SmeHasPaginationFilters
    {
        public string CategoryCode { get; set; }
        public decimal? PriceFrom { get; set; }
        public decimal? PriceTo { get; set; }
        public string BrandId { get; set; }

        // Search theo NAME or CODE
        public string Keyword { get; set; }
    }

    public class GiftListSmeOutput : BaseSmeOutput
    {
        public List<MerchantGiftGetAllGiftOutput> Items { get; set; }
        public int TotalCount { get; set; }
    }

    public class GiftDetailSmeInput
    {
        public int? GiftId { get; set; }
    }

    public class GiftDetailSmeOutput : BaseSmeOutput
    {
        public GiftDetailSmeOutputInner GiftDetail { get; set; }
    }
    public class GiftDetailSmeOutputInner
    {
        public int? Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public decimal? RequiredCoin { get; set; }
        public string CategoryCode { get; set; }
        public decimal? InStock { get; set; }
        public bool? IsEgift { get; set; }
        public string VendorHotline { get; set; }
        public string Vendor { get; set; }
        public string VendorImage { get; set; }
        public string VendorDescription { get; set; }
        public string BrandName { get; set; }
        public string BrandLinkLogo { get; set; }
        public string BrandAddress { get; set; }
        public string BrandDescription { get; set; }
        public List<MerchantGiftImageLinkShortDto> ImageLinks { get; set; }
    }

    public class SmeRedeemGiftInput
    {
        public string SmeCif { get; set; }
        public string TransactionCode { get; set; }
        public string GiftCode { get; set; }
        public decimal Amount { get; set; }
        public string Description { get; set; }
        public int Quantity { get; set; }
    }

    public class SmeRedeemGiftOutput : BaseSmeOutput
    {
        public string SmeCif { get; set; }
        public string GiftTransactionCode { get; set; }
        public List<SmeRedeemGiftOutputInner> Egifts { get; set; }
    }

    public class SmeRedeemGiftOutputInner
    {
        public string EGiftCode { get; set; }
        public string UsedStatus { get; set; }
        public bool UsageCheck { get; set; }
        public string QRCode { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public DateTime? ExpiredDate { get; set; }
    }

    public class SmeGetRedeemTransInput: SmeHasPaginationFilters
    {
        public string SmeCif { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string StatusFilter { get; set; }
        public string EGiftStatusFilter { get; set; }
        public string GiftTypeFilter { get; set; }
    }

    public class GetTxDetailOutput : BaseSmeOutput
    {
        public SmeGiftTransactionDto GiftTransaction { get; set; }
        public SmeRedeemGiftOutputInner EGift { get; set; }
        public List<string> ImageLinks { get; set; }
        public ThirdPartyGiftVendorShortDto VendorInfo { get; set; }
        public List<GiftUsageAddressShortDto> GiftUsageAddress { get; set; }
    }
    public class GetTxDetailInput
    {
        public string SmeCif { get; set; }
        public string GiftTransactionCode { get; set; }
    }

    public class SmeGetRedeemTransOutput : BaseSmeOutput
    {
        public int TotalCount { get; set; }
        public List<SmeGetRedeemTransOutputInner> Items { get; set; }
    }
    public class TransactionHistorySmeInput : MerchantGiftTransactionDetailInputDto{
        public string GiftTypeFilter { get; set; }
    }
    public class GetAllWithEGifResultSme
    {
        public int TotalCount { get; set; }
        public List<GetGiftTransSmeItem> Items { get; set; }
        public string GiftCategoryGiftCategoryName { get; set; }
    }
    public class TransactionHistorySmeOutput
    {
        public GetAllWithEGifResultSme Result { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
    }
    public class GetGiftTransSmeItem
    {
        public GiftTransactionDto GiftTransaction { get; set; }
        public EGiftInforDto EGift { get; set; }
        public ThirdPartyGiftVendorShortDto VendorInfo { get; set; }
        public List<ImageLinkDto> ImageLinks { get; set; }
        public List<GiftUsageAddressShortDto> GiftUsageAddress { get; set; }
    }
    public class SmeGetRedeemTransOutputInner
    {
        public SmeGiftTransactionDto GiftTransaction { get; set; }
        public SmeRedeemGiftOutputInner EGift { get; set; }
        public List<string> ImageLinks { get; set; }
        public ThirdPartyGiftVendorShortDto VendorInfo { get; set; }
        public List<GiftUsageAddressShortDto> GiftUsageAddress { get; set; }
    }

    public class SmeGiftTransactionDto
    {
        public string Code { get; set; }
        public string BuyerCode { get; set; }
        public string OwnerCode { get; set; }
        public DateTime? TransferTime { get; set; }
        public string Introduce { get; set; }
        public string GiftCode { get; set; }
        public string GiftName { get; set; }
        public int Quantity { get; set; }
        public decimal? Coin { get; set; }
        public DateTime? Date { get; set; }
        public string Status { get; set; }
        public int? GiftId { get; set; }
        public decimal TotalCoin { get; set; }
        public string Description { get; set; }
        public string TransactionCode { get; set; }
        public string QRCode { get; set; }
        public string CodeDisplay { get; set; }
        public int? Id { get; set; }
        public string LinkShippingInfo { get; set; }
        public string SerialNo { get; set; }
    }

    public class SmeEGiftInfo
    {
        public string Code { get; set; }
        public string QrCode { get; set; }
        public string UsedStatus { get; set; }
        public DateTime? ExpiredDate { get; set; }
        public DateTime CreationTime { get; set; }
    }

    public class GetListCampaignShortInforOutput
    {
        public List<CampaignShortInfor> ListCamgpaignShortInfor { get; set; } = new List<CampaignShortInfor>();
    }
    public class CampaignShortInfor
    {
        public int Id { get; set; }
        public string CampaginName { get; set; }
    }

    public class GetListCampaignShortInforInput
    {
        public List<int> ListId { get; set; }
        public int TenantId { get; set; }
    }
}