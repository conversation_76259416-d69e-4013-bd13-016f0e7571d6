# Circular Dependency Fix - Documentation

## Vấn đề

Gặp lỗi **InvalidOperationException: A circular dependency was detected** với dependency chain:

```
IMerchantGiftService (MerchantGiftService) 
    ↓ inject
ILinkIdLoyaltyVendorGiftService (LinkIdLoyaltyVendorGiftService) 
    ↓ inject  
IMerchantGiftService
```

## Root Cause Analysis

### 1. **MerchantGiftService Constructor**
```csharp
public MerchantGiftService(
    // ... other dependencies
    ILinkIdLoyaltyVendorGiftService linkIdLoyaltyService, // ← Inject LinkId service
    // ... other dependencies
)
{
    _linkIdLoyaltyService = linkIdLoyaltyService;
}
```

### 2. **LinkIdLoyaltyVendorGiftService Constructor**
```csharp
public LinkIdLoyaltyVendorGiftService(
    // ... other dependencies
    IMerchantGiftService merchantGiftService // ← Inject Merchant service
)
{
    _merchantGiftService = merchantGiftService;
}
```

### 3. **Circular Dependency**
- `MerchantGiftService` cần `ILinkIdLoyaltyVendorGiftService`
- `LinkIdLoyaltyVendorGiftService` cần `IMerchantGiftService`
- → **Circular dependency!**

## Giải pháp: Lazy Injection

### 1. **Cập nhật LinkIdLoyaltyVendorGiftService**

#### **Before (❌ Circular)**
```csharp
private readonly IMerchantGiftService _merchantGiftService;

public LinkIdLoyaltyVendorGiftService(
    // ... other dependencies
    IMerchantGiftService merchantGiftService
)
{
    _merchantGiftService = merchantGiftService;
}
```

#### **After (✅ Lazy)**
```csharp
private readonly Lazy<IMerchantGiftService> _merchantGiftService;

public LinkIdLoyaltyVendorGiftService(
    // ... other dependencies
    Lazy<IMerchantGiftService> merchantGiftService
)
{
    _merchantGiftService = merchantGiftService;
}
```

### 2. **Cập nhật Usage**

#### **Before**
```csharp
var memberTask = _merchantGiftService.CheckAndGetMemberInfo(new GetLinkIdMemberByCifCodeInput
{
    CifCode = request.CifCode
});
```

#### **After**
```csharp
var memberTask = _merchantGiftService.Value.CheckAndGetMemberInfo(new GetLinkIdMemberByCifCodeInput
{
    CifCode = request.CifCode
});
```

## Cách hoạt động của Lazy<T>

### 1. **Dependency Injection**
- DI container tạo `Lazy<IMerchantGiftService>` wrapper
- Wrapper này không tạo instance ngay lập tức
- Chỉ tạo instance khi `.Value` được gọi lần đầu

### 2. **Lazy Initialization**
```csharp
// Không tạo instance ngay
private readonly Lazy<IMerchantGiftService> _merchantGiftService;

// Chỉ tạo instance khi cần
var member = await _merchantGiftService.Value.CheckAndGetMemberInfo(...);
```

### 3. **Breaking Circular Dependency**
```
MerchantGiftService constructor
    ↓ inject
Lazy<IMerchantGiftService> (wrapper, chưa tạo instance)
    ↓ 
LinkIdLoyaltyVendorGiftService constructor hoàn thành
    ↓
MerchantGiftService constructor hoàn thành  
    ↓
Khi cần: _merchantGiftService.Value → tạo instance
```

## Lợi ích

### 1. **Giải quyết Circular Dependency**
- DI container có thể tạo cả 2 services
- Không còn circular dependency exception

### 2. **Performance**
- Lazy loading: chỉ tạo instance khi cần
- Tiết kiệm memory nếu dependency không được sử dụng

### 3. **Minimal Code Changes**
- Chỉ cần thay đổi constructor và thêm `.Value`
- Không cần refactor business logic

## Alternative Solutions

### 1. **Factory Pattern**
```csharp
public interface IMerchantGiftServiceFactory
{
    IMerchantGiftService Create();
}
```

### 2. **Service Locator**
```csharp
private readonly IServiceProvider _serviceProvider;

public SomeMethod()
{
    var merchantService = _serviceProvider.GetService<IMerchantGiftService>();
}
```

### 3. **Refactoring Architecture**
- Tách shared logic ra separate service
- Sử dụng events/messaging thay vì direct calls

## Best Practices

### 1. **Prefer Lazy<T> for Simple Cases**
- Ít code changes
- Dễ hiểu và maintain
- Built-in support trong .NET DI

### 2. **Consider Architecture Refactoring**
- Nếu có nhiều circular dependencies
- Tách concerns ra separate services
- Sử dụng mediator pattern

### 3. **Document Dependencies**
- Ghi chú lý do sử dụng Lazy<T>
- Document dependency flow
- Review architecture định kỳ

## Testing

### 1. **Unit Testing**
```csharp
var mockMerchantService = new Mock<IMerchantGiftService>();
var lazyMerchantService = new Lazy<IMerchantGiftService>(() => mockMerchantService.Object);

var service = new LinkIdLoyaltyVendorGiftService(
    // ... other dependencies
    lazyMerchantService
);
```

### 2. **Integration Testing**
- Test DI container configuration
- Verify no circular dependency exceptions
- Test actual service interactions

## Monitoring

### 1. **Performance Impact**
- Monitor lazy initialization overhead
- Check if services are created unnecessarily

### 2. **Dependency Health**
- Review circular dependencies định kỳ
- Consider architecture improvements

Giải pháp Lazy<T> đã giải quyết thành công circular dependency issue!
