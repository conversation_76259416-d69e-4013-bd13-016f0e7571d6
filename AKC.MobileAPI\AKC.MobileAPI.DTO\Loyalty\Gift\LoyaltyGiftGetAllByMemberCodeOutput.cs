﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class LoyaltyGiftGetAllByMemberCodeOutput
    {

        public GiftGetAllByMemberCodeView Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }


        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }
    public class GiftGetAllByMemberCodeView
    {
        public int TotalCount { get; set; }
        public List<GiftInforByMemberCodeForView> Items { get; set; }
    }


    public class GiftInforByMemberCodeForView
    {
        public GiftInforByMemberCodeDto GiftInfor { get; set; }

        public List<ImageLinkDto> ImageLink { get; set; }

        public GiftInforByMemberCodeForView()
        {
            ImageLink = new List<ImageLinkDto>();
        }
    }

    public class GiftInforByMemberCodeDto
    {
        public string Code { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public string Introduce { get; set; }

        public string FullGiftCategoryCode { get; set; }

        public string BrandName { get; set; }
        public string ThirdPartyBrandName { get; set; }

        public string Vendor { get; set; }

        public DateTime EffectiveFrom { get; set; }

        public DateTime EffectiveTo { get; set; }

        public decimal RequiredCoin { get; set; }

        public string Status { get; set; }

        public decimal TotalQuantity { get; set; }

        public decimal UsedQuantity { get; set; }

        public decimal RemainingQuantity { get; set; }

        public decimal FullPrice { get; set; }
        public decimal DiscountPrice { get; set; }

        public bool IsEGift { get; set; }

        public SettingParamDto TargetAudience { get; set; }
        public int? TargetAudienceId { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public DateTime CreationTime { get; set; }
        public string CreatedByUser { get; set; }
        public string UpdatedByUser { get; set; }

        public string Tag { get; set; }

        public int Id { get; set; }

        public List<OfficeInfo> Office { get; set; }
    }
}
