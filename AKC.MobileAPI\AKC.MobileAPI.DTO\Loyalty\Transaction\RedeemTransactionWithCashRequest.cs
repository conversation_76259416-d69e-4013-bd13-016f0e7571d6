﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;

namespace AKC.MobileAPI.DTO.Loyalty.Transaction
{
    public class RedeemTransactionWithCashRequest
    {
        public string CifCode { get; set; }
        public string GiftCode { get; set; }
        public int Quantity { get; set; }
        public decimal TotalAmount { get; set; }
        public string Description { get; set; }
        public string TransactionCode { get; set; }
        [JsonIgnore]
        public string PaymentType { get; set; } = "CASH";
    }
}
