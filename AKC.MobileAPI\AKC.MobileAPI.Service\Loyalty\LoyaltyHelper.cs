﻿using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyHelper
    {
        #region Private Methods
        /// <summary>
        /// Perform a http request to login into Loyalty system .
        /// </summary>
        /// <param name="tenantId">Loyalty TenantId</param>
        /// <param name="baseURL">Loyalty base URL</param>
        /// <param name="userName">admin username loyalty system</param>
        /// <param name="password">admin password</param>
        /// <returns><see cref="LoyaltyLoginResponse"/></returns>
        private static LoyaltyLoginResponse LoginLoyalty(int? tenantId, string baseURL, string userName, string password)
        {
            LoyaltyLoginDTO loyaltyLoginDTO = new LoyaltyLoginDTO()
            {
                UserNameOrEmailAddress = userName,
                Password = password
            };

            var body = JsonConvert.SerializeObject(loyaltyLoginDTO, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            });

            // Setup request.
            var req = new HttpRequestMessage { Method = HttpMethod.Post };
            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            req.RequestUri = new Uri($"{baseURL}/TokenAuth/Authenticate");
            req.Content = new StringContent(body, Encoding.UTF8, "application/json");

            // Get Response.
            HttpClient _client = new HttpClient();
            var response = _client.SendAsync(req).Result;
            var rawData = response.Content.ReadAsStringAsync().Result;

            // Make sure success status code.
            response.EnsureSuccessStatusCode();

            // Get respone result.
            return JsonConvert.DeserializeObject<LoyaltyLoginResponse>(rawData);
        }

        /// <summary>
        /// Perform set a new accesstoken into redis cache.
        /// </summary>
        /// <param name="cache"></param>
        /// <param name="allowRenewByJobCacheKey"></param>
        /// <param name="accessTokenCacheKey"></param>
        /// <param name="GetAccessTokenFunc"></param>
        /// <param name="delay"></param>
        /// <returns>Loyalty accesstoken</returns>
        private static string RenewCacheAndGetToken(IDistributedCache cache,
            string allowRenewByJobCacheKey,
            string accessTokenCacheKey,
            LoyaltyLoginResponse tokenInfo,
            TimeSpan delay)
        {
            // Cache flag allow refresh token (for cluster case).
            var cacheAllowRenewTokenFlagCacheOptions = new DistributedCacheEntryOptions()
                .SetAbsoluteExpiration(TimeSpan.FromSeconds(delay.TotalSeconds - 1));

            // Not allow refresh token until allowRenewByJobFlag expired
            cache.SetString(allowRenewByJobCacheKey, allowRenewByJobCacheKey, cacheAllowRenewTokenFlagCacheOptions);

            // Renew accesstoken and set new value into cache.
            var tokenExpiredSeconds = tokenInfo.Result.ExpireInSeconds > 0
                   ? tokenInfo.Result.ExpireInSeconds
                   : delay.TotalSeconds * 2;

            var cacheAccessTokenOptions = new DistributedCacheEntryOptions()
                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(tokenExpiredSeconds));

            cache.SetString(accessTokenCacheKey, tokenInfo?.Result?.AccessToken, cacheAccessTokenOptions);

            // Set accesstoken after renew.
            return tokenInfo.Result.AccessToken;
        }


        /// <summary>
        /// Get new access token of loyalty.
        /// </summary>
        /// <returns></returns>
        private static LoyaltyLoginResponse GetNewAccessToken()
        {
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var baseURL = configuration.GetSection("Loyalty:RemoteURL").Value;
            var tenantId = Convert.ToInt32(configuration.GetSection("Loyalty:TenantId").Value);
            var defaultUserName = configuration.GetSection("Loyalty:Username").Value;
            var defaultPassoword = configuration.GetSection("Loyalty:Password").Value;

            return LoginLoyalty(tenantId, baseURL, defaultUserName, defaultPassoword);
        }

        /// <summary>
        /// Get new accesstoken dummy tenant.
        /// </summary>
        /// <returns></returns>
        private static LoyaltyLoginResponse GetNewAccessTokenDummy()
        {
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var baseURL = configuration.GetSection("ThirdPartyMerchant:LoyaltyDummy:BaseURL").Value;
            var tenantId = Convert.ToInt32(configuration.GetSection("ThirdPartyMerchant:LoyaltyDummy:TenantId").Value);
            var defaultUserName = configuration.GetSection("ThirdPartyMerchant:LoyaltyDummy:Username").Value;
            var defaultPassoword = configuration.GetSection("ThirdPartyMerchant:LoyaltyDummy:Password").Value;

            return LoginLoyalty(tenantId, baseURL, defaultUserName, defaultPassoword);
        }
        #endregion

        #region Public Methods

        /// <summary>
        /// Get a new accesstoken in loyalty system
        /// and set this value into cache.
        /// </summary>
        /// <param name="cache"></param>
        /// <param name="delay"></param>
        /// <param name="isForceRenew">
        /// If true: Get new accesstoken form loyalty server and set this value into redis cache.
        /// If false: Return accesstoken in cache if existed, otherwirse get new accestoken from loyalty system and set this value into cache.
        /// </param>
        /// <returns>Loyalty accesstoken</returns>
        public static string RenewAccessTokenCacheValue(IDistributedCache cache, TimeSpan delay, bool isForceRenew = false)
        {
            return isForceRenew || string.IsNullOrEmpty(cache.GetString(CommonConstants.ALLOW_RENEW_LOYALTY_TOKEN))
                ? RenewCacheAndGetToken(cache,
                    CommonConstants.ALLOW_RENEW_LOYALTY_TOKEN,
                    CommonConstants.ACCESSS_TOKEN_CACHE_KEY,
                    GetNewAccessToken(), delay)
                : cache.GetString(CommonConstants.ACCESSS_TOKEN_CACHE_KEY);
        }

        /// <summary>
        /// Get a new accesstoken dummy tenant in loyalty system
        /// and set this value into cache.
        /// </summary>
        /// <param name="cache"></param>
        /// <param name="delay"></param>
        /// <param name="isForceRenew">
        /// If true: Get new accesstoken form loyalty server and set this value into redis cache.
        /// If false: Return accesstoken in cache if existed, otherwise get new accestoken from loyalty system and set this value into cache.
        /// </param>
        /// <returns>Loyalty accesstoken</returns>
        public static string RenewAccessTokenDummyCacheValue(IDistributedCache cache, TimeSpan delay, bool isForceRenew = false)
        {
            return isForceRenew || string.IsNullOrEmpty(cache.GetString(CommonConstants.ALLOW_RENEW_DUMMY_LOYALTY_TOKEN))
                ? RenewCacheAndGetToken(cache,
                    CommonConstants.ALLOW_RENEW_DUMMY_LOYALTY_TOKEN,
                    CommonConstants.ACCESSS_TOKEN_DUMMY_LOYALTY_CACHE_KEY,
                    GetNewAccessTokenDummy(), delay)
                : cache.GetString(CommonConstants.ACCESSS_TOKEN_DUMMY_LOYALTY_CACHE_KEY);
        }

        /// <summary>
        /// Generate transaction code.
        /// </summary>
        /// <param name="prefix"></param>
        /// <returns></returns>
        public static string GenTransactionCode(string prefix)
        {
            var prefixStart = DateTime.Now.ToString("HH");
            var prefixEnd = DateTime.Now.ToString("mmss");
            Guid gui = Guid.NewGuid();
            var textGui = gui.ToString().ToUpper().Replace("-", "");
            return prefixStart + textGui + prefixEnd;
        }

        // Integrate LinkID
        public static string RenewAccessTokenLinkIDCacheValue(IDistributedCache cache, TimeSpan delay, bool isForceRenew = false)
        {
            return isForceRenew || string.IsNullOrEmpty(cache.GetString(CommonConstants.ALLOW_RENEW_LOYALTY_TOKEN_LINKID))
                ? RenewCacheAndGetTokenLinkID(cache,
                    CommonConstants.ALLOW_RENEW_LOYALTY_TOKEN_LINKID,
                    CommonConstants.ACCESSS_TOKEN_CACHE_KEY_LINKID,
                    GetNewAccessTokenLinkID(), delay)
                : cache.GetString(CommonConstants.ACCESSS_TOKEN_CACHE_KEY_LINKID);
        }

        private static LoyaltyLoginResponse GetNewAccessTokenLinkID()
        {
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var baseURL = configuration.GetSection("LoyaltyLinkID:RemoteURL").Value;
            var tenantId = Convert.ToInt32(configuration.GetSection("LoyaltyLinkID:TenantId").Value);
            var defaultUserName = configuration.GetSection("LoyaltyLinkID:Username").Value;
            var defaultPassoword = configuration.GetSection("LoyaltyLinkID:Password").Value;
            return LoginLoyalty(tenantId, baseURL, defaultUserName, defaultPassoword);
        }

        private static string RenewCacheAndGetTokenLinkID(IDistributedCache cache,
            string allowRenewByJobCacheKey,
            string accessTokenCacheKey,
            LoyaltyLoginResponse tokenInfo,
            TimeSpan delay)
        {
            // Cache flag allow refresh token (for cluster case).
            var cacheAllowRenewTokenFlagCacheOptions = new DistributedCacheEntryOptions()
                .SetAbsoluteExpiration(TimeSpan.FromSeconds(delay.TotalSeconds - 1));

            // Not allow refresh token until allowRenewByJobFlag expired
            cache.SetString(allowRenewByJobCacheKey, allowRenewByJobCacheKey, cacheAllowRenewTokenFlagCacheOptions);

            // Renew accesstoken and set new value into cache.
            var tokenExpiredSeconds = tokenInfo.Result.ExpireInSeconds > 0
                   ? tokenInfo.Result.ExpireInSeconds
                   : delay.TotalSeconds * 2;

            var cacheAccessTokenOptions = new DistributedCacheEntryOptions()
                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(tokenExpiredSeconds));

            cache.SetString(accessTokenCacheKey, tokenInfo?.Result?.AccessToken, cacheAccessTokenOptions);

            // Set accesstoken after renew.
            return tokenInfo.Result.AccessToken;
        }
        // End Integrate LinkID

        public static string GenTransactionCodeV2(string prefix)
        {
            var temp = prefix + DateTime.Now.ToString("YYYYMMDDHHmmss") + DateTime.Now.Ticks.ToString();
            MD5 mh = MD5.Create();
            byte[] inputBytes = Encoding.ASCII.GetBytes(temp);
            byte[] hash = mh.ComputeHash(inputBytes);
            StringBuilder sb = new StringBuilder();

            for (int i = 0; i < hash.Length; i++)
            {
                sb.Append(hash[i].ToString("X2"));
            }
            return sb.ToString();
        }
        /// <summary>
        /// Generate member code.
        /// </summary>
        /// <param name="prefix"></param>
        /// <returns></returns>
        public static string GenMemberCode(string phoneNumber)
        {
            var memberCode = "MSO" + Regex.Replace(phoneNumber, @"[^0-9]+", "") + GenTimeStamp();
            return memberCode;
        }
        private static string GenTimeStamp()
        {
            return DateTime.Now.ToString("yyyyMMddHHmmss");
        }
        #endregion
    }
}
