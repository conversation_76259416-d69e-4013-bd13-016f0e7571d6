﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class LoyaltyGetGiftByByMemberCodeOutput
    {
        public GetGiftByByMemberCodeForView Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }


        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }

    public class GetGiftByByMemberCodeForView
    {
        public GiftInforDto GiftInfor { get; set; }

        public List<ImageLinkDto> ImageLink { get; set; }

        public GetGiftByByMemberCodeForView()
        {
            ImageLink = new List<ImageLinkDto>();
        }
    }
}
