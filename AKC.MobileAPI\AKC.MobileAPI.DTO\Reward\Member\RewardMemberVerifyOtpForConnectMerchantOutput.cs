﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberVerifyOtpForConnectMerchantOutput
    {
        public int Result { get; set; }
        public Dictionary<string, int> ExtraInfo { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; } = true;
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class RewardMemberVerifyOtpForConnectMerchantDto
    {
        public int Result { get; set; }
        public string Message { get; set; }
        public RewardMemberVerifyOtpForConnectMerchantItem Item { get; set; }
    }

    public class RewardMemberVerifyOtpForConnectMerchantItem
    {
        public int MemberId { get; set; }
        public string PhoneNumber { get; set; }
        public string MemberWalletAddress { get; set; }
    }
}
