﻿using AKC.MobileAPI.DTO.Reward.PayByTokenTransaction;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Loyalty;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardPayByTokenTransactionService : RewardBaseService, IRewardPayByTokenTransactionService
    {
        public RewardPayByTokenTransactionService(IConfiguration configuration) : base(configuration)
        {
        }
        public async Task<RewardCreatePayByTokenTransactionOutput> CreatePayByTokenTransaction(RewardCreatePayByTokenTransactionInput input)
        {
            var request = new RewardCreatePayByTokenTransactionDto()
            {
                NationalId = input.NationalId,
                OrderCode = LoyaltyHelper.GenTransactionCode(input.NationalId + "PayByToken"),
                StoreId = input.StoreId,
                Time = input.Time,
                TotalRequestedAmount = input.TotalRequestedAmount,
                WalletAddress = input.WalletAddress,
            };
            return await PostRewardAsync<RewardCreatePayByTokenTransactionOutput>(RewardApiUrl.PAY_BY_TOKEN_TRANSACTION_CREATE, request, MerchantNameConfig.VPID);
        }
    }
}
