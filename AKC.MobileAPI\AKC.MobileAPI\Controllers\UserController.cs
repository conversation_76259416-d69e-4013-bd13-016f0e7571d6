﻿//using AKC.MobileAPI.DTO.ApiSMS;
//using AKC.MobileAPI.DTO.User;
//using AKC.MobileAPI.Service.Abstract;
//using FirebaseAdmin.Auth;
//using Microsoft.AspNetCore.Mvc;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using Microsoft.Extensions.Logging;
//using System.Threading.Tasks;

//namespace AKC.MobileAPI.Controllers
//{
//    [Route("api/[controller]/[action]")]
//    [ApiController]
//    [ApiConventionType(typeof(DefaultApiConventions))]
//    public class UserController : ControllerBase
//    {
//        private readonly ILogger _logger;
//        private readonly IUserService _userService;
//        public UserController(ILogger<UserController> logger, IUserService userService)
//        {
//            _userService = userService;
//            _logger = logger;
//        }

//        [HttpPost]
//        public async Task<ValidateUserOutput> IsExisted(ValidateUserInput model)
//        {
//            return await _userService.IsExisted(model);
//        }

//        //[HttpPost]
//        //public async Task<ResponseApiSMSModel> GetOTP(ValidateUserInput model)
//        //{
//        //    return await _userService.GetOTP(model);
//        //}

//        [HttpPost]
//        public async Task<GetOTPOutput> GetOTP(ValidateUserInput model)
//        {
//            return await _userService.GetOTP(model);
//        }

//        [HttpPost]
//        public async Task<ActionResult<VerifyOTPOutput>> VerifyOTPAndLogin(SendOTPUserInput input)
//        {
//            try
//            {
//                var result = await _userService.VerifyOTPAndLogin(input);
//                return StatusCode(200, result);
//            }
//            catch (Exception ex)
//            {
//                _logger.LogError(ex, "--VerifyOTPAndLogin Error - " + ex.Message);
//                return StatusCode(400, null);
//            }
//        }

//        [HttpPost]
//        public async Task<ActionResult<VerifyOTPOutput>> VerifyOTPAndRegister(CreateUserMobileDTO input)
//        {
//            try
//            {
//                var result = await _userService.VerifyOTPAndRegister(input);
//                return StatusCode(200, result);
//            }
//            catch (Exception ex)
//            {
//                _logger.LogError(ex, "--VerifyOTPAndRegister Error - " + ex.Message);
//                return StatusCode(400, null);
//            }
//        }
//    }
//}
