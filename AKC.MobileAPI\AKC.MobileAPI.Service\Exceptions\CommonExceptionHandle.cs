﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.Service.Exceptions
{
    public static class CommonExceptionHandle
    {
        public static ErrorResult LoyaltyExceptionHandle(LoyaltyException ex)
        {
            var result = new ErrorResult()
            {
                Code = 100,
                Message = "Lỗi không xác định"
            };
            var dataError = ex.Data["ErrorData"];
            if (dataError != null)
            {
                var json = dataError.ToString();
                var errorData = JsonConvert.DeserializeObject<ErrorData>(json);
                if (errorData != null && errorData.error != null)
                {
                    result.Code = errorData.error.code;
                    result.Message = errorData.error.message;
                }
            }
            return result;
        }
    }

    public class ErrorResult
    {
        public int Code { get; set; }
        public string Message { get; set; }
    }

    public class ErrorDetail
    {
        public int code { get; set; }
        public string message { get; set; }
        public string details { get; set; }
        public string validationErrors { get; set; }
    }

    public class ErrorData
    {
        public object result { get; set; }
        public object targetUrl { get; set; }
        public bool success { get; set; }
        public ErrorDetail error { get; set; }
        public bool unAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

}
