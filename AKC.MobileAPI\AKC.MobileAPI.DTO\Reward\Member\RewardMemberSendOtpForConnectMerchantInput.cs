﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberAutoConnectMemberInput
    {
        public string LinkID_PhoneNumber { get; set; }
        public RewardMemberVerifyOtpLoyaltyInfo LoyaltyInfo { get; set; }
    }
    public class RewardMemberAutoConnectMember3Input: RewardMemberAutoConnectMemberInput
    {
        public string Prefix { get; set; }
    }
    public class RewardMemberSendOtpForConnectMerchantInput
    {
        public bool? RequiredOtp { get; set; }
        public string SessionId { get; set; }
        public string LinkID_PhoneNumber { get; set; }
        
        public int? ConnectSource { get; set; }
        public RewardMemberVerifyOtpLoyaltyInfo LoyaltyInfo { get; set; }
    }
    public class RewardMemberSaveRefreshTokenV2Output
    {
        public int Result { get; set; }
        public string Message { get; set; }
        public string MessageDetail { get; set; }
    }
    public class RewardMemberSaveRefreshTokenInputV2
    {
        public string MemberCode { get; set; }

        public int MerchantId { get; set; }

        public string RefreshToken { get; set; }
        public bool IsChangedLoyalty { get; set; }
        public string ReferenceData { get; set; }
        public string ConnectSource { get; set; }
        public MemberConnectLoyaltyInfoInput MemberLoyaltyInfo { get; set; }
    }
    public class MemberConnectLoyaltyInfoInput
    {
        public string Cif { get; set; }
        public string MemberLoyaltyCode { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string Email { get; set; }
        public string Gender { get; set; } = "O";
        public DateTime? Dob { get; set; }
        public string Type { get; set; } = "Member";
        public string RegionCode { get; set; }
        public string FullRegionCode { get; set; }
        public string MemberTypeCode { get; set; }
        public string FullMemberTypeCode { get; set; }
        public string ChannelType { get; set; }
        public string FullChannelTypeCode { get; set; }
        public string RankTypeCode { get; set; }
        public string StandardMemberCode { get; set; }
        public string IdCard { get; set; }
        public string PartnerPhoneNumber { get; set; }
        public string Phone { get; set; }
        public string Avatar { get; set; }
        public string Segment { get; set; }
        public string VipType { get; set; }
    }

    public class RewardMemberSendOtpForConnectMerchantInputDto
    {
        public bool RequiredOtp { get; set; }
        public bool IsValidation { get; set; }
        public string SessionId { get; set; }
        public int MerchantId { get; set; }
        public string PhoneNumber { get; set; }
        public string IdCard { get; set; }
        public string PartnerPhoneNumber { get; set; }
        public string Cif { get; set; }
        public string ConnectSource { get; set; }
        public RewardMemberSendOtpLoyaltyInfoDto LoyaltyInfo { get; set; }
    }

    public class RewardMemberSendOtpLoyaltyInfoDto
    {
        public string Cif { get; set; }
        public string MemberLoyaltyCode { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string Email { get; set; }
        public string Gender { get; set; }
        public DateTime? Dob { get; set; }
        public string Type { get; set; }
        public string RegionCode { get; set; }
        public string FullRegionCode { get; set; }
        public string MemberTypeCode { get; set; }
        public string FullMemberTypeCode { get; set; }
        public string ChannelType { get; set; }
        public string FullChannelTypeCode { get; set; }
        public string RankTypeCode { get; set; }
        public string StandardMemberCode { get; set; }
        public string IdCard { get; set; }
        public string PartnerPhoneNumber { get; set; }
        public string Phone { get; set; }
        public string Avatar { get; set; }
        public string Segment { get; set; }
        public string VipType { get; set; }
    }
    public class CheckExistenceAndConnectionWithMerchantInput
    {
        public int MerchantId { get; set; }
        public string LinkIdPhoneNumber { get; set; }
        public string IdCard { get; set; }
    }
    public class CheckExistenceAndConnectionWithMerchantOutput
    {
        public CheckExistenceAndConnectionWithMerchantOutputLid LinkIdMember { get; set; }
        public CheckExistenceAndConnectionWithMerchantOutputIdCard RsCheckIdCard { get; set; }
    }

    public class CheckExistenceAndConnectionWithMerchantOutputLid
    {
        public bool IsExisting { get; set; }
        public string Message { get; set; }
        public bool HasConnection { get; set; }
        public int MerchantId { get; set; }
        public string ConnectedCif { get; set; }
        public string ConnectedIdCard { get; set; }
        public string LinkIdMemberCode { get; set; }
        public string MemberIdCard { get; set; } // IDCard bên trong bản ghi Member
        public int LinkIdMemberId { get; set; }
        public string LinkIdMemberWallet { get; set; }
    }

    public class CheckExistenceAndConnectionWithMerchantOutputIdCard
    {
        public bool IsExist { get; set; }
        public int CorrespondingLinkIDMemberId { get; set; }
        public string CorrespondingMemberPhone { get; set; }
    }
}
