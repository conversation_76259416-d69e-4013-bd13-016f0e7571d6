using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.LoyaltyVendorGift.LinkID;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Examples
{
    /// <summary>
    /// Example service showing how to use PostLoyaltyAsyncV2 and handle different exceptions
    /// </summary>
    public class LoyaltyServiceUsageExample
    {
        private readonly BaseLinkIdLoyaltyVendorGiftService _loyaltyService;
        private readonly ILogger<LoyaltyServiceUsageExample> _logger;

        public LoyaltyServiceUsageExample(
            IConfiguration configuration,
            IDistributedCache cache,
            ILogger<BaseLinkIdLoyaltyVendorGiftService> loyaltyLogger,
            ILogger<LoyaltyServiceUsageExample> logger)
        {
            _loyaltyService = new BaseLinkIdLoyaltyVendorGiftService(configuration, cache, loyaltyLogger);
            _logger = logger;
        }

        /// <summary>
        /// Example method showing comprehensive exception handling for PostLoyaltyAsyncV2
        /// </summary>
        public async Task<ApiResponse<T>> CallLoyaltyApiWithExceptionHandling<T>(
            string apiUrl, 
            object requestBody = null, 
            HttpContext httpContext = null)
        {
            try
            {
                _logger.LogInformation($"Calling loyalty API: {apiUrl}");
                
                var result = await _loyaltyService.PostLoyaltyAsyncV2<T>(apiUrl, requestBody, httpContext);
                
                _logger.LogInformation($"Successfully called loyalty API: {apiUrl}");
                return new ApiResponse<T>
                {
                    Success = true,
                    Data = result,
                    Message = "API call successful"
                };
            }
            catch (LoyaltyTimeoutException ex)
            {
                _logger.LogError(ex, $"Timeout occurred for API: {ex.ApiUrl}");
                return new ApiResponse<T>
                {
                    Success = false,
                    ErrorCode = "TIMEOUT",
                    Message = $"Request timed out after {ex.Timeout?.TotalSeconds} seconds",
                    Details = ex.Message
                };
            }
            catch (LoyaltyGatewayTimeoutException ex)
            {
                _logger.LogError(ex, $"Gateway timeout for API: {ex.ApiUrl}");
                return new ApiResponse<T>
                {
                    Success = false,
                    ErrorCode = "GATEWAY_TIMEOUT",
                    Message = "Gateway timeout occurred. Please try again later.",
                    Details = ex.Message
                };
            }
            catch (LoyaltyCancelledException ex)
            {
                _logger.LogError(ex, $"Request cancelled for API: {ex.ApiUrl}");
                return new ApiResponse<T>
                {
                    Success = false,
                    ErrorCode = "CANCELLED",
                    Message = "Request was cancelled",
                    Details = ex.Message
                };
            }
            catch (LoyaltyBadRequestException ex)
            {
                _logger.LogError(ex, $"Bad request for API: {ex.ApiUrl}");
                return new ApiResponse<T>
                {
                    Success = false,
                    ErrorCode = "BAD_REQUEST",
                    Message = "Invalid request data",
                    Details = ex.ResponseContent
                };
            }
            catch (LoyaltyUnauthorizedException ex)
            {
                _logger.LogError(ex, $"Unauthorized access for API: {ex.ApiUrl}");
                return new ApiResponse<T>
                {
                    Success = false,
                    ErrorCode = "UNAUTHORIZED",
                    Message = "Authentication failed",
                    Details = ex.ResponseContent
                };
            }
            catch (LoyaltyHttpException ex)
            {
                _logger.LogError(ex, $"HTTP error {ex.StatusCode} for API: {ex.ApiUrl}");
                return new ApiResponse<T>
                {
                    Success = false,
                    ErrorCode = $"HTTP_{(int)ex.StatusCode}",
                    Message = $"HTTP {(int)ex.StatusCode} {ex.StatusCode}",
                    Details = ex.ResponseContent
                };
            }
            catch (LoyaltyNetworkException ex)
            {
                _logger.LogError(ex, $"Network error for API: {ex.ApiUrl}");
                return new ApiResponse<T>
                {
                    Success = false,
                    ErrorCode = "NETWORK_ERROR",
                    Message = "Network connection failed. Please check your internet connection.",
                    Details = ex.Message
                };
            }
            catch (LoyaltyException ex)
            {
                _logger.LogError(ex, $"General loyalty error for API: {apiUrl}");
                return new ApiResponse<T>
                {
                    Success = false,
                    ErrorCode = "LOYALTY_ERROR",
                    Message = "An error occurred while processing the loyalty request",
                    Details = ex.Message
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Unexpected error for API: {apiUrl}");
                return new ApiResponse<T>
                {
                    Success = false,
                    ErrorCode = "UNEXPECTED_ERROR",
                    Message = "An unexpected error occurred",
                    Details = ex.Message
                };
            }
        }

        /// <summary>
        /// Example method showing specific exception handling for different scenarios
        /// </summary>
        public async Task<bool> HandleSpecificScenarios<T>(string apiUrl, object requestBody = null)
        {
            try
            {
                await _loyaltyService.PostLoyaltyAsyncV2<T>(apiUrl, requestBody);
                return true;
            }
            catch (LoyaltyTimeoutException ex)
            {
                // Handle timeout - maybe retry with exponential backoff
                _logger.LogWarning($"Timeout for {ex.ApiUrl}, implementing retry logic...");
                // Implement retry logic here
                return false;
            }
            catch (LoyaltyBadRequestException ex)
            {
                // Handle bad request - log details and don't retry
                _logger.LogError($"Bad request for {ex.ApiUrl}: {ex.ResponseContent}");
                // Don't retry for bad requests
                return false;
            }
            catch (LoyaltyUnauthorizedException ex)
            {
                // Handle unauthorized - maybe refresh token and retry
                _logger.LogWarning($"Unauthorized for {ex.ApiUrl}, attempting token refresh...");
                // Token refresh logic would be handled in the base service
                return false;
            }
            catch (LoyaltyNetworkException ex)
            {
                // Handle network issues - maybe retry after delay
                _logger.LogWarning($"Network error for {ex.ApiUrl}, will retry later...");
                // Implement network retry logic
                return false;
            }
            catch (LoyaltyException ex)
            {
                // Handle other loyalty exceptions
                _logger.LogError($"Loyalty error: {ex.Message}");
                return false;
            }
        }
    }

    /// <summary>
    /// Generic API response wrapper
    /// </summary>
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public T Data { get; set; }
        public string Message { get; set; }
        public string ErrorCode { get; set; }
        public string Details { get; set; }
    }
}
