﻿using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.Service.Exceptions;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using AKC.MobileAPI.DTO.Sme;

namespace AKC.MobileAPI.Service.Helpers
{
    public class CommonHelper
    {
        /**
         * Nếu mà prefix do bên ngoài truyền vào ko đúng mong muốn, sẽ sử dụng defaultValue
         * Mong muốn: prefix dài ko quá 10 ký tự (Sau khi trimmed, không được chứa ký tự nào ngoài alphanumberic và _ )
         * Nếu không phải case dùng default value, thì kết quả nhận được sẽ all biến thành upper case
         */
        public static string GetPrefixCreateMemberCode(string inputCode, string defaultValue)
        {
            inputCode = inputCode?.Trim();

            if (string.IsNullOrEmpty(inputCode))
            {
                return defaultValue;
            }
            if (inputCode.Length > 10 || !Regex.IsMatch(inputCode, @"^[a-zA-Z0-9_]*$"))
            {
                return defaultValue;
            }
            var result = inputCode.ToUpper();
            return result;
        }
        public static RewardDataExceptionResponse GetErrorValidation(string errorCode, string errorMessage)
        {
            var ex = new RewardException();
            var error = new RewardDataExceptionResponse()
            {
                result = new RewardDataExceptionResultItem()
                {
                    code = errorCode,
                    message = errorMessage,
                },
                status = 500
            };
            ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
            ex.Data.Add("StatusCode", 400);
            throw ex;
        }

        public static string GetCodeRedeemFromCode(string code)
        {
            //601 Address not found
            //602 City code not found
            //603 The province you entered does not exist
            //604 District code not found
            //605 The district you entered does not exist
            //606 Districts that are not in the city of your choice
            //1031 Connection error. Please try again
            //1032 There was an error in the order processing, the order result will be sent to you as soon as possible
            //2009 Can not update status through urbox

            switch (code)
            {
                case "601":
                case "602":
                case "603":
                case "604":
                case "605":
                case "606":
                    return "1029";
                case "1019":
                case "1024":
                    return "1023";
                case "1028":
                    return "1025";
                case "1037":
                case "1038":
                    return "1022";
                case "1031":
                case "1032":
                case "2009":
                    return "SystemError";
                case "MemberNotExitsOrNotActive":
                    return "MemberNotExist";
                case "BalanceNotEnough":
                    return "MemberNotEnoughToken";
                default:
                    return code;
            }
        }

        public static string GetMessageRedeemFromCode(string code)
        {
            var codeCheck = GetCodeRedeemFromCode(code);
            switch (codeCheck)
            {
                case "MemberNotEnoughToken":
                    return "Member not enough token";
                case "MemberNotExist":
                    return "LinkID member not exist";
                case "1022":
                    return "Can not redeem at this time";
                case "1023":
                    return "Gift not exist";
                case "1025":
                    return "Out of gift";
                case "1026":
                    return "Gift required coin of request is not valid";
                case "1028":
                    return "Out of gift (Egift)";
                case "1029":
                    return "Address redeem not found";
                case "225":
                    return "Gift is out of stock, please remove product from the cart.";
                case "410":
                    return "Your order is exceeding the allowed quantity (30 products/order). Please try again.";
                default:
                    return "System error";
            }
        }
        
        public static string ComputeHash(SmeGetListTokenTransInput input)
        {
            if (input == null)
            {
                throw new ArgumentNullException(nameof(input));
            }

            // Create a list to hold field values
            var fieldValues = new List<string>();

            // Add each field value to the list (order matters for hashing)
            fieldValues.Add(nameof(input.SmeCif) + "=" + input.SmeCif);
            fieldValues.Add(nameof(input.Type) + "=" + input.Type);
            fieldValues.Add(nameof(input.FromDate) + "=" + input.FromDate);
            fieldValues.Add(nameof(input.ToDate) + "=" + input.ToDate);
            fieldValues.Add(nameof(input.SkipCount) + "=" + input.SkipCount);
            fieldValues.Add(nameof(input.MaxResultCount) + "=" + input.MaxResultCount);

            // Sort field values alphabetically by field name
            fieldValues.Sort(StringComparer.Ordinal);

            // Concatenate all sorted field values into a single string
            var concatenatedString = string.Join("&", fieldValues);

            // Compute SHA256 hash of the concatenated string
            using (var sha256 = SHA256.Create())
            {
                byte[] hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(concatenatedString));
                return BitConverter.ToString(hashedBytes).Replace("-", "").ToLower();
            }
        }
        public static string ComputeHash(FindOneTokenTransInput input)
        {
            if (input == null)
            {
                throw new ArgumentNullException(nameof(input));
            }
            var fieldValues = new List<string>();
            fieldValues.Add(nameof(input.SmeCif) + "=" + input.SmeCif);
            fieldValues.Add(nameof(input.TxId) + "=" + input.TxId);
            fieldValues.Sort(StringComparer.Ordinal);
            var concatenatedString = string.Join("&", fieldValues);
            using (var sha256 = SHA256.Create())
            {
                byte[] hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(concatenatedString));
                return BitConverter.ToString(hashedBytes).Replace("-", "").ToLower();
            }
        }
    }
}
