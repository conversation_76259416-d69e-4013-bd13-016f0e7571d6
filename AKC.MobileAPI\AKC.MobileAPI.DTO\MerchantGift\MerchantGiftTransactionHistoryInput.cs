﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.MerchantGift
{
    public class MerchantGiftTransactionHistoryInput
    {
        public string CifCode { get; set; }
        public DateTime? FromDateFilter { get; set; }
        public DateTime? ToDateFilter { get; set; }
        public string StatusFilter { get; set; }
        public int? SkipCount { get; set; } = 0;
        public int? MaxResultCount { get; set; } = 9999;
        public string Language { get; set; }
    }

    // For DTO
    public class MerchantGiftTransactionHistoryDto
    {
        public string OwnerCodeFilter { get; set; }
        public string StatusFilter { get; set; }
        public string EGiftStatusFilter { get; set; }
        public DateTime? FromDateFilter { get; set; }
        public DateTime? ToDateFilter { get; set; }
        public string GiftTransactionCode { get; set; }
        public int MaxResultCount { get; set; }
        public int SkipCount { get; set; }
        public string Sorting { get; set; }
    }
}
