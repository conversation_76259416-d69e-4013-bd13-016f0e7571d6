﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class GetAllGiftCategoriesAndInfoInput
    {
        [Required]
        public string MemberCode { get; set; }
        public string Filter { get; set; }

        public string DescriptionFilter { get; set; }

        public string StatusFilter { get; set; }

        public int? MaxLevelFilter { get; set; }
        public int? MinLevelFilter { get; set; }

        public string ParentCodeFilter { get; set; }

        public string ParentNameGiftCategoryFilter { get; set; }

        public int SkipCount { get; set; } = 0;

        public int MaxResultCount { get; set; } = int.MaxValue;

        public string Sorting { get; set; }

        public int MaxItem { get; set; }
    }
}
