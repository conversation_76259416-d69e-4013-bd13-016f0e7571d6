﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.MerchantGift
{
    public class MerchantGetGiftWithoutMemberCodeInput
    {
        public string Filter { get; set; }
        public string FullGiftCategoryCodeFilter { get; set; }
        public decimal? FromCointFilter { get; set; }
        public decimal? ToCoinFilter { get; set; }
        public string BrandIdFilter { get; set; }
        public string PartnerCode { get; set; }
        public int? SkipCount { get; set; }
        public int? MaxResultCount { get; set; }
        public bool? IsEGiftFilter { get; set; }
    }
}
