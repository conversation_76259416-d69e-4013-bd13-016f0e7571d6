# Loyalty API Exception Handling Guide

## Tổng quan

Hàm `PostLoyaltyAsyncV2` đã được cải tiến để ném các exception riêng biệt cho từng loại lỗi khác nhau. Điều này giúp các service khác có thể bắt và xử lý các lỗi một cách cụ thể.

## Các loại Exception

### 1. LoyaltyTimeoutException
- **Khi nào xảy ra**: Request bị timeout (vượt quá thời gian chờ 300 giây)
- **Properties**:
  - `ApiUrl`: URL của API bị timeout
  - `Timeout`: Thời gian timeout được cấu hình
- **Cách xử lý**: Có thể retry với exponential backoff

```csharp
catch (LoyaltyTimeoutException ex)
{
    _logger.LogWarning($"API {ex.ApiUrl} timed out after {ex.Timeout?.TotalSeconds} seconds");
    // Implement retry logic
}
```

### 2. LoyaltyGatewayTimeoutException
- **<PERSON>hi nào xảy ra**: Server trả về HTTP 504 Gateway Timeout
- **Properties**:
  - `ApiUrl`: URL của API bị gateway timeout
- **Cách xử lý**: Thường là lỗi server, có thể retry sau một khoảng thời gian

```csharp
catch (LoyaltyGatewayTimeoutException ex)
{
    _logger.LogError($"Gateway timeout for API {ex.ApiUrl}");
    // Wait and retry later
}
```

### 3. LoyaltyCancelledException
- **Khi nào xảy ra**: Request bị cancel (thường do user hoặc system)
- **Properties**:
  - `ApiUrl`: URL của API bị cancel
- **Cách xử lý**: Không nên retry, thông báo cho user

```csharp
catch (LoyaltyCancelledException ex)
{
    _logger.LogInfo($"Request to {ex.ApiUrl} was cancelled");
    // Don't retry, inform user
}
```

### 4. LoyaltyBadRequestException
- **Khi nào xảy ra**: Server trả về HTTP 400 Bad Request
- **Properties**:
  - `ApiUrl`: URL của API
  - `StatusCode`: HttpStatusCode.BadRequest
  - `ResponseContent`: Nội dung response từ server
- **Cách xử lý**: Lỗi dữ liệu đầu vào, không nên retry

```csharp
catch (LoyaltyBadRequestException ex)
{
    _logger.LogError($"Bad request for {ex.ApiUrl}: {ex.ResponseContent}");
    // Validate input data, don't retry
}
```

### 5. LoyaltyUnauthorizedException
- **Khi nào xảy ra**: Server trả về HTTP 401 Unauthorized (sau khi đã retry với token mới)
- **Properties**:
  - `ApiUrl`: URL của API
  - `StatusCode`: HttpStatusCode.Unauthorized
  - `ResponseContent`: Nội dung response từ server
- **Cách xử lý**: Lỗi authentication, cần check token

```csharp
catch (LoyaltyUnauthorizedException ex)
{
    _logger.LogError($"Unauthorized access to {ex.ApiUrl}: {ex.ResponseContent}");
    // Check authentication, redirect to login
}
```

### 6. LoyaltyHttpException
- **Khi nào xảy ra**: Server trả về các HTTP error status code khác
- **Properties**:
  - `ApiUrl`: URL của API
  - `StatusCode`: HTTP status code
  - `ResponseContent`: Nội dung response từ server
- **Cách xử lý**: Tùy thuộc vào status code cụ thể

```csharp
catch (LoyaltyHttpException ex)
{
    _logger.LogError($"HTTP {(int)ex.StatusCode} error for {ex.ApiUrl}: {ex.ResponseContent}");
    // Handle based on specific status code
}
```

### 7. LoyaltyNetworkException
- **Khi nào xảy ra**: Lỗi network (HttpRequestException, WebException)
- **Properties**:
  - `ApiUrl`: URL của API
- **Cách xử lý**: Lỗi kết nối, có thể retry

```csharp
catch (LoyaltyNetworkException ex)
{
    _logger.LogError($"Network error for {ex.ApiUrl}: {ex.Message}");
    // Check network connection, retry later
}
```

### 8. LoyaltyException (Base)
- **Khi nào xảy ra**: Các lỗi khác không thuộc các loại trên
- **Cách xử lý**: Lỗi chung, log và thông báo user

```csharp
catch (LoyaltyException ex)
{
    _logger.LogError($"Loyalty error: {ex.Message}");
    // General error handling
}
```

## Cách sử dụng trong Service

### Ví dụ 1: Xử lý toàn diện

```csharp
public async Task<ApiResult<T>> CallLoyaltyApi<T>(string apiUrl, object body)
{
    try
    {
        var result = await _loyaltyService.PostLoyaltyAsyncV2<T>(apiUrl, body);
        return ApiResult<T>.Success(result);
    }
    catch (LoyaltyTimeoutException ex)
    {
        return ApiResult<T>.Error("TIMEOUT", "Request timed out");
    }
    catch (LoyaltyBadRequestException ex)
    {
        return ApiResult<T>.Error("BAD_REQUEST", ex.ResponseContent);
    }
    catch (LoyaltyUnauthorizedException ex)
    {
        return ApiResult<T>.Error("UNAUTHORIZED", "Authentication failed");
    }
    catch (LoyaltyException ex)
    {
        return ApiResult<T>.Error("GENERAL_ERROR", ex.Message);
    }
}
```

### Ví dụ 2: Xử lý có retry

```csharp
public async Task<T> CallWithRetry<T>(string apiUrl, object body, int maxRetries = 3)
{
    for (int i = 0; i < maxRetries; i++)
    {
        try
        {
            return await _loyaltyService.PostLoyaltyAsyncV2<T>(apiUrl, body);
        }
        catch (LoyaltyTimeoutException) when (i < maxRetries - 1)
        {
            await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, i))); // Exponential backoff
        }
        catch (LoyaltyNetworkException) when (i < maxRetries - 1)
        {
            await Task.Delay(TimeSpan.FromSeconds(5)); // Fixed delay for network issues
        }
        catch (LoyaltyBadRequestException)
        {
            throw; // Don't retry bad requests
        }
    }
    throw new Exception("Max retries exceeded");
}
```

## Lưu ý quan trọng

1. **Thứ tự catch**: Luôn catch exception cụ thể trước exception tổng quát
2. **Logging**: Mỗi exception đều có thông tin chi tiết để debug
3. **Retry logic**: Chỉ retry cho timeout và network errors
4. **User experience**: Cung cấp thông báo lỗi phù hợp cho từng loại exception
5. **Monitoring**: Track các loại exception để phát hiện pattern và cải thiện system
