﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.MerchantGift
{
    public class MerchantGiftGetAllCategoryInput
    {
        public string CifCode { get; set; }
        public int? SkipCount { get; set; }
        public int? MaxResultCount { get; set; }
        public string GiftCategoryChannelCode { get; set; }
    }

    public class MerchantGiftGetAllCategoryInputDto
    {
        public string MemberCode { get; set; }
        public string Filter { get; set; }
        public string CodeFilter { get; set; }
        public string NameFilter { get; set; }
        public string DescriptionFilter { get; set; }
        public string StatusFilter { get; set; }
        public int? MaxLevelFilter { get; set; }
        public int? MinLevelFilter { get; set; }
        public string ParentCodeFilter { get; set; }
        public string ParentNameGiftCategoryFilter { get; set; }
        public bool? Is3rdPartyGiftCategory { get; set; }
        public int? SkipCount { get; set; }
        public int? MaxResultCount { get; set; }
        public string Sorting { get; set; }
        public string ParentName { get; set; }
        public string VendorName { get; set; }
        public string Channel { get; set; }
    }
}
