﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.MerchantGift
{
    public class MerchantGiftGetAllGiftOutput
    {
		public int? Id { get; set; }
		public string Code { get; set; }
		public string Name { get; set; }
		public string Description { get; set; }
		public string ShortDescription { get; set; }
		public decimal? RequiredCoin { get; set; }
		public string CategoryCode { get; set; }
		public decimal? InStock { get; set; }
		public bool? IsEgift { get; set; }
		//public string VendorHotline { get; set; }
		//public string Office { get; set; }
		public string Vendor { get; set; }
		public string BrandName { get; set; }
		public string ExpireDuration { get; set; }
		public int TotalWish { get; set; }
		public List<MerchantGiftImageLinkShortDto> ImageLinks { get; set; }
	}

	// For DTO
	public class MerchantGiftImageLinkShortDto
	{
		public string Link { get; set; }
		public string FullLink { get; set; }
	}

	public class MerchantGiftImageLinkDto
	{
		public string Code { get; set; }
		public string Type { get; set; }
		public string Link { get; set; }
		public bool isActive { get; set; }
		public int? Ordinal { get; set; }
		public string FullLink { get; set; }
		public int Id { get; set; }
	}

	public class MerchantGiftGetAllGiftOutputDto
	{
        public MerchantGiftShortInforDto GiftInfor { get; set; }
        public List<MerchantGiftImageLinkDto> ImageLink { get; set; }
        public List<MerchantGiftGroupDto> GiftGroup { get; set; }
        public List<MerchantGiftShortInforForView> RelatedGiftInfor { get; set; }
    }

	public class MerchantGiftGroupDto
	{
		public string Code { get; set; }

		public string Name { get; set; }

		public string Description { get; set; }

		public string Status { get; set; }

		public string Type { get; set; }

		public int Id { get; set; }
	}

	public class MerchantGiftShortInforForView
	{
		public MerchantGiftShortInforDto GiftInfor { get; set; }
		public List<MerchantGiftImageLinkDto> ImageLink { get; set; }
		public MerchantGiftShortInforForView()
		{
			ImageLink = new List<MerchantGiftImageLinkDto>();
		}
	}

	public class MerchantGiftShortInforDto
	{
		public int Id { get; set; }
		public string Code { get; set; }
		public string Name { get; set; }
		public string Description { get; set; }
		public string Introduce { get; set; }
		public string FullGiftCategoryCode { get; set; }
		public string BrandName { get; set; }
		public string ThirdPartyBrandName { get; set; }
		public string Vendor { get; set; }
		public DateTime EffectiveFrom { get; set; }
		public DateTime EffectiveTo { get; set; }
		public decimal RequiredCoin { get; set; }
		public string Status { get; set; }
		public decimal TotalQuantity { get; set; }
		public decimal UsedQuantity { get; set; }
		public decimal RemainingQuantity { get; set; }
		public decimal FullPrice { get; set; }
		public decimal DiscountPrice { get; set; }
		public bool IsEGift { get; set; }
		public string VendorHotline { get; set; }
		public string Tag { get; set; }
		public bool IsInWishlist { get; set; }
		public string RegionCode { get; set; }
		public string Office { get; set; }
		public string ExpireDuration { get; set; }
		public int TotalWish { get; set; }
		public string VendorImage { get; set; }
        public string VendorDescription { get; set; }
        public string BrandLinkLogo { get; set; }
        public string BrandAddress { get; set; }
        public string BrandDescription { get; set; }
	}
}
