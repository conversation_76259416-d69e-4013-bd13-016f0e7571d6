﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.MerchantGift
{
    public class MerchantGiftLocationInput
    {
        public int? SkipCount { get; set; }
        public int? MaxResultCount { get; set; }
    }

    public class MerchantGiftLocationInputDto
    {
        public int? SkipCount { get; set; }
        public int? MaxResultCount { get; set; }
        public int IsActiveFilter { get; set; } = 1; // Only active
    }
}
