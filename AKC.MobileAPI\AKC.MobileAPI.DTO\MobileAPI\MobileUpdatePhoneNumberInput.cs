﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.MobileAPI
{
    public class MobileUpdatePhoneNumberInput
    {
        [Required]
        public string MemberCode { get; set; }

        //[Required]
        [RegularExpression(@"\+[0-9]{11}$", ErrorMessage = "Old phone number is incorrect format")]
        public string OldPhoneNumber { get; set; }

        [Required]
        [RegularExpression(@"\+[0-9]{11}$", ErrorMessage = "Phone number is incorrect format")]
        public string PhoneNumber { get; set; }
    }
}
