﻿using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Const;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.MasterCard;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.LoyaltyVendorGift;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using AKC.MobileAPI.Service.LoyaltyVendorGift.LinkID;
using AKC.MobileAPI.Service.LoyaltyVpbank;
using AKC.MobileAPI.Service.Reward;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class MasterCardService : BaseLoyaltyUtilService, IMasterCardService
    {
        private readonly ILoyaltyMemberService _loyaltyMemberService;
        private readonly ILinkIdLoyaltyVendorGiftService _giftTransactionsService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly IRewardMemberService _rewardMemberService;
        private readonly ILogger _logger;
        private readonly IDistributedCache _cache;
        private readonly BaseLinkIdLoyaltyVendorGiftService _baseLoyaltyService;
        private readonly IRewardBaseService _rewardBaseService;
        private readonly IBaseAdapterService _baseAdapterService;
        protected readonly string GiftGroupChannelId;
        protected readonly string GiftGroupSubType;
        protected readonly string _RedeemSource;
        protected readonly string MerchantId;
        public MasterCardService(
            IConfiguration configuration,
            IDistributedCache cache,
            IRewardBaseService rewardBaseService,
            ILinkIdLoyaltyVendorGiftService giftTransactionsService,
            IBaseAdapterService baseAdapterService,
            IExceptionReponseService exceptionReponseService,
            IRewardMemberService rewardMemberService,
            ILogger<MasterCardService> logger,
            ILogger<BaseLinkIdLoyaltyVendorGiftService> loggerService,
        ILoyaltyMemberService loyaltyMemberService) : base(configuration, cache)
        {
            _loyaltyMemberService = loyaltyMemberService;
            _exceptionReponseService = exceptionReponseService;
            _rewardMemberService = rewardMemberService;
            _giftTransactionsService = giftTransactionsService;
            _logger = logger;
            _cache = cache;
            _baseLoyaltyService = new BaseLinkIdLoyaltyVendorGiftService(configuration, cache, loggerService);
            _rewardBaseService = rewardBaseService;
            _baseAdapterService = baseAdapterService;
            GiftGroupChannelId = _configuration.GetSection("MasterCardDataConfig:GiftGroupChannelId").Value;
            GiftGroupSubType = _configuration.GetSection("MasterCardDataConfig:GiftGroupSubType").Value;
            _RedeemSource = _configuration.GetSection("MasterCardDataConfig:RedeemSource").Value;
            MerchantId = _configuration.GetSection("RewardMasterCard:MerchantId").Value;
        }
        public async Task<MasterCardOutput<MasterCardCheckRegisterOutput>> CustomerCheck(MasterCardCustomerInput input)
        {
            var customer_cache = _cache.GetString(CommonConstants.MASTER_CARD_CUSTOMER_CHECK_KEY + "_" + input.Cif + "_" + input.Lang);
            if (!string.IsNullOrEmpty(customer_cache))
            {
                var output = JsonConvert.DeserializeObject<MasterCardOutput<MasterCardCheckRegisterOutput>>(customer_cache);
                return output;
            }
            else
            {
                var output = await PostLoyaltyAsync<MasterCardOutput<MasterCardCheckRegisterOutput>>(LoyaltyApiUtilsUrl.MASTER_CARD_CUSTOMER_CHECK, input, MasterCardConsts.ErrorTypeKey);
                var cacheOption = new DistributedCacheEntryOptions()
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5)
                };
                _cache.SetString(CommonConstants.MASTER_CARD_CUSTOMER_CHECK_KEY + "_" + input.Cif + "_" + input.Lang, JsonConvert.SerializeObject(output), cacheOption);
                return output;
            }
        }

        public async Task<MasterCardOutput<GiftRedeemedAndCardInfor>> GetAllGiftRedeemed(string Cif, string Lang = "vi")
        {
            GiftRedeemedAndCardInfor output = new GiftRedeemedAndCardInfor();
            var listGroupCodeByCif = await GetLoyaltyAsync<LoyaltyResponsResultDto<GiftRedeemedUtilsOuput>>(LoyaltyApiUtilsUrl.MASTER_CARD_GET_GIFT_REDEEM, new { Cif = Cif, Lang = Lang }, MasterCardConsts.ErrorTypeKey);
            if (listGroupCodeByCif != null && listGroupCodeByCif.TotalCount > 0)
            {
                var memberCode = listGroupCodeByCif.Items.First().MemberCode;
                // Get ds quà đã đổi
                var lstGroupCode = listGroupCodeByCif.Items.Select(x => x.GiftGroupCode).Distinct().ToList();
                var lstGift = await _baseLoyaltyService.GetLoyaltyAsync<LoyaltyResponseList<GiftRedeemedLoyaltyOuput>>(LoyaltyApiUrl.REDEEM_GET_GIFT_REDEEMED, new
                {
                    MemberCode = memberCode,
                    GiftGroupCode = string.Join(";", lstGroupCode),
                    RedeemSource = _RedeemSource,
                    Lang = Lang,
                    SkipCount = 0,
                    MaxResultCount = 9999
                });
                if (lstGift != null && lstGift.Success && lstGift.Result != null && lstGift.Result.TotalCount > 0)
                {
                    var giftRedeemNotExpired = lstGift.Result.Items.Where(x => x.EGiftExpiredDate >= DateTime.UtcNow).Select(y =>
                    {
                        if (y.EGiftExpiredDate.HasValue)
                        {
                            y.EGiftExpiredDate = DateTime.Parse(y.EGiftExpiredDate.Value.AddHours(7).ToString("yyyy-MM-ddTHH:mm:ss.999"));
                        }
                        return y;
                    }).OrderBy(z => z.EGiftExpiredDate).ToList();
                    var giftRedeemExpired = lstGift.Result.Items.Where(x => x.EGiftExpiredDate < DateTime.UtcNow).Select(y =>
                    {
                        if (y.EGiftExpiredDate.HasValue)
                        {
                            y.EGiftExpiredDate = DateTime.Parse(y.EGiftExpiredDate.Value.AddHours(7).ToString("yyyy-MM-ddTHH:mm:ss.999"));
                        }
                        return y;
                    }).OrderBy(z => z.EGiftExpiredDate).ToList();
                    giftRedeemNotExpired.AddRange(giftRedeemExpired);
                    output.GiftRedeemeds = giftRedeemNotExpired;
                }
                // Get ds thẻ điểm
                var rewardCard = await _rewardBaseService.GetRewardAsync<GetCardResponse>(RewardApiUrl.MASTER_CARD_GET_CARD_LIST,
                    new { MemberCode = memberCode, Status = "A", IsRedeem = 1, MerchantId = MerchantId, skipCount = 0, maxResultCount = 9999 }, "MasterCard"
                );
                if (rewardCard != null && rewardCard.Items != null && rewardCard.Items.Count > 0)
                {
                    var listCardCode = rewardCard.Items.Select(x => x.CardCode).ToList();
                    var lstCard = await GetLoyaltyAsync<LoyaltyResponsResultDto<MasterCardListCardCoin>>(LoyaltyApiUtilsUrl.MASTER_CARD_GET_CARD_COIN, new { Cif = Cif, CardCode = string.Join(",", listCardCode), Lang = Lang }, MasterCardConsts.ErrorTypeKey);
                    output.CardInfors = new List<MasterCardCardCoinOuput>();
                    if (lstCard != null && lstCard.Items != null && lstCard.Items.Count > 0)
                    {
                        foreach (var CardFromReward in rewardCard.Items)
                        {
                            var CardFromVP = lstCard.Items.FirstOrDefault(x => x.Challenge.CardCode == CardFromReward.CardCode);
                            if (CardFromVP != null)
                            {
                                var item = new MasterCardCardCoinOuput()
                                {
                                    Campaign = CardFromVP.Campaign,
                                    Cif = CardFromVP.Cif,
                                    Status = CardFromReward?.Status,
                                    Challenge = new ChallengeInforCardOutput()
                                    {
                                        ActualAmount = CardFromVP.Challenge?.ActualAmount,
                                        Status = CardFromReward?.Status,
                                        ActualTnx = CardFromVP.Challenge.ActualTnx,
                                        CampaignId = CardFromVP.Challenge.CampaignId,
                                        CardCode = CardFromReward?.CardCode,
                                        CardExpiredDate = CardFromReward.ExpiryDate.HasValue ? CardFromReward.ExpiryDate.Value.ToString("yyyy-MM-ddTHH:mm:ss.fff") : null,
                                        CoinStatus = CardFromVP.Challenge.CoinStatus,
                                        GiftCoin = CardFromReward?.TokenAmount,
                                        GoalDate = CardFromVP.Challenge.GoalDate,
                                        RemainGiftCoin = CardFromReward?.RemainingAmount,
                                        TotalActualAmount = CardFromVP.Challenge.TotalActualAmount,
                                        TotalActualTnx = CardFromVP.Challenge.TotalActualTnx,
                                        TransactionCode = CardFromVP.Challenge.TransactionCode,
                                        MerChantPhoto = CardFromReward?.MerChantPhoto,
                                        GiftInfor = CardFromVP.Challenge.GiftRemdeemCodes != null && output.GiftRedeemeds != null ? output.GiftRedeemeds.Where(x => CardFromVP.Challenge.GiftRemdeemCodes.Contains(x.TransactionCode)).ToList() : new List<GiftRedeemedLoyaltyOuput>()
                                    }
                                };
                                output.CardInfors.Add(item);
                            }
                        }
                        if (output.CardInfors.Count > 0)
                        {
                            output.CardInfors.OrderBy(x => x.Challenge.CardExpiredDate).ToList();
                        }
                    }
                }
            }
            return new MasterCardOutput<GiftRedeemedAndCardInfor>()
            {
                code = "00",
                message = "Success",
                data = output
            };
        }

        public async Task<LoyaltyResponsResultDto<MasterCardCampaignOutput>> GetCampaignByCustomer(string Cif, string Status, string Lang = "vi")
        {
            var lstCampaign = await GetLoyaltyAsync<LoyaltyResponsResultDto<MasterCardCampaignOutput>>(LoyaltyApiUtilsUrl.MASTER_CARD_GET_CAMPAIGN, new { Cif = Cif, Status = Status, Lang = Lang }, MasterCardConsts.ErrorTypeKey);
            if (lstCampaign != null && lstCampaign.TotalCount > 0)
            {
                if (lstCampaign.Items.Any(x => x.Status == "S"))
                {
                    var customerInfor = await GetLoyaltyAsync<MasterCardOutput<MasterCardCustomer>>(LoyaltyApiUtilsUrl.MASTER_CARD_GET_CUSTOMER_INFOR, new { Cif = Cif }, MasterCardConsts.ErrorTypeKey);
                    var rewardCard = await _rewardBaseService.GetRewardAsync<GetCardResponse>(RewardApiUrl.MASTER_CARD_GET_CARD_LIST,
                        new { MemberCode = customerInfor.data.MemberCode, MerchantId = MerchantId, skipCount = 0, maxResultCount = 9999 }, "MasterCard"
                    );
                    foreach (var item in lstCampaign.Items)
                    {
                        if (item.Status == "S")
                        {
                            var cardCurrent = rewardCard != null && rewardCard.Items != null && rewardCard.Items.Count > 0 ? rewardCard.Items.FirstOrDefault(x => x.CardCode == item.Challenge.CardCode) : new CardObject();
                            if (cardCurrent == null)
                            {
                                cardCurrent = new CardObject();
                            }
                            item.Challenge.Status = cardCurrent.Status;
                            item.Challenge.CardExpiredDate = cardCurrent.ExpiryDate.HasValue ? cardCurrent.ExpiryDate.Value.AddHours(7).ToString("yyyy-MM-ddTHH:mm:ss.fff") : null;
                            item.Challenge.GiftCoin = cardCurrent?.TokenAmount;
                            item.Challenge.RemainGiftCoin = cardCurrent?.RemainingAmount;
                            item.Challenge.CoinStatus = cardCurrent?.RemainingAmount != cardCurrent?.TokenAmount ? "REDEEMED" : "";
                        }
                    }
                }
            }
            return lstCampaign;
        }

        public async Task<LoyaltyResponsResultDto<MasterCardCampaignOutput>> GetCardCoin(string Cif, string Lang = "vi")
        {
            var output = new LoyaltyResponsResultDto<MasterCardCampaignOutput>();
            var customerInfor = await GetLoyaltyAsync<MasterCardOutput<MasterCardCustomer>>(LoyaltyApiUtilsUrl.MASTER_CARD_GET_CUSTOMER_INFOR, new { Cif = Cif }, MasterCardConsts.ErrorTypeKey);
            var rewardCard = await _rewardBaseService.GetRewardAsync<GetCardResponse>(RewardApiUrl.MASTER_CARD_GET_CARD_LIST,
                new { MemberCode = customerInfor.data.MemberCode, Status = "A", MerchantId = MerchantId, skipCount = 0, maxResultCount = 9999 }, "MasterCard"
            );
            if (rewardCard != null && rewardCard.Items != null && rewardCard.Items.Count > 0)
            {
                var listCardCode = rewardCard.Items.Select(x => x.CardCode).ToList();
                output = await GetLoyaltyAsync<LoyaltyResponsResultDto<MasterCardCampaignOutput>>(LoyaltyApiUtilsUrl.MASTER_CARD_GET_CARD_COIN, new { Cif = Cif, CardCode = string.Join(",", listCardCode), Lang = Lang }, MasterCardConsts.ErrorTypeKey);
            }
            return output;
        }

        public async Task<LoyaltyResponsResultDto<MasterCardGiftByCampaignOutput>> GetGiftByCampaign(string Cif, string CampaignId, string Lang = "vi")
        {
            var checkRegister = await PostLoyaltyAsync<MasterCardOutput<CustomerInforByCardCode>>(LoyaltyApiUtilsUrl.MASTER_CARD_CHECK_CIF_WITH_CAMPAIGN, new { Cif = Cif, CampaignId = !string.IsNullOrEmpty(CampaignId) ? Int32.Parse(CampaignId) : 0 }, MasterCardConsts.ErrorTypeKey);
            if (checkRegister != null && checkRegister.code == "00" && checkRegister.data != null && !string.IsNullOrEmpty(checkRegister.data.Cif))
            {
                var giftGroupCode = "";
                List<CampaignConfigInfor> lstCampaign = new List<CampaignConfigInfor>();
                var campaigns_cache = _cache.GetString(CommonConstants.MASTER_CARD_CAMPAIGN_CACHE_KEY);
                if (!string.IsNullOrEmpty(campaigns_cache))
                {
                    lstCampaign = JsonConvert.DeserializeObject<List<CampaignConfigInfor>>(campaigns_cache);
                    var current_campaign = lstCampaign.Find(x => x.Campaign.Id == int.Parse(CampaignId));
                    if (current_campaign != null && current_campaign.BaseSetting != null && current_campaign.BaseSetting.Count > 0)
                    {
                        var current_base = current_campaign.BaseSetting.FirstOrDefault(x => x.EffectiveFrom >= checkRegister.data.StartDate && checkRegister.data.EndDate >= x.EffectiveTo);
                        giftGroupCode = current_base != null ? current_base.GiftGroupCode : "";
                    }
                }
                else
                {
                    lstCampaign = await PostLoyaltyAsync<List<CampaignConfigInfor>>(LoyaltyApiUtilsUrl.MASTER_CARD_CAMPAIGN_INFOR, new GetGiftByCampaignInput(), MasterCardConsts.ErrorTypeKey);
                    if (lstCampaign != null && lstCampaign.Count > 0)
                    {
                        var current_campaign = lstCampaign.Find(x => x.Campaign.Id == int.Parse(CampaignId));
                        if (current_campaign != null && current_campaign.BaseSetting != null && current_campaign.BaseSetting.Count > 0)
                        {
                            var current_base = current_campaign.BaseSetting.FirstOrDefault(x => x.EffectiveFrom >= checkRegister.data.StartDate && checkRegister.data.EndDate >= x.EffectiveTo);
                            giftGroupCode = current_base != null ? current_base.GiftGroupCode : "";
                        }
                        var cacheOption = new DistributedCacheEntryOptions()
                        {
                            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5)
                        };
                        _cache.SetString(CommonConstants.MASTER_CARD_CAMPAIGN_CACHE_KEY, JsonConvert.SerializeObject(lstCampaign), cacheOption);
                    }
                }
                // Nếu có quà từ cache thì lấy ra
                // Nếu không thì lấy quà từ Lynkid loyalty
                var campaigns_gifts = _cache.GetString(CommonConstants.MASTER_CARD_CAMPAIGN_GIFT_CACHE_KEY + "_" + giftGroupCode + "_" + Lang);
                if (!string.IsNullOrEmpty(campaigns_gifts))
                {
                    var listGift = JsonConvert.DeserializeObject<LoyaltyResponsResultDto<MasterCardGiftByCampaignOutput>>(campaigns_gifts);
                    return listGift;
                }
                else
                {
                    var customerInfor = await GetLoyaltyAsync<MasterCardOutput<MasterCardCustomer>>(LoyaltyApiUtilsUrl.MASTER_CARD_GET_CUSTOMER_INFOR, new { Cif = Cif }, MasterCardConsts.ErrorTypeKey);
                    var listGift = await _baseLoyaltyService.GetLoyaltyAsync<LoyaltyResponseList<MasterCardGiftOutput>>(LoyaltyApiUrl.GET_GIFT_BY_GROUP_CHANNEL,
                    new
                    {
                        MemberCode = customerInfor.data.MemberCode,
                        ChannelId = GiftGroupChannelId,
                        ReferenceCode = CampaignId,
                        GiftGroupCode = giftGroupCode,
                        SubType = GiftGroupSubType,
                        Lang = Lang,
                        SkipCount = 0,
                        MaxResultCount = 9999
                    });

                    if (listGift != null && listGift.Success && listGift.Result != null && listGift.Result.Items != null && listGift.Result.Items.Count > 0)
                    {
                        var lstGiftFromLoyalty = listGift.Result.Items.Select(x => new MasterCardGiftByCampaignOutput()
                        {
                            GiftInfor = new GiftRedeemedLoyaltyOuput()
                            {
                                Amount = x.GiftInfor.RequiredCoin,
                                BrandLogo = x.GiftInfor.BrandLinkLogo,
                                BrandName = x.GiftInfor.BrandName,
                                GiftCode = x.GiftInfor.Code,
                                GiftCondition = x.GiftInfor.Condition,
                                GiftDescription = x.GiftInfor.Description,
                                GiftIntroduce = x.GiftInfor.Introduce,
                                GiftName = x.GiftInfor.Name,
                                GiftPhoto = x.ImageLink != null && x.ImageLink.Count > 0 ? x.ImageLink.FirstOrDefault(y => y.isActive)?.FullLink : "",
                                VendorLogo = x.GiftInfor.VendorImage,
                                VendorName = x.GiftInfor.VendorName,
                                GiftUsageAddress = x.GiftUsageAddres != null && x.GiftUsageAddres.Count > 0 ? string.Format("<br/><p style=\"font-size: 16px;font-weight: bold\">{0}: </p> {1}", Lang == "en" ? "Usage address" : "Địa điểm sử dụng", string.Join("", x.GiftUsageAddres.Select(y => "<p style=\"margin: 10px 0px;\">- " + (y.Address?.ToUpper() == y.Name?.ToUpper() ? y.Address : y.Name + ", " + y.Address) + "</p>").ToList())) : ""
                            }
                        }).ToList();
                        var cacheOption = new DistributedCacheEntryOptions()
                        {
                            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5)
                        };
                        var dataCache = new LoyaltyResponsResultDto<MasterCardGiftByCampaignOutput>()
                        {
                            Items = lstGiftFromLoyalty,
                            TotalCount = lstGiftFromLoyalty.Count
                        };
                        _cache.SetString(CommonConstants.MASTER_CARD_CAMPAIGN_GIFT_CACHE_KEY + "_" + giftGroupCode + "_" + Lang, JsonConvert.SerializeObject(dataCache), cacheOption);
                        return dataCache;
                    }
                    else
                    {
                        return new LoyaltyResponsResultDto<MasterCardGiftByCampaignOutput>();
                    }
                }
            }
            else
            {
                return new LoyaltyResponsResultDto<MasterCardGiftByCampaignOutput>();
            }
        }
        public async Task<LoyaltyResponsResultDto<MasterCardGiftByCampaignGroupBrandOutput>> GetGiftByCampaignGroupBrand(string Cif, string CampaignId, string CardCode, string Lang = "vi")
        {
            var checkRegister = await PostLoyaltyAsync<MasterCardOutput<CustomerInforByCardCode>>(LoyaltyApiUtilsUrl.MASTER_CARD_CHECK_CIF_WITH_CAMPAIGN, new { Cif = Cif, CampaignId = !string.IsNullOrEmpty(CampaignId) ? Int32.Parse(CampaignId) : 0, CardCode = CardCode }, MasterCardConsts.ErrorTypeKey);
            if (checkRegister != null && checkRegister.code == "00" && checkRegister.data != null && !string.IsNullOrEmpty(checkRegister.data.Cif))
            {
                var giftGroupCode = "";
                List<CampaignConfigInfor> lstCampaign = new List<CampaignConfigInfor>();
                var campaigns_cache = _cache.GetString(CommonConstants.MASTER_CARD_CAMPAIGN_CACHE_KEY);
                if (!string.IsNullOrEmpty(campaigns_cache))
                {
                    lstCampaign = JsonConvert.DeserializeObject<List<CampaignConfigInfor>>(campaigns_cache);
                    var current_campaign = lstCampaign.Find(x => x.Campaign.Id == int.Parse(CampaignId));
                    if (current_campaign != null && current_campaign.BaseSetting != null && current_campaign.BaseSetting.Count > 0)
                    {
                        var current_base = current_campaign.BaseSetting.FirstOrDefault(x => x.EffectiveFrom >= checkRegister.data.StartDate && checkRegister.data.EndDate >= x.EffectiveTo);
                        giftGroupCode = current_base != null ? current_base.GiftGroupCode : "";
                    }
                }
                else
                {
                    lstCampaign = await PostLoyaltyAsync<List<CampaignConfigInfor>>(LoyaltyApiUtilsUrl.MASTER_CARD_CAMPAIGN_INFOR, new GetGiftByCampaignInput(), MasterCardConsts.ErrorTypeKey);
                    if (lstCampaign != null && lstCampaign.Count > 0)
                    {
                        var current_campaign = lstCampaign.Find(x => x.Campaign.Id == int.Parse(CampaignId));
                        if (current_campaign != null && current_campaign.BaseSetting != null && current_campaign.BaseSetting.Count > 0)
                        {
                            var current_base = current_campaign.BaseSetting.FirstOrDefault(x => x.EffectiveFrom >= checkRegister.data.StartDate && checkRegister.data.EndDate >= x.EffectiveTo);
                            giftGroupCode = current_base != null ? current_base.GiftGroupCode : "";
                        }
                        var cacheOption = new DistributedCacheEntryOptions()
                        {
                            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5)
                        };
                        _cache.SetString(CommonConstants.MASTER_CARD_CAMPAIGN_CACHE_KEY, JsonConvert.SerializeObject(lstCampaign), cacheOption);
                    }
                }
                // Nếu có quà từ cache thì lấy ra
                // Nếu không thì lấy quà từ Lynkid loyalty
                var campaigns_gifts = _cache.GetString(CommonConstants.MASTER_CARD_CAMPAIGN_GIFT_GROUP_BRAND_CACHE_KEY + "_" + giftGroupCode + "_" + Lang);
                if (!string.IsNullOrEmpty(campaigns_gifts))
                {
                    var listGift = JsonConvert.DeserializeObject<LoyaltyResponsResultDto<MasterCardGiftByCampaignGroupBrandOutput>>(campaigns_gifts);
                    return listGift;
                }
                else
                {
                    var customerInfor = await GetLoyaltyAsync<MasterCardOutput<MasterCardCustomer>>(LoyaltyApiUtilsUrl.MASTER_CARD_GET_CUSTOMER_INFOR, new { Cif = Cif }, MasterCardConsts.ErrorTypeKey);
                    if (customerInfor != null && customerInfor.code == "00")
                    {

                        var listGift = await _baseLoyaltyService.GetLoyaltyAsync<LoyaltyResponseList<MasterCardGiftOutput>>(LoyaltyApiUrl.GET_GIFT_BY_GROUP_CHANNEL,
                        new
                        {
                            MemberCode = customerInfor.data.MemberCode,
                            ChannelId = GiftGroupChannelId,
                            ReferenceCode = CampaignId,
                            GiftGroupCode = giftGroupCode,
                            SubType = GiftGroupSubType,
                            Lang = Lang,
                            SkipCount = 0,
                            MaxResultCount = 9999
                        });

                        if (listGift != null && listGift.Success && listGift.Result != null && listGift.Result.Items != null && listGift.Result.Items.Count > 0)
                        {
                            var lstOutput = new List<MasterCardGiftByCampaignGroupBrandOutput>();
                            var lstBrand = listGift.Result.Items.Select(x => x.GiftInfor.BrandId).Distinct().ToList();
                            if (lstBrand != null && lstBrand.Count > 0)
                            {
                                foreach (var item in lstBrand)
                                {
                                    var outputItem = new MasterCardGiftByCampaignGroupBrandOutput();
                                    outputItem.BrandId = item;
                                    outputItem.GiftInfors = listGift.Result.Items.Where(x => x.GiftInfor.BrandId == item).Select(x => new GiftRedeemedLoyaltyOuput()
                                    {
                                        Amount = x.GiftInfor.RequiredCoin,
                                        BrandLogo = x.GiftInfor.BrandLinkLogo,
                                        BrandId = x.GiftInfor.BrandId,
                                        BrandName = x.GiftInfor.BrandName,
                                        GiftCode = x.GiftInfor.Code,
                                        GiftCondition = x.GiftInfor.Condition,
                                        GiftDescription = x.GiftInfor.Description,
                                        GiftIntroduce = x.GiftInfor.Introduce,
                                        GiftName = x.GiftInfor.Name,
                                        GiftPhoto = x.ImageLink != null && x.ImageLink.Count > 0 ? x.ImageLink.FirstOrDefault(y => y.isActive)?.FullLink : "",
                                        VendorLogo = x.GiftInfor.VendorImage,
                                        VendorName = x.GiftInfor.VendorName,
                                        GiftUsageAddress = x.GiftUsageAddres != null && x.GiftUsageAddres.Count > 0 ? string.Format("<br/><p style=\"font-size: 16px;font-weight: bold\">{0}: </p> {1}", Lang == "en" ? "Usage address" : "Địa điểm sử dụng", string.Join("", x.GiftUsageAddres.Select(y => "<p style=\"margin: 10px 0px;\">- " + (y.Address?.ToUpper() == y.Name?.ToUpper() ? y.Address : y.Name + ", " + y.Address) + "</p>").ToList())) : ""
                                    }).ToList();
                                    outputItem.BrandName = outputItem.GiftInfors != null && outputItem.GiftInfors.Count > 0 ? outputItem.GiftInfors[0].BrandName : "";
                                    lstOutput.Add(outputItem);
                                }
                            }
                            var cacheOption = new DistributedCacheEntryOptions()
                            {
                                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5)
                            };
                            var dataCache = new LoyaltyResponsResultDto<MasterCardGiftByCampaignGroupBrandOutput>()
                            {
                                Items = lstOutput,
                                TotalCount = lstOutput.Count
                            };
                            _cache.SetString(CommonConstants.MASTER_CARD_CAMPAIGN_GIFT_GROUP_BRAND_CACHE_KEY + "_" + giftGroupCode + "_" + Lang, JsonConvert.SerializeObject(dataCache), cacheOption);
                            return dataCache;
                        }
                        else
                        {
                            return new LoyaltyResponsResultDto<MasterCardGiftByCampaignGroupBrandOutput>();
                        }
                    }
                    else
                    {
                        return new LoyaltyResponsResultDto<MasterCardGiftByCampaignGroupBrandOutput>();
                    }
                }
            }
            else
            {
                return new LoyaltyResponsResultDto<MasterCardGiftByCampaignGroupBrandOutput>();
            }
        }
        public async Task<ChallengeInforAfterUpdateOutput> GetInforChallenge(string Cif, string CampaignId, string TransactionCode, string Lang = "vi")
        {
            ChallengeInforAfterUpdateOutput output = new ChallengeInforAfterUpdateOutput();
            var checkRegister = await PostLoyaltyAsync<MasterCardOutput<CustomerInforByCardCode>>(LoyaltyApiUtilsUrl.MASTER_CARD_CHECK_CIF_WITH_CAMPAIGN, new { Cif = Cif, CampaignId = !string.IsNullOrEmpty(CampaignId) ? Int32.Parse(CampaignId) : 0 }, MasterCardConsts.ErrorTypeKey);
            if (checkRegister != null && checkRegister.code == "00")
            {
                _logger.LogInformation($" >> GetInforChallenge >> call to adapter with input = {JsonConvert.SerializeObject(new { CIF = Cif, RegisterCampaign = CampaignId, TransactionCode = TransactionCode })}");
                var dataProcessing = await _baseAdapterService.GetAdapterAsync<AdapterResponse<ChallengeProcessingOutput>>(AdapterApiUrl.MASTER_CARD_GET_PROCESSING, new { CIF = Cif, RegisterCampaign = CampaignId, TransactionCode = TransactionCode });
                _logger.LogInformation($" >> GetInforChallenge >> call to adapter Output = {JsonConvert.SerializeObject(dataProcessing)}");
                if (dataProcessing != null && dataProcessing.Status == true)
                {
                    var challenge = await PostLoyaltyAsync<MasterCardOutput<ChallengeInfor>>(LoyaltyApiUtilsUrl.MASTER_CARD_GET_UPDATE_PROCESSING_CHALLENGE,
                    new
                    {
                        Cif = Cif,
                        TransactionCode = TransactionCode,
                        CampaignId = CampaignId,
                        totalActualAmount = dataProcessing.data.TotalActualAmount,
                        totalActualTnx = dataProcessing.data.TotalActualTnx
                    }, MasterCardConsts.ErrorTypeKey);
                    if (challenge != null && challenge.code == "00" && challenge.data != null)
                    {
                        output = new ChallengeInforAfterUpdateOutput()
                        {
                            ActualAmount = challenge.data.ActualAmount,
                            ActualTnx = challenge.data.ActualTnx,
                            BigPhoto = challenge.data.BigPhoto,
                            CampaignId = challenge.data.CampaignId,
                            CardCode = challenge.data.CardCode,
                            CardExpiredDate = challenge.data.CardExpiredDate,
                            CoinStatus = challenge.data.CoinStatus,
                            GiftCoin = challenge.data.GiftCoin,
                            GoalDate = challenge.data.GoalDate,
                            RemainGiftCoin = challenge.data.RemainGiftCoin,
                            SmallPhoto = challenge.data.SmallPhoto,
                            Status = challenge.data.Status,
                            TotalActualAmount = challenge.data.TotalActualAmount,
                            TotalActualTnx = challenge.data.TotalActualTnx,
                            TransactionCode = challenge.data.TransactionCode
                        };
                    }
                    else if (challenge != null && challenge.code != "00" && challenge.data != null)
                    {
                        output = new ChallengeInforAfterUpdateOutput()
                        {
                            ActualAmount = challenge.data.ActualAmount,
                            ActualTnx = challenge.data.ActualTnx,
                            BigPhoto = challenge.data.BigPhoto,
                            CampaignId = challenge.data.CampaignId,
                            CardCode = challenge.data.CardCode,
                            CardExpiredDate = challenge.data.CardExpiredDate,
                            CoinStatus = challenge.data.CoinStatus,
                            GiftCoin = challenge.data.GiftCoin,
                            GoalDate = challenge.data.GoalDate,
                            RemainGiftCoin = challenge.data.RemainGiftCoin,
                            SmallPhoto = challenge.data.SmallPhoto,
                            Status = challenge.data.Status,
                            TotalActualAmount = challenge.data.TotalActualAmount,
                            TotalActualTnx = challenge.data.TotalActualTnx,
                            TransactionCode = challenge.data.TransactionCode,
                            Warning = new ChallengeInforWarning()
                            {
                                code = challenge.code,
                                message = challenge.message
                            }
                        };
                    }
                }
            }
            return output;
        }

        public async Task<MasterCardRedeemGiftOutput> RedeemGift(MasterCardRedeemGiftInput input)
        {
            var trans = new MasterCardRedeemGiftOutput();
            var cardInfor = await GetLoyaltyAsync<MasterCardOutput<GetCardInforForRedeemOutput>>(LoyaltyApiUtilsUrl.MASTER_CARD_GET_CARD_BY_CARD_CODE, new { Cif = input.Cif, CardCode = input.CardCode, TotalAmount = input.TotalAmount }, MasterCardConsts.ErrorTypeKey);
            if (cardInfor != null && cardInfor.code == "00" && cardInfor.data != null)
            {
                // Update RedeemStatus = REDEEMING
                var updateRedeeming = await PostLoyaltyAsync<MasterCardOutput<string>>(LoyaltyApiUtilsUrl.MASTER_CARD_UPDATE_REDEEM_STATUS_CARD, new { Cif = input.Cif, CardCode = input.CardCode, RedeemStatus = "REDEEMING" }, MasterCardConsts.ErrorTypeKey);
                if (updateRedeeming != null && updateRedeeming.code == "00")
                {
                    var merchantByGift = cardInfor.data.MerchantId;
                    try
                    {
                        _logger.LogInformation($"Master_Card_RedeemMultiGift >> Get MerchantId infor from GiftCode");
                        var merchantIdFromGiftCode = await _giftTransactionsService.GetMerchantIdFromGiftCode(new LoyaltyGiftGetMerchantIdFromGiftCodeInput()
                        {
                            GiftCode = input.giftCode
                        });
                        if (merchantIdFromGiftCode == null || !merchantIdFromGiftCode.Success || merchantIdFromGiftCode.Result == null || !merchantIdFromGiftCode.Result.MerchantId.HasValue)
                        {
                            _logger.LogError($"Master_Card_RedeemMultiGift >> Cannot find merchantId by GiftCode {input.giftCode}");
                        }
                        else
                        {
                            merchantByGift = merchantIdFromGiftCode.Result.MerchantId.Value.ToString();
                        }
                    }
                    catch (Exception)
                    {
                        _logger.LogError("Start RedeemMultiGift Reward Redeem card Cif:" + input.Cif + " --- Gift Code: " + input.giftCode);
                    }
                    // Call API ghi nhận đổi quà bên LynkId loyalty
                    try
                    {
                        _logger.LogInformation("Start Master_Card_RedeemGift create Redeem Order Cif:" + input.Cif);
                        var createRedeemOrder = await _baseLoyaltyService.PostLoyaltyAsync<LoyaltyResponse<CreateRedeemGiftOrderResponse>>(LoyaltyApiUrl.VERIFY_AND_CREATE_REDEEM_ORDER, new
                        {
                            giftGroupCode = cardInfor.data.GiftGroupCode,
                            cardCode = input.CardCode,
                            memberCode = cardInfor.data.MemberCode,
                            giftCode = input.giftCode,
                            quantity = input.Quantity,
                            totalAmount = input.TotalAmount,
                            date = DateTime.UtcNow,
                            redeemSource = _RedeemSource,
                            merchantIdRedeem = merchantByGift
                        });
                        if (createRedeemOrder != null && createRedeemOrder.Success && createRedeemOrder.Result != null && createRedeemOrder.Result.isSuccess)
                        {
                            // Trừ điểm bên reward
                            var listCard = new List<string>();
                            listCard.Add(input.CardCode);
                            if (!string.IsNullOrEmpty(input.PhoneNumber) && input.PhoneNumber.StartsWith("0"))
                            {
                                input.PhoneNumber = "+84" + input.PhoneNumber.Substring(1);
                            }
                            try
                            {
                                _logger.LogInformation("Start Master_Card_RedeemGift Reward card redeem Cif:" + input.Cif);
                                var redeemCardCoin = await _rewardBaseService.PostRewardAsync<RewardRedeemCardResponse>(RewardApiUrl.MASTER_CARD_REDEEM_CARD, new
                                {
                                    MemberCode = cardInfor.data.MemberCode,
                                    PhoneNumber = "",
                                    MoneyCardIds = listCard,
                                    TokenAmount = input.TotalAmount,
                                    MerchantId = MerchantId,
                                    Reason = "REDEEM"
                                }, "MasterCard");
                                if (redeemCardCoin != null && redeemCardCoin.result == 200 && redeemCardCoin.item != null && redeemCardCoin.item.Count > 0)
                                {
                                    try
                                    {
                                        // Active quà cho khách hàng theo transactionCode
                                        _logger.LogInformation("Start Master_Card_RedeemGift Redeem Gift Cif:" + input.Cif);
                                        var activeTransaction = await _baseLoyaltyService.PostLoyaltyAsync<LoyaltyResponse<RedeemGiftResponse>>(LoyaltyApiUrl.REDEEM_GIFT_BY_GROUP_CODE, new
                                        {
                                            giftGroupCode = cardInfor.data.GiftGroupCode,
                                            cardCode = input.CardCode,
                                            cardCodeTx = redeemCardCoin.item[0].OrderCode,
                                            memberCode = cardInfor.data.MemberCode,
                                            transactionCode = createRedeemOrder.Result.transactionCode,
                                            giftCode = input.giftCode,
                                            quantity = input.Quantity,
                                            totalAmount = input.TotalAmount,
                                            date = DateTime.UtcNow,
                                            redeemSource = _RedeemSource,
                                            merchantIdRedeem = merchantByGift
                                        });
                                        if (activeTransaction != null && activeTransaction.Success && activeTransaction.Result != null && activeTransaction.Result.isSuccess)
                                        {
                                            trans.Cif = input.Cif;
                                            trans.GiftTransactionCode = activeTransaction.Result.transactionCode;
                                            trans.EgiftCode = activeTransaction.Result.EgiftCode;
                                            trans.ExpiredDate = activeTransaction.Result.ExpiredDate.HasValue ? DateTime.Parse(activeTransaction.Result.ExpiredDate.Value.AddHours(7).ToString("yyyy-MM-ddTHH:mm:ss.999")) : activeTransaction.Result.ExpiredDate;
                                            try
                                            {
                                                await PostLoyaltyAsync<MasterCardOutput<string>>(LoyaltyApiUtilsUrl.MASTER_CARD_UPDATE_REDEEM_GIFT, new
                                                {
                                                    Cif = input.Cif,
                                                    PhoneNumber = input.PhoneNumber,
                                                    Quantity = input.Quantity,
                                                    GiftCode = input.giftCode,
                                                    CardCode = input.CardCode,
                                                    TotalAmount = input.TotalAmount,
                                                    GiftTransactionCode = trans.GiftTransactionCode,
                                                    RewardOrderCode = redeemCardCoin.item[0].OrderCode,
                                                    EgiftCode = trans.EgiftCode,
                                                    GiftGroupCode = cardInfor.data.GiftGroupCode,
                                                    MemberCode = cardInfor.data.MemberCode,
                                                    ExpiredDate = trans.ExpiredDate,
                                                    Description = input.Description,
                                                }, MasterCardConsts.ErrorTypeKey);
                                            }
                                            catch (LoyaltyException ex)
                                            {
                                                _logger.LogError("Update status after RedeemGift false:" + ex.Message + " --- StackTrace: " + ex.StackTrace);
                                            }
                                        }
                                        else
                                        {
                                            _logger.LogError("Master_Card_Redeem_Active_Transaction Error:" + JsonConvert.SerializeObject(activeTransaction));
                                            if (!activeTransaction.Result.Timeout)
                                            {
                                                await _rewardBaseService.PostRewardAsync<RewardReturnCardResponse>(RewardApiUrl.MASTER_CARD_RETURN_CARD,
                                                new
                                                {
                                                    OrderCode = redeemCardCoin.item[0].OrderCode,
                                                    MemberCode = cardInfor.data.MemberCode,
                                                    PhoneNumber = "",
                                                    Reason = "RETURN",
                                                }, "MasterCard");
                                                await PostLoyaltyAsync<MasterCardOutput<string>>(LoyaltyApiUtilsUrl.MASTER_CARD_UPDATE_REDEEM_STATUS_CARD, new { Cif = input.Cif, CardCode = input.CardCode }, MasterCardConsts.ErrorTypeKey);
                                            }
                                            else
                                            {
                                                throw new TimeoutException("Request merchant time out!");
                                            }
                                        }
                                    }
                                    catch (LoyaltyException ex)
                                    {
                                        _logger.LogError("LoyaltyException Master_Card_Redeem_Active_Transaction Cif: " + input.Cif + " Error:" + ex.Message);
                                        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                                        if (res.Code != "0" && res.Code != "SystemError")
                                        {
                                            await _rewardBaseService.PostRewardAsync<RewardReturnCardResponse>(RewardApiUrl.MASTER_CARD_RETURN_CARD, new
                                            {
                                                OrderCode = redeemCardCoin.item[0].OrderCode,
                                                MemberCode = cardInfor.data.MemberCode,
                                                PhoneNumber = "",
                                                Reason = "RETURN",
                                            }, "MasterCard");
                                        }
                                        throw ex;
                                    }
                                }
                                else
                                {
                                    _logger.LogError("Master_Card_redeem Redeem Reward Card Coin Error:" + JsonConvert.SerializeObject(redeemCardCoin));
                                    await PostLoyaltyAsync<MasterCardOutput<string>>(LoyaltyApiUtilsUrl.MASTER_CARD_UPDATE_REDEEM_STATUS_CARD, new { Cif = input.Cif, CardCode = input.CardCode }, MasterCardConsts.ErrorTypeKey);
                                }
                            }
                            catch (RewardException ex)
                            {
                                _logger.LogError("RewardException Master_Card_redeem Redeem Reward Card Coin Cif: " + input.Cif + " Error:" + ex.Message);
                                throw ex;
                            }
                        }
                        else
                        {
                            // Tạo bản ghi đổi quà trước khi trừ điểm thất bại và trả về mã mỗi khác TimeoutException
                            _logger.LogError("Master_Card_redeem_Verify_And_Create_Redeem_Order Error:" + JsonConvert.SerializeObject(createRedeemOrder));
                            await PostLoyaltyAsync<MasterCardOutput<string>>(LoyaltyApiUtilsUrl.MASTER_CARD_UPDATE_REDEEM_STATUS_CARD, new { Cif = input.Cif, CardCode = input.CardCode }, MasterCardConsts.ErrorTypeKey);
                        }
                    }
                    catch (TimeoutException ex)
                    {
                        _logger.LogError("TimeoutException Master_Card_redeem_Verify_And_Create_Redeem_Order Cif: " + input.Cif + " Error:" + ex.Message);
                        throw ex;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("Exception Master_Card_redeem Cif: " + input.Cif + " Error:" + ex.Message);
                        // Time out từ utils, reward hoặc LinkId thì không làm j cả
                        // Nếu có lỗi trả về từ LinkId thì nếu là lỗi tường minh + mã lỗi khác 0 và khác SystemError => Update trạng thái REDEEMING -> Null
                        // Nếu có lỗi trả về từ các nguồn khác và không phải lỗi Timeout => Update trạng thái REDEEMING -> Null
                        if (ex.GetType() == typeof(LoyaltyException))
                        {
                            var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                            if (res.Code != "0" && res.Code != "SystemError")
                            {
                                await PostLoyaltyAsync<MasterCardOutput<string>>(LoyaltyApiUtilsUrl.MASTER_CARD_UPDATE_REDEEM_STATUS_CARD, new { Cif = input.Cif, CardCode = input.CardCode }, MasterCardConsts.ErrorTypeKey);
                            }
                        }
                        else if (ex.GetType() != typeof(TimeoutException) && ex.GetType() != typeof(WebException) && ex.GetType() != typeof(TaskCanceledException))
                        {
                            await PostLoyaltyAsync<MasterCardOutput<string>>(LoyaltyApiUtilsUrl.MASTER_CARD_UPDATE_REDEEM_STATUS_CARD, new { Cif = input.Cif, CardCode = input.CardCode }, MasterCardConsts.ErrorTypeKey);
                        }
                        throw ex;
                    }
                }
                else
                {
                    _logger.LogError("Master_Card_redeem Update Redeeming:" + JsonConvert.SerializeObject(updateRedeeming));
                }
            }
            else
            {
                _logger.LogError("Master_Card_redeem Get Card Infor:" + JsonConvert.SerializeObject(cardInfor));
            }
            return trans;
        }

        public async Task<MasterCardRedeemMultiGiftResponse> RedeemMultiGift(MasterCardRedeemMultiGiftInput input)
        {
            var trans = new List<MasterCardRedeemMultiGiftOutput>();
            if (input.GiftInfors != null && input.GiftInfors.Count > 0)
            {
                var TotalAmount = input.GiftInfors.Where(y => y.TotalAmount.HasValue)?.Sum(x => x.TotalAmount);
                var cardInfor = await GetLoyaltyAsync<MasterCardOutput<GetCardInforForRedeemOutput>>(LoyaltyApiUtilsUrl.MASTER_CARD_GET_CARD_BY_CARD_CODE, new { Cif = input.Cif, CardCode = input.CardCode, TotalAmount = TotalAmount.HasValue ? TotalAmount.Value : 0 }, MasterCardConsts.ErrorTypeKey);
                if (cardInfor != null && cardInfor.code == "00" && cardInfor.data != null)
                {
                    bool IsValidBrand = false;
                    if (input.GiftInfors.Count > 1)
                    {
                        try
                        {
                            var checkBrands = await _baseLoyaltyService.PostLoyaltyAsync<LoyaltyResponse<bool>>(LoyaltyApiUrl.REDEEM_CHECK_GIFT_BRAND, new
                            {
                                GiftCodes = input.GiftInfors.Select(x => x.giftCode).ToList()
                            });
                            if (checkBrands != null && checkBrands.Success && checkBrands.Result)
                            {
                                IsValidBrand = true;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("Master_Card_RedeemMultiGift valid Brand Exception --- Message:" + ex.Message + " ---StackTrace: " + ex.StackTrace);
                            throw ex;
                        }
                    }
                    else
                    {
                        IsValidBrand = true;
                    }
                    if (IsValidBrand)
                    {
                        foreach (var GiftItem in input.GiftInfors)
                        {
                            // Update RedeemStatus = REDEEMING
                            var merchantByGift = cardInfor.data.MerchantId;
                            var updateRedeeming = await PostLoyaltyAsync<MasterCardOutput<string>>(LoyaltyApiUtilsUrl.MASTER_CARD_UPDATE_REDEEM_STATUS_CARD, new { Cif = input.Cif, CardCode = input.CardCode, RedeemStatus = "REDEEMING" }, MasterCardConsts.ErrorTypeKey);
                            if (updateRedeeming != null && updateRedeeming.code == "00")
                            {
                                try
                                {
                                    _logger.LogInformation($"Master_Card_RedeemMultiGift >> Get MerchantId infor from GiftCode");
                                    var merchantIdFromGiftCode = await _giftTransactionsService.GetMerchantIdFromGiftCode(new LoyaltyGiftGetMerchantIdFromGiftCodeInput()
                                    {
                                        GiftCode = GiftItem.giftCode
                                    });
                                    if (merchantIdFromGiftCode == null || !merchantIdFromGiftCode.Success || merchantIdFromGiftCode.Result == null || !merchantIdFromGiftCode.Result.MerchantId.HasValue)
                                    {
                                        _logger.LogError($"Master_Card_RedeemMultiGift >> Cannot find merchantId by GiftCode {GiftItem.giftCode}");
                                    }
                                    else
                                    {
                                        merchantByGift = merchantIdFromGiftCode.Result.MerchantId.Value.ToString();
                                    }
                                }
                                catch (Exception)
                                {
                                    _logger.LogError("Start RedeemMultiGift Reward Redeem card Cif:" + input.Cif + " --- Gift Code: " + GiftItem.giftCode);
                                }
                                // Call API ghi nhận đổi quà bên LynkId loyalty
                                try
                                {
                                    _logger.LogInformation("Start RedeemMultiGift create Redeem Order loyalty Cif:" + input.Cif + " --- Gift Code: " + GiftItem.giftCode);
                                    var createRedeemOrder = await _baseLoyaltyService.PostLoyaltyAsync<LoyaltyResponse<CreateRedeemGiftOrderResponse>>(LoyaltyApiUrl.VERIFY_AND_CREATE_REDEEM_ORDER, new
                                    {
                                        giftGroupCode = cardInfor.data.GiftGroupCode,
                                        cardCode = input.CardCode,
                                        memberCode = cardInfor.data.MemberCode,
                                        giftCode = GiftItem.giftCode,
                                        quantity = GiftItem.Quantity,
                                        totalAmount = GiftItem.TotalAmount,
                                        date = DateTime.UtcNow,
                                        redeemSource = _RedeemSource,
                                        merchantIdRedeem = merchantByGift
                                    });
                                    if (createRedeemOrder != null && createRedeemOrder.Success && createRedeemOrder.Result != null && createRedeemOrder.Result.isSuccess)
                                    {
                                        // Trừ điểm bên reward
                                        var listCard = new List<string>();
                                        listCard.Add(input.CardCode);
                                        try
                                        {
                                            _logger.LogInformation("Start RedeemMultiGift Reward Redeem card Cif:" + input.Cif + " --- Gift Code: " + GiftItem.giftCode);
                                            var redeemCardCoin = await _rewardBaseService.PostRewardAsync<RewardRedeemCardResponse>(RewardApiUrl.MASTER_CARD_REDEEM_CARD, new
                                            {
                                                MemberCode = cardInfor.data.MemberCode,
                                                PhoneNumber = "",
                                                MoneyCardIds = listCard,
                                                TokenAmount = GiftItem.TotalAmount,
                                                MerchantId = MerchantId,
                                                Reason = "REDEEM"
                                            }, "MasterCard");
                                            if (redeemCardCoin != null && redeemCardCoin.result == 200 && redeemCardCoin.item != null && redeemCardCoin.item.Count > 0)
                                            {
                                                try
                                                {
                                                    // Active quà cho khách hàng theo transactionCode
                                                    _logger.LogInformation("Start RedeemMultiGift Redeem Gift loyalty Cif:" + input.Cif + " --- Gift Code: " + GiftItem.giftCode);
                                                    var activeTransaction = await _baseLoyaltyService.PostLoyaltyAsync<LoyaltyResponse<RedeemMultiGiftResponse>>(LoyaltyApiUrl.REDEEM_MULTI_GIFT_BY_GROUP_CODE, new
                                                    {
                                                        giftGroupCode = cardInfor.data.GiftGroupCode,
                                                        cardCode = input.CardCode,
                                                        cardCodeTx = redeemCardCoin.item[0].OrderCode,
                                                        memberCode = cardInfor.data.MemberCode,
                                                        transactionCode = createRedeemOrder.Result.transactionCode,
                                                        giftCode = GiftItem.giftCode,
                                                        quantity = GiftItem.Quantity,
                                                        totalAmount = GiftItem.TotalAmount,
                                                        date = DateTime.UtcNow,
                                                        redeemSource = _RedeemSource,
                                                        merchantIdRedeem = merchantByGift
                                                    });
                                                    if (activeTransaction != null && activeTransaction.Success && activeTransaction.Result != null && activeTransaction.Result.isSuccess)
                                                    {
                                                        _logger.LogInformation("Success RedeemMultiGift Redeem Gift loyalty Cif:" + input.Cif + " --- Gift Code: " + GiftItem.giftCode);
                                                        foreach (var egiftItem in activeTransaction.Result.items)
                                                        {
                                                            var tranItem = new MasterCardRedeemMultiGiftOutput();
                                                            tranItem.Cif = input.Cif;
                                                            tranItem.GiftCode = GiftItem.giftCode;
                                                            tranItem.GiftTransactionCode = egiftItem.transactionCode;
                                                            tranItem.EgiftCode = egiftItem.EgiftCode;
                                                            tranItem.ExpiredDate = egiftItem.ExpiredDate.HasValue ? DateTime.Parse(egiftItem.ExpiredDate.Value.AddHours(7).ToString("yyyy-MM-ddTHH:mm:ss.999")) : egiftItem.ExpiredDate;
                                                            trans.Add(tranItem);
                                                            try
                                                            {
                                                                _logger.LogInformation("Start Update coin after Redeem Gift loyalty Cif:" + input.Cif + " --- Gift Code: " + GiftItem.giftCode);
                                                                await PostLoyaltyAsync<MasterCardOutput<string>>(LoyaltyApiUtilsUrl.MASTER_CARD_UPDATE_REDEEM_GIFT, new
                                                                {
                                                                    Cif = input.Cif,
                                                                    PhoneNumber = input.PhoneNumber,
                                                                    Quantity = 1,
                                                                    GiftCode = GiftItem.giftCode,
                                                                    CardCode = input.CardCode,
                                                                    TotalAmount = GiftItem.TotalAmount / GiftItem.Quantity,
                                                                    GiftTransactionCode = tranItem.GiftTransactionCode,
                                                                    RewardOrderCode = redeemCardCoin.item[0].OrderCode,
                                                                    EgiftCode = tranItem.EgiftCode,
                                                                    GiftGroupCode = cardInfor.data.GiftGroupCode,
                                                                    MemberCode = cardInfor.data.MemberCode,
                                                                    ExpiredDate = tranItem.ExpiredDate,
                                                                    Description = input.Description,
                                                                }, MasterCardConsts.ErrorTypeKey);
                                                            }
                                                            catch (LoyaltyException ex)
                                                            {
                                                                _logger.LogError("LoyaltyException Update status after RedeemGift false:" + ex.Message + " --- StackTrace: " + ex.StackTrace);
                                                            }
                                                        }
                                                    }
                                                    else
                                                    {
                                                        _logger.LogError("Error RedeemMultiGift Redeem Gift loyalty Cif:" + input.Cif + " --- Gift Code: " + GiftItem.giftCode + " --- Response: " + JsonConvert.SerializeObject(activeTransaction));
                                                        if (!activeTransaction.Result.Timeout)
                                                        {
                                                            trans.Add(new MasterCardRedeemMultiGiftOutput()
                                                            {
                                                                Cif = input.Cif,
                                                                GiftCode = GiftItem.giftCode,
                                                                Error = new MasterCardErrorResponse()
                                                                {
                                                                    ErrorCode = 500,
                                                                    ErrorMessage = activeTransaction.Result.Exception
                                                                }
                                                            });
                                                            await _rewardBaseService.PostRewardAsync<RewardReturnCardResponse>(RewardApiUrl.MASTER_CARD_RETURN_CARD,
                                                            new
                                                            {
                                                                OrderCode = redeemCardCoin.item[0].OrderCode,
                                                                MemberCode = cardInfor.data.MemberCode,
                                                                PhoneNumber = "",
                                                                Reason = "RETURN",
                                                            }, "MasterCard");
                                                            await PostLoyaltyAsync<MasterCardOutput<string>>(LoyaltyApiUtilsUrl.MASTER_CARD_UPDATE_REDEEM_STATUS_CARD, new { Cif = input.Cif, CardCode = input.CardCode }, MasterCardConsts.ErrorTypeKey);
                                                        }
                                                        else
                                                        {
                                                            throw new TimeoutException("Request merchant time out!");
                                                        }
                                                    }
                                                }
                                                catch (LoyaltyException ex)
                                                {
                                                    _logger.LogError("LoyaltyException RedeemMultiGift Redeem Gift loyalty Cif:" + input.Cif + " --- Gift Code: " + GiftItem.giftCode + " --- Message: " + ex.Message + " --- StackTrace: " + ex.StackTrace + " --- ErrorData: " + JsonConvert.SerializeObject(ex.Data));
                                                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                                                    if (res.Code != "0" && res.Code != "SystemError")
                                                    {
                                                        trans.Add(new MasterCardRedeemMultiGiftOutput()
                                                        {
                                                            Cif = input.Cif,
                                                            GiftCode = GiftItem.giftCode,
                                                            Error = new MasterCardErrorResponse()
                                                            {
                                                                ErrorCode = !string.IsNullOrEmpty(res.Code) ? Int32.Parse(res.Code) : 500,
                                                                ErrorMessage = res.Message
                                                            }
                                                        });
                                                        await _rewardBaseService.PostRewardAsync<RewardReturnCardResponse>(RewardApiUrl.MASTER_CARD_RETURN_CARD, new
                                                        {
                                                            OrderCode = redeemCardCoin.item[0].OrderCode,
                                                            MemberCode = cardInfor.data.MemberCode,
                                                            PhoneNumber = "",
                                                            Reason = "RETURN",
                                                        }, "MasterCard");
                                                        await PostLoyaltyAsync<MasterCardOutput<string>>(LoyaltyApiUtilsUrl.MASTER_CARD_UPDATE_REDEEM_STATUS_CARD, new { Cif = input.Cif, CardCode = input.CardCode }, MasterCardConsts.ErrorTypeKey);
                                                    }
                                                    else
                                                    {
                                                        throw ex;
                                                    }
                                                }
                                                catch (WebException ex)
                                                {
                                                    throw ex;
                                                }
                                                catch (TaskCanceledException ex)
                                                {
                                                    throw ex;
                                                }
                                            }
                                            else
                                            {
                                                _logger.LogError("Error RedeemMultiGift Redeem Gift loyalty Cif:" + input.Cif + " --- Gift Code: " + GiftItem.giftCode + " --- Response: " + JsonConvert.SerializeObject(redeemCardCoin));
                                                trans.Add(new MasterCardRedeemMultiGiftOutput()
                                                {
                                                    Cif = input.Cif,
                                                    GiftCode = GiftItem.giftCode,
                                                    Error = new MasterCardErrorResponse()
                                                    {
                                                        ErrorCode = redeemCardCoin.result.HasValue ? redeemCardCoin.result.Value : 500,
                                                        ErrorMessage = redeemCardCoin.message
                                                    }
                                                });
                                                await PostLoyaltyAsync<MasterCardOutput<string>>(LoyaltyApiUtilsUrl.MASTER_CARD_UPDATE_REDEEM_STATUS_CARD, new { Cif = input.Cif, CardCode = input.CardCode }, MasterCardConsts.ErrorTypeKey);
                                            }
                                        }
                                        catch (RewardException ex)
                                        {
                                            _logger.LogError("RewardException RedeemMultiGift Reward Redeem card Cif:" + input.Cif + " --- Gift Code: " + GiftItem.giftCode + " --- Message: " + ex.Message + " --- StackTrace: " + ex.StackTrace + " --- ErrorData: " + JsonConvert.SerializeObject(ex.Data));
                                            var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                                            if (!string.IsNullOrEmpty(res.Code))
                                            {
                                                trans.Add(new MasterCardRedeemMultiGiftOutput()
                                                {
                                                    Cif = input.Cif,
                                                    GiftCode = GiftItem.giftCode,
                                                    Error = new MasterCardErrorResponse()
                                                    {
                                                        ErrorCode = res.Code,
                                                        ErrorMessage = res.MessageDetail != null ? res.MessageDetail.ToString() : res.Message
                                                    }
                                                });
                                                await PostLoyaltyAsync<MasterCardOutput<string>>(LoyaltyApiUtilsUrl.MASTER_CARD_UPDATE_REDEEM_STATUS_CARD, new { Cif = input.Cif, CardCode = input.CardCode }, MasterCardConsts.ErrorTypeKey);
                                            }
                                            else
                                            {
                                                throw ex;
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            throw ex;
                                        }
                                    }
                                    else
                                    {
                                        // Tạo bản ghi đổi quà trước khi trừ điểm thất bại và trả về mã mỗi khác TimeoutException
                                        _logger.LogError("Error RedeemMultiGift create Redeem Order loyalty Cif:" + input.Cif + " --- Gift Code: " + GiftItem.giftCode + " --- Response: " + JsonConvert.SerializeObject(createRedeemOrder));
                                        trans.Add(new MasterCardRedeemMultiGiftOutput()
                                        {
                                            Cif = input.Cif,
                                            GiftCode = GiftItem.giftCode,
                                            Error = new MasterCardErrorResponse()
                                            {
                                                ErrorCode = 500,
                                                ErrorMessage = createRedeemOrder.Error
                                            }
                                        });
                                        await PostLoyaltyAsync<MasterCardOutput<string>>(LoyaltyApiUtilsUrl.MASTER_CARD_UPDATE_REDEEM_STATUS_CARD, new { Cif = input.Cif, CardCode = input.CardCode }, MasterCardConsts.ErrorTypeKey);
                                    }
                                }
                                catch (LoyaltyException ex)
                                {
                                    _logger.LogError("LoyaltyException RedeemMultiGift create Redeem Order loyalty Cif:" + input.Cif + " --- Gift Code: " + GiftItem.giftCode + " --- Message: " + ex.Message + " --- StackTrace: " + ex.StackTrace + " --- ErrorData: " + JsonConvert.SerializeObject(ex.Data));
                                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                                    if (res.Code != "0" && res.Code != "SystemError")
                                    {
                                        trans.Add(new MasterCardRedeemMultiGiftOutput()
                                        {
                                            Cif = input.Cif,
                                            GiftCode = GiftItem.giftCode,
                                            Error = new MasterCardErrorResponse()
                                            {
                                                ErrorCode = !string.IsNullOrEmpty(res.Code) ? Int32.Parse(res.Code) : 500,
                                                ErrorMessage = res.Message
                                            }
                                        });
                                        await PostLoyaltyAsync<MasterCardOutput<string>>(LoyaltyApiUtilsUrl.MASTER_CARD_UPDATE_REDEEM_STATUS_CARD, new { Cif = input.Cif, CardCode = input.CardCode }, MasterCardConsts.ErrorTypeKey);
                                    }
                                    else
                                    {
                                        throw ex;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError("Exception RedeemMultiGift create Redeem Order loyalty Cif:" + input.Cif + " --- Gift Code: " + GiftItem.giftCode + " --- Message: " + ex.Message + " --- StackTrace: " + ex.StackTrace + " --- ErrorData: " + JsonConvert.SerializeObject(ex.Data));
                                    throw ex;
                                }
                            }
                            else
                            {
                                trans.Add(new MasterCardRedeemMultiGiftOutput()
                                {
                                    Cif = input.Cif,
                                    GiftCode = GiftItem.giftCode,
                                    Error = new MasterCardErrorResponse()
                                    {
                                        ErrorCode = !string.IsNullOrEmpty(updateRedeeming.code) ? Int32.Parse(updateRedeeming.code) : 500,
                                        ErrorMessage = updateRedeeming.message
                                    }
                                });
                                _logger.LogError("Master_Card_redeem Update Redeeming Error:" + JsonConvert.SerializeObject(updateRedeeming));
                            }
                        }
                    }
                }
                else
                {
                    _logger.LogInformation("Master_Card_RedeemMultiGift Get Card Infor:" + JsonConvert.SerializeObject(cardInfor));
                }
            }
            return new MasterCardRedeemMultiGiftResponse() { Results = trans };
        }

        public async Task<MasterCardOutput<string>> RegisterCampaign(RegisterCampaignInput input)
        {
            var output = await PostLoyaltyAsync<MasterCardOutput<string>>(LoyaltyApiUtilsUrl.MASTER_CARD_REGISTER_CAMPAIGN, input, MasterCardConsts.ErrorTypeKey);
            try
            {
                var customer_cache_vi = _cache.GetString(CommonConstants.MASTER_CARD_CUSTOMER_CHECK_KEY + "_" + input.Cif + "_vi");
                if (customer_cache_vi != null)
                {
                    _logger.LogInformation("Master_RegisterCampaign Remove Cache key: " + CommonConstants.MASTER_CARD_CUSTOMER_CHECK_KEY + "_" + input.Cif + "_vi" + " ---Data: " + customer_cache_vi);
                    _cache.Remove(CommonConstants.MASTER_CARD_CUSTOMER_CHECK_KEY + "_" + input.Cif + "_vi");
                }
                var customer_cache_en = _cache.GetString(CommonConstants.MASTER_CARD_CUSTOMER_CHECK_KEY + "_" + input.Cif + "_en");
                if (customer_cache_en != null)
                {
                    _logger.LogInformation("Master_RegisterCampaign Remove Cache key: " + CommonConstants.MASTER_CARD_CUSTOMER_CHECK_KEY + "_" + input.Cif + "_en" + " ---Data: " + customer_cache_en);
                    _cache.Remove(CommonConstants.MASTER_CARD_CUSTOMER_CHECK_KEY + "_" + input.Cif + "_en");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Master_RegisterCampaign Remove Cache Exception: ---Input:" + JsonConvert.SerializeObject(input) + " ---Message: " + ex.Message + " ---StackTrace: " + ex.StackTrace);
            }
            return output;
        }

        private async Task<CheckAndGetMemberInfoOutput> CheckAndGetMemberInfo(GetLinkIdMemberByCifCodeInput input)
        {
            LoyaltyResponse<GetLinkIdMemberByCifCodeOutput> vpbMember;
            try
            {
                vpbMember = await _loyaltyMemberService.GetLinkIdMemberByCifCode(input);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                CommonHelper.GetErrorValidation(res.Code, res.Message);
                throw ex;
            }

            RewardGetMemberInfoResponse linkIdMember;
            try
            {
                linkIdMember = await _rewardMemberService.GetMemberInfo(vpbMember.Result.LinkID_MemberID);
                if (linkIdMember == null)
                {
                    CommonHelper.GetErrorValidation("750", "Member with this CIF code not exist");
                }
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    CommonHelper.GetErrorValidation(GetCodeFromCode(res.Code), GetMessageFromCode(res.Code));
                }
                throw ex;
            }

            return new CheckAndGetMemberInfoOutput
            {
                LinkID_MemberID = linkIdMember.Id,
                NationalId = linkIdMember.NationalId,
                LinkID_PhoneNumber = linkIdMember.PhoneNumber,
            };
        }

        private string GetCodeFromCode(string code)
        {
            switch (code)
            {
                case "MemberNotExitsOrNotActive":
                    return "MemberNotExist";
                default:
                    return code;
            }
        }

        private string GetMessageFromCode(string code)
        {
            _logger.LogInformation($"Message from code {code}");
            var codeCheck = GetCodeFromCode(code);
            switch (codeCheck)
            {
                case "MemberNotEnoughToken":
                    return "Member not enough token";
                case "MemberNotExist":
                    return "LinkID member not exist";
                case "OriginalOrderNotExist":
                    return "Original order code not exist";
                case "OriginalOrderHasBeenReverted":
                    return "Original order code has been reverted";
                case "DuplicateOrderCode":
                    return "Duplicate order code";
                case "DuplicateRevertCode":
                    return "Duplicate revert code";
                case "1023":
                    return "Gift not exist";
                case "1025":
                    return "Out of gift";
                default:
                    return "System error";
            }
        }
    }
}
