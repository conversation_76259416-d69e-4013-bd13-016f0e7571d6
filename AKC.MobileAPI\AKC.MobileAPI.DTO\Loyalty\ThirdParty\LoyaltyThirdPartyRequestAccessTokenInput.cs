﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.ThirdParty
{
    public class LoyaltyThirdPartyRequestAccessTokenInput
    {
        //[Required]
        //public string RefreshToken { get; set; }
        //[Required]
        //public string IdNumber { get; set; }
        [Required]
        [Range(1, Double.MaxValue)]
        public int MerchantId { get; set; }

       // [Required]
        public string MemberCode { get; set; }
        public string NationalId { get; set; }
        
    }
}
