﻿using AKC.MobileAPI.DTO.Loyalty.Article;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyArticleService
    {
        Task<LoyaltyArticleGetAllOutput> GetAll(LoyaltyArticleGetAllInput input);

        Task<GetArticleForEditOutput> GetArticleByIdAndRelatedNews(GetArticleByIdAndRelatedNewsInput input);

        Task<GetArticleForEditOutput> GetArticleByIdByMemberCode(GetArticleByIdByMemberCodeInput input);

        Task<GetAllArticleAndRelatedNewsOutput> GetAllArticleAndRelatedNews(GetAllArticleAndRelatedNewsInput input);

        Task<GetAllArticleByMemberCodeOutPut> GetAllArticleByMemberCode(GetAllArticleByMemberCodeInput input);

    }
}
