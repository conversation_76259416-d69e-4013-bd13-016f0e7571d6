﻿using AKC.MobileAPI.Service.Exceptions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace AKC.MobileAPI.Service.Reward
{
    public interface IRewardBaseService
    {
        Task<T> GetRewardAsync<T>(string apiURL, object query = null, string rewardType = null);
        Task<T> PostRewardAsync<T>(string apiURL, object body = null, string rewardType = null);
        Task<T> PutRewardAsync<T>(string apiURL, object body = null, string rewardType = null);
    }
}
