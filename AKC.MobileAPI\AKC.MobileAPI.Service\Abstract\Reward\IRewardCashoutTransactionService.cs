﻿using AKC.MobileAPI.DTO.Reward.CashoutTransaction;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Reward
{
    public interface IRewardCashoutTransactionService
    {
        Task<RewardCreateCashoutTransactionOutput> CreateCashoutTransaction(RewardCreateCashoutTransactionInput input);
        Task<RewardRevertCashoutTransactionOutput> RevertCashoutTransaction(RewardRevertCashoutTransactionInput input);
    }
}
