﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberGetInfoOutput
    {
        public int Id { get; set; }
        public string UserAddress { get; set; }
        public string Status { get; set; }
        public string Type { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string PhoneNumber { get; set; }
        public string PartnerPhoneNumber { get; set; }
        public string NationalId { get; set; }
        public decimal Balance { get; set; }
    }

    public class MemberInfo
    {
        public int Id { get; set; }
        public string UserAddress { get; set; }
        public string Status { get; set; }
        public string Type { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string PhoneNumber { get; set; }
        //public string Dob { get; set; }
        //public string NationalId { get; set; }
        //public string IdCard { get; set; }
        //public string PartnerPhoneNumber { get; set; }
        //public string PointUsingOrdinary { get; set; }
        //public string Gender { get; set; }
        //public string Email { get; set; }
        //public string HashAddress { get; set; }
        //public string RegionCode { get; set; }
        //public string FullRegionCode { get; set; }
        //public string MemberTypeCode { get; set; }
        //public string FullMemberTypeCode { get; set; }
        //public string ChannelType { get; set; }
        //public string FullChannelTypeCode { get; set; }
        //public string RankTypeCode { get; set; }
        //public string StandardMemberCode { get; set; }
        //public decimal TempPointBalance { get; set; }
        //public decimal TokenBalance { get; set; }
        //public string ReferralCode { get; set; }
        //public string Avatar { get; set; }
        //public List<GrantTypeResult> GrantTypeBalance { get; set; }
        //public bool IsIdCardVerified { get; set; }
        //public bool HasPinCode { get; set; }
    }
}
