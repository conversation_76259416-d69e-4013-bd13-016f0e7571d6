﻿using AKC.MobileAPI.DTO.Loyalty.ChallengeMaf;
using AKC.MobileAPI.DTO.Loyalty.MasterCard;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface IChallengeMafService
    {
        Task<LynkiDResponseList<LynkiDGiftOutput>> GetGiftByGroupChannel(GetGiftByGroupChannelInput input);
        Task<LynkiDResponse<bool>> ValidGiftByBrand(ValidGiftByBrandInput input);
        Task<LynkiDResponse<CreateRedeemMafGiftOrderResponse>> VerifyAndCreateRedeemOrder(VerifyAndCreateRedeemOrderInput input);
        Task<LynkiDResponse<RedeemMultiMafGiftResponse>> CreateRedeemMultiTxWithGroupCode(CreateRedeemMultiTxWithGroupCodeInput input);
        Task<LynkiDResponseList<GiftRedeemedLoyaltyResultOuput>> GetGiftRedeemTransWithGiftGroup(GetGiftRedeemTransWithGiftGroupInput input);
        Task<GetCardMafResponse> CardTransactionGetAll(CardTransactionGetAllInput input);
        Task<RewardRedeemCardMafResponse> CardTransactionRedeem(CardTransactionRedeemInput input);
        Task<RewardReturnCardMafResponse> CardTransactionReturn(CardTransactionReturnInput input);
        Task<CreateCardCoinOutput> CardTransactionCreate(CardTransactionCreateInput input);
        Task<RewardResponse> CardTransactionRevert(CardTransactionRevertInput input);
        Task<List<MasterCardRedeemMultiGiftOutput>> RedeemGiftV2(ChallengrRedeemGiftInput input);
    }
}
