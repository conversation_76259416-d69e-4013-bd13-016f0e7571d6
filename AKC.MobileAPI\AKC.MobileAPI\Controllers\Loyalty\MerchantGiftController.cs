﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Loyalty.LocationManagement;
using AKC.MobileAPI.DTO.MerchantGift;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/Gift")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class MerchantGiftController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IMerchantGiftService _merchantGiftService;
        private readonly IExceptionReponseService _exceptionReponseService;
        public MerchantGiftController(
            ILogger<MerchantGiftController> logger,
            IMerchantGiftService merchantGiftService,
            IExceptionReponseService exceptionReponseService)
        {
            _logger = logger;
            _merchantGiftService = merchantGiftService;
            _exceptionReponseService = exceptionReponseService;
        }

        [HttpGet]
        [Route("GetListAddressShip")]
        public async Task<ActionResult<GetAllLocationManagementDto>> GetAllLocation([FromQuery] MerchantGiftLocationShipInput input)
        {
            try
            {
                return await _merchantGiftService.GetAllLocationShip(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Merchant gift get all location ship- " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpGet]
        [Route("GetAllLocation")]
        public async Task<ActionResult<LoyaltyResponseList<MerchantGiftLocationOutput>>> GetAllLocation([FromQuery] MerchantGiftLocationInput input)
        {
            try
            {
                return await _merchantGiftService.GetAllLocation(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Merchant gift get all location - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpGet]
        [Route("GetAllCategory")]
        public async Task<ActionResult<LoyaltyResponseList<MerchantGiftGetAllCategoryOutput>>> GetAllCategory([FromQuery] MerchantGiftGetAllCategoryInput input)
        {
            try
            {
                return await _merchantGiftService.GetAllCategory(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Merchant gift get all category - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }
        [HttpGet]
        [Route("GetAllCategoryFor247")]
        public async Task<ActionResult<LoyaltyResponseList<MerchantGiftGetAllCategoryOutput>>> GetAllCategoryFor247([FromQuery] MerchantGiftGetAllCategoryInput input)
        {
            try
            {
                return await _merchantGiftService.GetAllCategoryFor247(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Merchant gift get all category GetAllCategoryFor247 - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpGet]
        [Route("GiftList")]
        public async Task<ActionResult<LoyaltyResponseList<MerchantGiftGetAllGiftOutput>>> GiftList([FromQuery] MerchantGiftGetAllGiftInput input)
        {
            try
            {
                return await _merchantGiftService.GiftList(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Merchant gift get gift list - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpGet]
        [Route("GiftDetail")]
        public async Task<ActionResult<LoyaltyResponse<MerchantGiftGetDetailOutput>>> GiftDetail([FromQuery] MerchantGiftGetDetailInput input)
        {
            try
            {
                return await _merchantGiftService.GiftDetail(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Merchant gift get gift detail - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("MarkUseEGift")]
        public async Task<ActionResult<RedeemVoucherOutput>> MarkUseEGift([FromBody] MarkUseEGiftInput input)
        {
            try
            {
                _logger.LogInformation($"MarkUseEGift_Request:{JsonConvert.SerializeObject(input)}");
                return await _merchantGiftService.MarkUseEGift(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "MarkUseEGift - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }
    }
}

