﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.MerchantGift
{
    public class MerchantGiftCreateRedeemOutputDto
    {
        public MerchantGiftCreateRedeemTransactionDto Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }
    public class MerchantGiftCreateRedeemTransactionDto
    {
        public List<MerchantGiftDataRedeemTransaction> Items { get; set; }
        public int TotalCount { get; set; }
        public string Exception { get; set; }
        public string Messages { get; set; }
        public bool SuccessedRedeem { get; set; }
        public bool IsNotEnoughBalance { get; set; }

        public bool Timeout { get; set; }
    }

    public class MerchantGiftDataRedeemTransactionDto
    {
        public string Code { get; set; }
        public decimal TotalCoin { get; set; }
        public string Status { get; set; }
        public DateTime Date { get; set; }
        public string Description { get; set; }
        public MerchantGiftEGiftData EGift { get; set; }
    }

    public class MerchantGiftEGiftDataDto
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string usedStatus { get; set; }
        public DateTime? ExpiredDate { get; set; }
        public string QRCode { get; set; }
    }

    public class MerchantGiftVerifyCreateRedeemOutput
    {
        public string MemberCode { get; set; }
        public string GiftCode { get; set; }
        public string GiftName { get; set; }
    }

    public class VerifyAndCreateRedeemOrderOutput
    {
        public VerifyAndCreateRedeemOrderResult Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class VerifyAndCreateRedeemOrderResult
    {
        public bool IsSuccess { get; set; }
        public string TransactionCode { get; set; }
        public string Exception { get; set; }
    }

    public class UpdateErrorWhenCreateRedeemPayment
    {
        public string TransactionCode { get; set; }
        public string ErrorCode { get; set; }
        public string ErrorMessage { get; set; }
        public string Status { get; set; }
    }

    public class UpdateErrorWhenCreateRedeemPaymentOutput
    {
        public bool IsSuccess { get; set; }
        public string TransactionCode { get; set; }
        public string Exception { get; set; }
    }
}
