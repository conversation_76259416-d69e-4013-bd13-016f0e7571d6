﻿using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.CronServices;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Loyalty;
using Cronos;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace AKC.MobileAPI.Service.LoyaltyVpbank
{
    public class BaseLoyaltyUtilService
    {
        protected readonly HttpClient _client = new HttpClient();
        protected readonly IConfiguration _configuration;
        protected readonly string baseURL;
        protected readonly int tenantId;
        protected readonly string defaultUserName;
        protected readonly string defaultPassowrd;
        private readonly IDistributedCache _cache;

        public BaseLoyaltyUtilService(IConfiguration configuration, IDistributedCache cache)
        {
            _client.Timeout = TimeSpan.FromSeconds(300);
            _configuration = configuration;
            baseURL = _configuration.GetSection("VPBankLoyaltyAPIUtils:BaseURL").Value;
            tenantId = Convert.ToInt32(_configuration.GetSection("VPBankLoyaltyAPIUtils:TenantId").Value);
            defaultUserName = _configuration.GetSection("VPBankLoyaltyAPIUtils:Username").Value;
            defaultPassowrd = _configuration.GetSection("VPBankLoyaltyAPIUtils:Password").Value;
            _cache = cache;
        }

        /// <summary>
        /// Perform a GET request to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> GetLoyaltyAsync<T>(string apiURL, object query = null, string ErrorType = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var requestURL = $"{baseURL}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Get
            };

            //req.Headers.Add("Abp.TenantId", tenantId.ToString());
            var token = GetAccessToken(true);
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            req.RequestUri = new Uri(requestURL);
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    ex.Data.Add("ErrorType", ErrorType);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();


                // Convert response to result object which is a instance of 'T'.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }

        /// <summary>
        /// Perform a DELETE obj to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> DeleteLoyaltyAsync<T>(string apiURL, object query = null, string ErrorType = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var requestURL = $"{baseURL}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Delete
            };

            var token = GetAccessToken();
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            req.RequestUri = new Uri(requestURL);
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    ex.Data.Add("ErrorType", ErrorType);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();


                // Convert response to result object which is a instance of 'T'.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }

        /// <summary>
        /// Convert a object to query string format.
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public string GetQueryString(object obj)
        {
            var properties = from p in obj.GetType().GetProperties()
                             where p.GetValue(obj, null) != null
                             select p.Name + "=" + HttpUtility.UrlEncode(p.GetValue(obj, null).ToString());

            return string.Join("&", properties.ToArray());
        }

        public async Task<T> PostLoyaltyAsync<T>(string apiURL, object body = null, string ErrorType = null, HttpContext request = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }
            var Client_Request_Address = request != null && request.Request.Headers.ContainsKey("Client-Request-Address") ?
                                            request?.Request.Headers["Client-Request-Address"].ToString() : request?.Connection.RemoteIpAddress.ToString();
            req.Headers.Add("Client-Request-Address", Client_Request_Address);
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", GetAccessToken());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    ex.Data.Add("ErrorType", ErrorType);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();

                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);

                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<T> PutLoyaltyAsync<T>(string apiURL, object body = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Put
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }

            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", GetAccessToken());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();

                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);

                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }

        /// <summary>
        /// Get a new accessToken form Loyalty.
        /// </summary>
        /// <returns></returns>
        private string GetAccessToken(bool mustResetCache = false)
        {
            var token = _cache.GetString(CommonConstants.ACCESSS_TOKEN_VPBANK_LOYALTY_CACHE_KEY);
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var expression = CronExpression.Parse(configuration.GetSection("VPBankLoyaltyAPIUtils:CronExpressionRefreshToken").Value);
            var timeZoneInfo = TimeZoneInfo.Local;

            // Request body
            if (string.IsNullOrEmpty(token) || mustResetCache)
            {
                return RenewAccessTokenCacheValue(_cache, CronHelper.GetDelayToNextRefreshToken(expression, timeZoneInfo), mustResetCache);
            }

            return token;
        }
        public static string RenewAccessTokenCacheValue(IDistributedCache cache, TimeSpan delay, bool isForceRenew = false)
        {
            return isForceRenew || string.IsNullOrEmpty(cache.GetString(CommonConstants.ALLOW_RENEW_LOYALTY_TOKEN))
                ? RenewCacheAndGetToken(cache,
                    CommonConstants.ALLOW_RENEW_LOYALTY_TOKEN,
                    CommonConstants.ACCESSS_TOKEN_VPBANK_LOYALTY_CACHE_KEY,
                    GetNewAccessToken(), delay)
                : cache.GetString(CommonConstants.ACCESSS_TOKEN_VPBANK_LOYALTY_CACHE_KEY);
        }

        private static string RenewCacheAndGetToken(IDistributedCache cache,
            string allowRenewByJobCacheKey,
            string accessTokenCacheKey,
            VPBankLoyaltyUtilLoginResponse tokenInfo,
            TimeSpan delay)
        {
            // Cache flag allow refresh token (for cluster case).
            var cacheAllowRenewTokenFlagCacheOptions = new DistributedCacheEntryOptions()
                .SetAbsoluteExpiration(TimeSpan.FromSeconds(delay.TotalSeconds - 1));

            // Not allow refresh token until allowRenewByJobFlag expired
            cache.SetString(allowRenewByJobCacheKey, allowRenewByJobCacheKey, cacheAllowRenewTokenFlagCacheOptions);

            // Renew accesstoken and set new value into cache.
            var cacheAccessTokenOptions = new DistributedCacheEntryOptions()
                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(delay.TotalSeconds * 2));

            cache.SetString(accessTokenCacheKey, tokenInfo?.token, cacheAccessTokenOptions);

            // Set accesstoken after renew.
            return tokenInfo?.token;
        }
        private static VPBankLoyaltyUtilLoginResponse GetNewAccessToken()
        {
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var baseURL = configuration.GetSection("VPBankLoyaltyAPIUtils:BaseURL").Value;
            var defaultUserName = configuration.GetSection("VPBankLoyaltyAPIUtils:Username").Value;
            var defaultPassoword = configuration.GetSection("VPBankLoyaltyAPIUtils:Password").Value;

            return LoginLoyalty(baseURL, defaultUserName, defaultPassoword);
        }
        private static VPBankLoyaltyUtilLoginResponse LoginLoyalty(string baseURL, string userName, string password)
        {
            VPBankLoyaltyUtilLoginRequest loyaltyLoginDTO = new VPBankLoyaltyUtilLoginRequest()
            {
                username = userName,
                password = password
            };

            var body = JsonConvert.SerializeObject(loyaltyLoginDTO, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            });

            // Setup request.
            var req = new HttpRequestMessage { Method = HttpMethod.Post };
            req.RequestUri = new Uri($"{baseURL}/User/authenticate");
            req.Content = new StringContent(body, Encoding.UTF8, "application/json");

            // Get Response.
            HttpClient _client = new HttpClient();
            var response = _client.SendAsync(req).Result;
            var rawData = response.Content.ReadAsStringAsync().Result;

            // Make sure success status code.
            response.EnsureSuccessStatusCode();

            // Get respone result.
            return JsonConvert.DeserializeObject<VPBankLoyaltyUtilLoginResponse>(rawData);
        }

        //Clone a HttpRequest
        private HttpRequestMessage CloneHttpRequest(HttpRequestMessage req)
        {
            HttpRequestMessage clone = new HttpRequestMessage(req.Method, req.RequestUri);

            clone.Content = req.Content;
            clone.Version = req.Version;

            foreach (KeyValuePair<string, object> prop in req.Properties)
            {
                clone.Properties.Add(prop);
            }

            foreach (KeyValuePair<string, IEnumerable<string>> header in req.Headers)
            {
                clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            return clone;
        }
    }
    
    public class VPBankLoyaltyUtilLoginResponse
    {
        public int id { get; set; }
        public string username { get; set; }
        public string token { get; set; }
    }
    public class VPBankLoyaltyUtilLoginRequest
    {
        public string username { get; set; }
        public string password { get; set; }
    }
}
