﻿using System;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Sme;
using AKC.MobileAPI.Service.Abstract.Sme;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Sme
{
    [Route("api/Sme")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class SmeController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ISmeService _smeService;
        private readonly IExceptionReponseService _exceptionResponseService;
        public SmeController(
            ILogger<SmeController> logger,
            ISmeService sv,
            IExceptionReponseService esx)
        {
            _logger = logger;
            _smeService = sv;
            _exceptionResponseService = esx;
        }

        #region VIEW POINT / GET CATES / GET GIFTS
        
        [HttpGet]
        [Route("ViewPoint")]
        public async Task<ActionResult<SmeViewPointOutput>> ViewPoint([FromQuery] SmeViewPointInput request)
        {
            try
            {
                var result = await _smeService.ViewPoint(request);
                var httpCode = result.code == SmeErrorCodes.Success ? 200 : 400;
                
                return StatusCode(httpCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, " >> SmeViewPoint >>  " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, GetBaseSmeErrorResponse());
            }
        }
        
        [HttpGet]
        [Route("GetAllCategory")]
        public async Task<ActionResult<SmeCateListOutput>> GetAllCategory([FromQuery] SmeCateListInput input)
        {
            try
            {
                return await _smeService.GetAllCategory(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, " >> Sme GetAllCategory >>  " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, GetBaseSmeErrorResponse());
            }
        }
        #endregion
        
        /**
         * Trả về thông tin cơ bản của SME. Dữ liệu kết nối của Sme đó với LynkiD SME
         * Và trả về count các member của SME này
         */
        [HttpGet]
        [Route("GetSmeInfo")]
        public async Task<ActionResult<GetSmeInfoOutput>> GetSmeInfo([FromQuery] GetSmeInfoInput input)
        {
            try
            {
                _logger.LogInformation(" >> GetSmeInfo >> input " + JsonConvert.SerializeObject(input));
                var res = await _smeService.GetSmeInfo(input);
                if (res.ErrorCode == "00")
                {
                    res.code = "00";
                    res.message = "Success";
                    return StatusCode(200, res);
                }
                return StatusCode(400, SmeErrorCodes.UtilErrorCodeConverter(res.ErrorCode));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, " >> GetSmeInfo >>  " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, GetBaseSmeErrorResponse());
            }
        }
        
        [HttpGet]
        [Route("GetTokenTrans")]
        public async Task<ActionResult<SmeGetListTokenTransOutput>> GetTokenTrans([FromQuery] SmeGetListTokenTransInput input)
        {
            try
            {
                _logger.LogInformation(" >> GetTokenTrans >> input " + JsonConvert.SerializeObject(input));
                var res = await _smeService.GetListTokenTrans(input);
                var httpCode = res.code == SmeErrorCodes.Success ? 200 : 400;
                return StatusCode(httpCode, res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, " >> GetTokenTrans >>  " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, GetBaseSmeErrorResponse());
            }
        }
        
        [HttpGet]
        [Route("FindOneTokenTrans")]
        public async Task<ActionResult<FindOneTokenTransOutput>> FindOneTokenTrans([FromQuery] FindOneTokenTransInput input)
        {
            try
            {
                _logger.LogInformation(" >> FindOneTokenTrans >> input " + JsonConvert.SerializeObject(input));
                var res = await _smeService.FindOneTokenTrans(input);
                var httpCode = res.code == SmeErrorCodes.Success ? 200 : 400;
                return StatusCode(httpCode, res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, " >> FindOneTokenTrans >>  " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, GetBaseSmeErrorResponse());
            }
        }

        [HttpPost]
        [Route("CreateConnection")]
        public async Task<ActionResult<CreateSmeConnectionOutput>> CreateConnection([FromBody] CreateSmeConnectionInput input)
        {
            try
            {
                _logger.LogInformation(" >> Create Sme Connection >> input " + JsonConvert.SerializeObject(input));
                return await _smeService.CreateConnection(input);
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionResponseService.GetExceptionRewardReponse(ex);
                    var convertRewardErrorToGateway = SmeErrorCodes.RewardErrorConvert(res);
                    return StatusCode(400, convertRewardErrorToGateway);
                }
                _logger.LogError(ex, " >> Create Sme Connection >>  " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, GetBaseSmeErrorResponse());
            }
        }

        [HttpGet]
        [Route("GetListAddressShip")]
        public async Task<ActionResult<GetListAddressShipSmeOutput>> GetListAddressShip([FromQuery] GetListAddressShipSmeInput input)
        {
            try
            {
                return await _smeService.GetListAddressShip(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SME_GetListAddressShip_" + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionResponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionResponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpGet]
        [Route("GetAllLocation")]
        public async Task<ActionResult<GiftLocationSmeOutput>> GetAllLocation([FromQuery] GiftLocationSmeInput input)
        {
            try
            {
                return await _smeService.GetAllLocation(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SME_GetAllLocation_" + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionResponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionResponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpGet]
        [Route("GiftList")]
        public async Task<ActionResult<GiftListSmeOutput>> GiftList([FromQuery] GiftListSmeInput input)
        {
            try
            {
                return await _smeService.GiftListWithoutMemberCode(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SME_GiftList_" + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionResponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionResponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpGet]
        [Route("GiftDetail")]
        public async Task<ActionResult<GiftDetailSmeOutput>> GiftDetail([FromQuery] GiftDetailSmeInput input)
        {
            try
            {
                return await _smeService.GiftDetail(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SME_GiftDetail_" + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionResponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionResponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }
        [HttpPost]
        [Route("RedeemGift")]
        public async Task<ActionResult<SmeRedeemGiftOutput>> RedeemGift([FromBody] SmeRedeemGiftInput input)
        {
            try
            {
                this._logger.LogInformation(" >> SME >> RedeemGift >> " + JsonConvert.SerializeObject(input));
                var res = await _smeService.RedeemGift(input);
                var httpCode = res.code == SmeErrorCodes.Success ? 200 : 400;
                return StatusCode(httpCode, res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RedeemGift" + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionResponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionResponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }
        
        [HttpGet]
        [Route("GetRedeemTransaction")]
        public async Task<ActionResult<SmeGetRedeemTransOutput>> GetRedeemTransaction([FromQuery] SmeGetRedeemTransInput input)
        {
            try
            {
                this._logger.LogInformation(" >> SME >> GetRedeemTransaction >> " + JsonConvert.SerializeObject(input));
                var res = await _smeService.GetRedeemTransaction(input);
                var httpCode = res.code == SmeErrorCodes.Success ? 200 : 400;
                return StatusCode(httpCode, res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetRedeemTransaction" + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionResponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionResponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }
        [HttpGet]
        [Route("GetTxDetail")]
        public async Task<ActionResult<GetTxDetailOutput>> GetTxDetail([FromQuery] GetTxDetailInput input)
        {
            try
            {
                this._logger.LogInformation(" >> GetTxDetail >> " + JsonConvert.SerializeObject(input));
                var res = await _smeService.GetTxDetail(input);
                var httpCode = res.code == SmeErrorCodes.Success ? 200 : 400;
                return StatusCode(httpCode, res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetRedeemTransaction" + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionResponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionResponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }


        private BaseSmeOutput GetBaseSmeErrorResponse()
        {
            return new BaseSmeOutput()
            {
                code = SmeErrorCodes.UnknownError, // General error
                message = "Error Happened"
            };
        }
    }
}

