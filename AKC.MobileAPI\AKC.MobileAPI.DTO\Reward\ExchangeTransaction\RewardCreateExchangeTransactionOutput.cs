﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.ExchangeTransaction
{
    public class RewardCreateExchangeTransactionOutput
    {
        public int Result { get; set; }
        public RewardCreateExchangeTransactionItem Items { get; set; }
        public string Message { get; set; }
    }

    public class RewardCreateExchangeTransactionResult
    {
        public RewardCreateExchangeTransactionItem Transaction { get; set; }
    }

    public class RewardCreateExchangeTransactionItem
    {
        public string PartnerBindingTxId { get; set; }
        public long ExchangeAmount { get; set; }
        public long EquivalentTokenAmount { get; set; }
    }
}
