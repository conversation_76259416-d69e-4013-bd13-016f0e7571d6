﻿using AKC.MobileAPI.DTO.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AKC.MobileAPI.DTO.Loyalty.MasterCard
{
    public class MasterCardRedeemGiftInput
    {
        public string Cif { get; set; }
        public string PhoneNumber { get; set; }
        public int? Quantity { get; set; }
        public string giftCode { get; set; }
        public string CardCode { get; set; }
        public long? TotalAmount { get; set; }
        public string Description { get; set; }
    }
    public class MasterCardRedeemMultiGiftInput
    {
        public string Cif { get; set; }
        public string PhoneNumber { get; set; }
        public List<MasterCardRedeemGiftInfor> GiftInfors { get; set; }
        public string CardCode { get; set; }
        public string Description { get; set; }
    }

    public class MasterCardRedeemGiftInfor
    {
        public int? Quantity { get; set; }
        public string giftCode { get; set; }
        public long? TotalAmount { get; set; }
    }

    public class MasterCardRedeemGiftOutput
    {
        public string Cif { get; set; }
        public string GiftTransactionCode { get; set; }
        public string EgiftCode { get; set; }
        public DateTime? ExpiredDate { get; set; }
    }
    public class MasterCardRedeemMultiGiftResponse
    {
        public List<MasterCardRedeemMultiGiftOutput> Results { get; set; }
    }
    public class MasterCardRedeemMultiGiftOutput
    {
        public string Cif { get; set; }
        public string GiftCode { get; set; }
        public string GiftTransactionCode { get; set; }
        public string EgiftCode { get; set; }
        public DateTime? ExpiredDate { get; set; }
        public MasterCardErrorResponse Error { get; set; }
    }

    public class GetCardInforForRedeemOutput
    {
        public string CardCode { get; set; }
        public string Cif { get; set; }
        public decimal? GiftCoin { get; set; }
        public decimal? RemainGiftCoin { get; set; }
        public int? CampaignId { get; set; }
        public string GiftGroupCode { get; set; }
        public string MerchantId { get; set; }
        public string MemberCode { get; set; }
    }

    public class RedeemGiftResponse
    {
         public bool isSuccess { get; set; }
         public string transactionCode { get; set; }
         public string EgiftCode { get; set; }
         public DateTime? ExpiredDate { get; set; }
         public string Exception { get; set; }
         public bool Timeout { get; set; }
    }

    public class RedeemMultiGiftResponse
    {
        public bool isSuccess { get; set; }
        public string Exception { get; set; }
        public bool Timeout { get; set; }
        public List<ListEgiftRedeem> items { get; set; }
    }
    public class ListEgiftRedeem
    {
        public string transactionCode { get; set; }
        public string EgiftCode { get; set; }
        public DateTime? ExpiredDate { get; set; }
    }


    public class CreateRedeemGiftOrderResponse
    {
         public bool isSuccess { get; set; }
         public string transactionCode { get; set; }
         public object exception { get; set; }
    }

    public class ChallengrRedeemGiftInput
    {
        public string Cif { get; set; }
        public string PhoneNumber { get; set; }
        public int? Quantity { get; set; }
        public string GiftCode { get; set; }
        public string CardCode { get; set; }
        public long? TotalAmount { get; set; }
        public string Description { get; set; }
        public string GiftGroupCode { get; set; }
        public string MemberCode { get; set; }
        public DateTime Date { get; set; }
        public string RedeemSource { get; set; }
        public int MerchantIdRedeem { get; set; }
        public int MerchantId { get; set; }
    }
}
