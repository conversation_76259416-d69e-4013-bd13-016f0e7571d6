﻿using AKC.MobileAPI.Service.Exceptions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardBaseService : IRewardBaseService
    {
        protected readonly HttpClient _client = new HttpClient();
        protected readonly IConfiguration _configuration;
        protected string baseURL;
        protected int MerchantId;
        protected string accessToken;

        public RewardBaseService(IConfiguration configuration)
        {
            _client.Timeout = TimeSpan.FromSeconds(300);
            _configuration = configuration;
        }

        private void updateConfig(string rewardType)
        {
            if (!string.IsNullOrEmpty(rewardType))
            {
                baseURL = _configuration.GetSection("Reward" + rewardType + ":RemoteURL").Value;
                MerchantId = Convert.ToInt32(_configuration.GetSection("Reward" + rewardType + ":MerchantId").Value);
                accessToken = _configuration.GetSection("Reward" + rewardType + ":AccessToken").Value;
            }
            else
            {
                baseURL = _configuration.GetSection("Reward:RemoteURL").Value;
                MerchantId = Convert.ToInt32(_configuration.GetSection("Reward:MerchantId").Value);
                accessToken = _configuration.GetSection("Reward:AccessToken").Value;
            }
        }

        /// <summary>
        /// Perform a GET request to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> GetRewardAsync<T>(string apiURL, object query = null, string rewardType = null)
        {
            this.updateConfig(rewardType);
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }
            var requestURL = $"{baseURL}/{apiURL}";
            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }
            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Get
            };
            req.Headers.Add("MerchantId", MerchantId.ToString());
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            req.RequestUri = new Uri(requestURL);
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new RewardException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }
                response.EnsureSuccessStatusCode();
                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// Convert a object to query string format.
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public string GetQueryString(object obj)
        {
            var properties = from p in obj.GetType().GetProperties()
                             where p.GetValue(obj, null) != null
                             select p.Name + "=" + HttpUtility.UrlEncode(p.GetValue(obj, null).ToString());

            return string.Join("&", properties.ToArray());
        }

        public async Task<T> PostRewardAsync<T>(string apiURL, object body = null, string rewardType = null)
        {
            this.updateConfig(rewardType);
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }
            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };
            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body);
                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            req.Headers.Add("MerchantId", MerchantId.ToString());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();
                //Recall API when GatewayTimeout
                if (response.StatusCode == HttpStatusCode.GatewayTimeout)
                {
                    throw new TimeoutException("Request reward time out!");
                }
                //End Recall API when GatewayTimeout
                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new RewardException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }
                response.EnsureSuccessStatusCode();
                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            } 
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<T> PutRewardAsync<T>(string apiURL, object body = null, string rewardType = null)
        {
            this.updateConfig(rewardType);
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }
            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Put
            };
            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body);
                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            req.Headers.Add("MerchantId", MerchantId.ToString());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new RewardException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }
                response.EnsureSuccessStatusCode();
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }

        public async Task<string> getNational(string tokenBearer)
        {
            var idToken = tokenBearer.Replace("Bearer ", "").Replace("bearer ", "");
            var token = await FirebaseAdmin.Auth.FirebaseAuth.DefaultInstance.VerifyIdTokenAsync(idToken);
            return token.Uid;
        }

        public async Task<T> PostRewardAsyncV2<T>(string apiURL, object body = null, string rewardType = null)
        {
            this.updateConfig(rewardType);
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }
            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };
            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body);
                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            req.Headers.Add("MerchantId", MerchantId.ToString());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();
                //Recall API when GatewayTimeout
                if (response.StatusCode == HttpStatusCode.GatewayTimeout)
                {
                    throw new TimeoutException("Request reward time out!");
                }
                //End Recall API when GatewayTimeout
                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new RewardException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }
                response.EnsureSuccessStatusCode();
                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
