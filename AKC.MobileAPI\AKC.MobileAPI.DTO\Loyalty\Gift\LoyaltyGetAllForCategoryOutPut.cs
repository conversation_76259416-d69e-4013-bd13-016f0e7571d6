﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class LoyaltyGetAllForCategoryOutPut
    {
        public GiftInfosCategoryResult Result { get; set; }

    }

    public class GiftInfosCategoryResult
    {
        public int TotalCount { get; set; }

        public List<GiftInfosCategoryGetAllForview> Items { get; set; }
    }

    public class GiftInfosCategoryGetAllForview
    {
        public GiftInforDto GiftInfor { get; set; }

        public List<ImageLinkDto> ImageLink { get; set; }

        public GiftInfosCategoryGetAllForview()
        {
            ImageLink = new List<ImageLinkDto>();
        }
    }

    public class GiftInforDto
    {
        public string Code { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public string Introduce { get; set; }

        public string FullGiftCategoryCode { get; set; }

        public string BrandName { get; set; }
        public string ThirdPartyBrandName { get; set; }

        public string Vendor { get; set; }

        public DateTime EffectiveFrom { get; set; }

        public DateTime EffectiveTo { get; set; }

        public decimal RequiredCoin { get; set; }

        public string Status { get; set; }

        public decimal TotalQuantity { get; set; }

        public decimal UsedQuantity { get; set; }

        public decimal RemainingQuantity { get; set; }

        public decimal FullPrice { get; set; }
        public decimal DiscountPrice { get; set; }

        public bool IsEGift { get; set; }

        public SettingParamDto TargetAudience { get; set; }
        public int? TargetAudienceId { get; set; }

        public DateTime? LastModificationTime { get; set; }
        public DateTime CreationTime { get; set; }
        public string CreatedByUser { get; set; }
        public string UpdatedByUser { get; set; }

        public string Tag { get; set; }

        public int  Id { get; set; }

    }

    public class SettingParamDto
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public bool IsActive { get; set; }
        public TargetAudienceType? Type { get; set; }
        public int Count { get; set; }
        public bool IsFilterProfile { get; set; }
        public ICollection<SettingParamFieldDto> TargetAudienceDetail { get; set; }
    }

    public enum TargetAudienceType
    {
        Basic = 1,
        Segment = 2,
        MemberList = 3
    }


    public class SettingParamFieldDto
    {
        public string Code { get; set; }
        public int? TargetAudienceId { get; set; }
        public string Type { get; set; }
        public string FullValue { get; set; }
        public string Value { get; set; }
        public string FullValueName { get; set; }
        public string Level { get; set; }
        public int SegmentId { get; set; }

        public int Id { get; set; }
    }
}
