using System;
using System.Collections.Generic;

namespace AKC.MobileAPI.DTO.Sme
{
    public class BaseSmeOutput
    {
        public string code { get; set; }
        public string message { get; set; }
    }
    public class CreateSmeInput
    {
        public CreateSmeInputSme SmeInfo { get; set; }
        public List<CreateSmeInputMember> ListMemberInfo { get; set; }
    }

    public class CreateSmeInputSme
    {
        public string SmeCif { get; set; }
        public string LicenseNumber { get; set; }
        public string TaxNumber { get; set; }
        public string FullName { get; set; }
        public string EnglishName { get; set; }
        public string ShortName { get; set; }
        public string Address { get; set; }
        public string ContactEmail { get; set; }
        public string ContactPhone { get; set; }
        public DateTime? BusinessStartDate { get; set; }
    }

    public class CreateSmeInputMember
    {
        public string Cif { get; set; }
        public string Name { get; set; }
        public string PhoneNumber { get; set; }
        public string IdCard { get; set; }
        public DateTime? DoB { get; set; }
        public string Gender { get; set; }
    }

    public class CreateSmeOutput : BaseSmeOutput
    {
        public CreateSmeOutputResult Result { get; set; }
    }
    public class UtilCreateSmeOutput : CreateSmeOutput
    {
        public string ErrorCode { get; set; }
    }

    public class CreateSmeOutputResult
    {
        public int? SmeId { get; set; }
        public List<CreateSmeOutputResult2> ListMemberRs { get; set; }
    }

    public class CreateSmeOutputResult2
    {
        public string Cif { get; set; }
        public int? MemberId { get; set; }
        public string Message { get; set; } // Nếu MemberId = null thì message báo lỗi
    }
}