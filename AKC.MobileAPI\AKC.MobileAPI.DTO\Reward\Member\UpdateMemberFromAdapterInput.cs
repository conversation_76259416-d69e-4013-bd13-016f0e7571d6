﻿using System;
using System.ComponentModel.DataAnnotations;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class UpdateMemberFromAdapterInput
    {
        [StringLength(255, MinimumLength = 0)]
        public string CifCode { get; set; }
        [StringLength(255, MinimumLength = 0)]
        public string ShortName { get; set; }
        [StringLength(20, MinimumLength = 0)]
        public string Phone { get; set; }
        [StringLength(255, MinimumLength = 0)]
        public string Email { get; set; }
        public string Address { get; set; }		
        public string Gender { get; set; }
        public string IdCard { get; set; }
        public string Segment { get; set; }
        public string VipType { get; set; }
        public string MafType { get; set; }
        public string MafAssessStatus { get; set; }
        public DateTime? MafStartDate { get; set; }
        public DateTime? AfSignDate { get; set; }
        public DateTime? DoB { get; set; }
        
        public string IsStaff { get; set; } // FullMemberTypeCode

    }

    public class UpdateMemberFromAdapterOutput
    {
        public bool IsSuccess { get; set; }
        public string ErrorCode { get; set; }
        public string Message { get; set; }
    }
}