﻿using AKC.MobileAPI.DTO.Base;
using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.ChallengeMaf
{
    public class ChallengeMafOperatorInOut
    {
    }
    /// <summary>
    /// 
    /// </summary>
    public class CardTransactionGetAllInput
    {
        public string MemberCode { get; set; }
        public string Status { get; set; }
        public string CardCode { get; set; }
        public string RefCode { get; set; }
        public int? IsRedeem { get; set; }
        public int? MerchantId { get; set; }
        public int skipCount { get; set; }
        public int maxResultCount { get; set; }
    }
    public class GetCardMafResponse
    {
        public List<CardObject> Items { get; set; }
    }
    public class CardObject
    {
        public int Id { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public bool? IsDeleted { get; set; }
        public string MerchantID { get; set; }
        public string MemberCode { get; set; }
        public DateTime? BusinessTime { get; set; }
        public int? CampaignID { get; set; }
        public string PhoneNumber { get; set; }
        public string CardCode { get; set; }
        public string RefCode { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public decimal? TokenAmount { get; set; }
        public decimal? RemainingAmount { get; set; }
        public string Status { get; set; }
        public string Note { get; set; }
        public object History { get; set; }
        public string MerChantPhoto { get; set; }
    }
    /// <summary>
    /// 
    /// </summary>
    public class CardTransactionRedeemInput
    {
        public string MemberCode { get; set; }
        public string PhoneNumber { get; set; }
        public List<string> MoneyCardIds { get; set; }
        public decimal? TokenAmount { get; set; }
        public int MerchantId { get; set; }
        public string Reason { get; set; }
    }
    public class RewardRedeemCardMafResponse
    {
        public int? result { get; set; }
        public string message { get; set; }
        public List<RewardRedeemCardItem> item { get; set; }
    }
    public class RewardRedeemCardItem
    {
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public bool IsDeleted { get; set; }
        public int CardId { get; set; }
        public string FromWalletAddress { get; set; }
        public string ToWalletAddress { get; set; }
        public decimal? TokenAmount { get; set; }
        public string ActionType { get; set; }
        public string OrderCode { get; set; }
        public string Status { get; set; }
        public string Reason { get; set; }
        public int UserId { get; set; }
        public int Id { get; set; }
    }
    /// <summary>
    /// 
    /// </summary>
    public class CardTransactionReturnInput
    {
        public string OrderCode { get; set; }
        public string MemberCode { get; set; }
        public string PhoneNumber { get; set; }
        public string Reason { get; set; }
    }
    public class RewardReturnCardMafResponse
    {
        public int? result { get; set; }
        public string message { get; set; }
        public RewardRedeemCardItem item { get; set; }
    }
    /// <summary>
    /// 
    /// </summary>
    public class CardTransactionCreateInput
    {
        public DateTime? ExpiryDate { get; set; }
        public string MemberCode { get; set; }
        public string PhoneNumber { get; set; }
        public int? CampaignID { get; set; }
        public int MerchantID { get; set; }
        public decimal? TokenAmount { get; set; }
        public DateTime? BusinessTime { get; set; }
        public string RefCode { get; set; }
        public string Note { get; set; }
    }
    public class RewardResponse
    {
        public int result { get; set; }
        public string message { get; set; }
        public string messageDetail { get; set; }
    }
    public class CreateCardCoinOutput : RewardResponse
    {
        public CreateCardCoinItem item { get; set; }
    }
    public class CreateCardCoinItem
    {
        public int Id { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public bool IsDeleted { get; set; }
        public int MerchantID { get; set; }
        public string MemberCode { get; set; }
        public DateTime? BusinessTime { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public int CampaignID { get; set; }
        public string CardCode { get; set; }
        public string PhoneNumber { get; set; }
        public decimal? TokenAmount { get; set; }
        public decimal? RemainingAmount { get; set; }
        public string Status { get; set; }
        public string Note { get; set; }
    }
    /// <summary>
    /// 
    /// </summary>
    public class CardTransactionRevertInput
    {
        public string CardTransactionId { get; set; }
        public string MemberCode { get; set; }
        public string PhoneNumber { get; set; }
        public string Reason { get; set; }
    }
}
