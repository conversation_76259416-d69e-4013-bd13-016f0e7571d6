﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberRequestInput
    {
        //[Required]
        public string MemberCode { get; set; }
        public bool SimpleMode { get; set; } = true;
    }

    public class ViewMemberInput
    {
        public string PhoneNumber { get; set; }
        public string NationalId { get; set; }
    }

    public class ViewBalanceWithExpiringCoinOutput
    {
        public int Result { get; set; }
        public RewardMemberViewPoint Items { get; set; }
        public string MessageDetail { get; set; }
        public string Message { get; set; }
    }
    public class ViewBalanceWithExpiringCoinInput
    {
        public string UserAddress { get; set; }
    }
}
