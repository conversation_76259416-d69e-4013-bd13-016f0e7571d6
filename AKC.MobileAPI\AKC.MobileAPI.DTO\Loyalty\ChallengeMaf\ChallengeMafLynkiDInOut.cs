﻿using AKC.MobileAPI.DTO.Base;
using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.ChallengeMaf
{
    public class ChallengeMafLynkiDInOut
    {
    }
    /// <summary>
    /// 
    /// </summary>
    public class GetGiftByGroupChannelInput
    {
        public string MemberCode { get; set; }
        public int ChannelId { get; set; }
        public string ReferenceCode { get; set; }
        public string GiftGroupCode { get; set; }
        public string Lang { get; set; }
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
    }

    public class LynkiDGiftOutput
    {
        public GiftShortInforDto GiftInfor { get; set; }
        public List<ImageLinkDto> ImageLink { get; set; }
        public List<GiftUsageAddressShortDto> GiftUsageAddres { get; set; }
    }
    public class GiftShortInforDto
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Introduce { get; set; }
        public int? BrandId { get; set; }
        public string BrandName { get; set; }
        public string ThirdPartyBrandName { get; set; }
        public string Vendor { get; set; }
        public DateTime? EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public decimal RequiredCoin { get; set; }
        public string Status { get; set; }
        public decimal TotalQuantity { get; set; }
        public decimal UsedQuantity { get; set; }
        public decimal RemainingQuantity { get; set; }
        public decimal FullPrice { get; set; }
        public decimal DiscountPrice { get; set; }
        public bool IsEGift { get; set; }
        public string RegionCode { get; set; }
        public string ExpireDuration { get; set; }
        public int TotalWish { get; set; }
        public string VendorHotline { get; set; }
        public int? MerchantOrdinal { get; set; }
        public int? TotalRedeem { get; set; }
        public string VendorName { get; set; }
        public string VendorImage { get; set; }
        public string MerchantName { get; set; }
        public string MerchantAvatar { get; set; }
        public string BrandLinkLogo { get; set; }
        public string BrandDescription { get; set; }
        public string Condition { get; set; }
        public string ContactEmail { get; set; }
        public string ContactHotline { get; set; }
        public string GiftType { get; set; }
    }
    public class ImageLinkDto
    {
        public string Code { get; set; }
        public bool isActive { get; set; }
        public string FullLink { get; set; }
        public string Source { get; set; }
    }
    public class GiftUsageAddressShortDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }
        public string Latitude { get; set; }
        public string Longtitude { get; set; }
    }
    /// <summary>
    /// 
    /// </summary>
    public class ValidGiftByBrandInput
    {
        public List<string> GiftCodes { get; set; }
    }
    /// <summary>
    /// 
    /// </summary>
    public class VerifyAndCreateRedeemOrderInput
    {
        public string giftGroupCode { get; set; }
        public string cardCode { get; set; }
        public string memberCode { get; set; }
        public string giftCode { get; set; }
        public int? quantity { get; set; }
        public long? totalAmount { get; set; }
        public DateTime date { get; set; }
        public string redeemSource { get; set; }
        public int merchantIdRedeem { get; set; }
    }
    public class CreateRedeemMafGiftOrderResponse
    {
        public bool isSuccess { get; set; }
        public string transactionCode { get; set; }
        public object exception { get; set; }
    }
    /// <summary>
    /// 
    /// </summary>
    public class CreateRedeemMultiTxWithGroupCodeInput
    {
        public string giftGroupCode { get; set; }
        public string cardCode { get; set; }
        public string cardCodeTx { get; set; }
        public string memberCode { get; set; }
        public string transactionCode { get; set; }
        public string giftCode { get; set; }
        public int? quantity { get; set; }
        public long? totalAmount { get; set; }
        public DateTime date { get; set; }
        public string redeemSource { get; set; }
        public int merchantIdRedeem { get; set; }
    }
    public class RedeemMultiMafGiftResponse
    {
        public bool isSuccess { get; set; }
        public string Exception { get; set; }
        public bool Timeout { get; set; }
        public List<ListEgiftRedeem> items { get; set; }
    }
    public class ListEgiftRedeem
    {
        public string transactionCode { get; set; }
        public string EgiftCode { get; set; }
        public DateTime? ExpiredDate { get; set; }
    }

    public class GetGiftRedeemTransWithGiftGroupInput
    {
        public string MemberCode { get; set; }
        public string GiftGroupCode { get; set; }
        public string RedeemSource { get; set; }
        public string Lang { get; set; }
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
    }
    public class GiftRedeemedLoyaltyResultOuput
    {
        public string EGiftStatus { get; set; }
        public string TransactionCode { get; set; }
        public string EGiftCode { get; set; }
        public DateTime? EGiftExpiredDate { get; set; }
        public string GiftCode { get; set; }
        public string GiftName { get; set; }
        public string GiftType { get; set; }
        public string GiftIntroduce { get; set; }
        public string GiftDescription { get; set; }
        public string GiftPhoto { get; set; }
        public string TransactionDescription { get; set; }
        public decimal? Amount { get; set; }
        public DateTime? CreationTime { get; set; }
        public string GiftCondition { get; set; }
        public string GiftUsageAddress { get; set; }
        public string VendorName { get; set; }
        public string VendorLogo { get; set; }
        public int? BrandId { get; set; }
        public string BrandName { get; set; }
        public string BrandLogo { get; set; }
        public string Status { get; set; }
        public bool? IsEncrypted { get; set; }
    }
}
