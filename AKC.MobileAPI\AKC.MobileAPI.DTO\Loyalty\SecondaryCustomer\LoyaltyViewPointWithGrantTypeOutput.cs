﻿using AKC.MobileAPI.DTO.Reward.Member;
using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer
{
    public class LoyaltyViewPointWithGrantTypeOutput
    {
        public string MemberCode { get; set; }
        public decimal TotalCoin { get; set; }
        public decimal TotalAutoExchangeCoin { get; set; }
        public string LinkID_WalletAddress { get; set; }
        public decimal Point { get; set; }
        public List<ViewCoinResponse> Coin { get; set; }
    }
}
