﻿using System;
using System.Collections.Generic;
using System.Text;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Sme;

namespace AKC.MobileAPI.Service.Constants
{
    public class CommonConstants
    {
        public const string ACCESSS_TOKEN_CACHE_KEY = "ACCESSS_TOKEN_CACHE_KEY";
        public const string ALLOW_RENEW_LOYALTY_TOKEN = "ALLOW_RENEW_LOYALTY_TOKEN";
        // To do for dummy
        public const string ACCESSS_TOKEN_DUMMY_LOYALTY_CACHE_KEY = "ACCESSS_TOKEN_DUMMY_LOYALTY_CACHE_KEY";
        public const string ALLOW_RENEW_DUMMY_LOYALTY_TOKEN = "ALLOW_RENEW_DUMMY_LOYALTY_TOKEN";
        public const string GiftRedemptionRejectRouteKey = "gift.redemption.reject";
        public const string LANGUAGE_SOURCE_NAME_REDEEM = "RedeemTransaction";
        public const string LANGUAGE_SOURCE_NAME_REDEEM_REFIX = "RedeemMsg_";
        public const string LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR = "SystemError";
        public const string LANGUAGE_SOURCE_NAME_REDEEM_BALANCE_NOT_ENOUGH = "BalanceNotEnough";
        // LinkId integrate
        public const string ACCESSS_TOKEN_CACHE_KEY_LINKID = "ACCESSS_TOKEN_CACHE_KEY_LINKID";
        public const string ALLOW_RENEW_LOYALTY_TOKEN_LINKID = "ALLOW_RENEW_LOYALTY_TOKEN_LINKID";
        public const string REDEEM_KEY = "Redeem";
        public const string VPB_KEY = "VPB";
        public const string ADMIN_REDEEM_SOURCE_KEY = "247";
        public const string ACCESSS_TOKEN_VPBANK_LOYALTY_CACHE_KEY = "ACCESSS_TOKEN_VPBANK_LOYALTY_CACHE_KEY";
        public const string ACCESSS_TOKEN_SME_ON_UTIL = "ACCESSS_TOKEN_SME_UTILAPI";
        // Master card
        public const string ACCESSS_TOKEN_MASTER_CARD_CACHE_KEY = "ACCESSS_TOKEN_MASTER_CARD_CACHE_KEY";
        public const string ALLOW_RENEW_MASTER_CARD_TOKEN = "ALLOW_RENEW_MASTER_CARD_TOKEN";
        public const string MASTER_CARD_CAMPAIGN_CACHE_KEY = "MASTER_CARD_CAMPAIGN_CACHE_KEY";
        public const string MASTER_CARD_CUSTOMER_CHECK_KEY = "MASTER_CARD_CUSTOMER_CHECK_KEY";
        public const string MASTER_CARD_CAMPAIGN_GIFT_CACHE_KEY = "MASTER_CARD_CAMPAIGN_GIFT_CACHE_KEY";
        public const string MASTER_CARD_CAMPAIGN_GIFT_GROUP_BRAND_CACHE_KEY = "MASTER_CARD_CAMPAIGN_GIFT_GROUP_BRAND_CACHE_KEY";
    }

    public class SmeErrorCodes
    {
        public const string Success = "00";
        public const string UnknownError = "100";
        public static string SME_DOES_NOT_EXIST = "01";
        public static string SME_CIF_ALREADY_EXIST = "02";
        public static string SME_LICENSENUMBER_ALREADY_EXIST = "03";
        public static string SME_TAXNUMBER_ALREADY_EXIST = "04";
        public static string MEMBER_CIF_ALREADY_EXIST = "05";
        public static string MEMBER_NOT_EXIST_IN_SME = "06";
        public static string LOYSME_IS_INCONNECTION_TO_SAME_LINKIDSME = "07";
        public static string LOYSME_IS_INCONNECTION_TO_DIFF_LINKIDSME = "08";
        public static string LOYSME_DID_CONNECT_TO_OTHER_SMELINKID = "09";
        public static string LINKIDSME_DID_CONNECT_TO_OTHER_LOYALTYSME = "10";
        public static string LINKIDSME_IS_INCONNECTION_TO_DIFF_LOYSME = "11";
        public static string SME_STATUS_IS_INACTIVE = "12";
        public const string SME_SearchKeyWordIsTooShort = "101";
        public const string SME_GiftId_IsRequired = "102";
        public const string SME_NoLYNKIDCONNECTION = "103";
        public const string SME_NotEnoughToken = "104";
        public const string SME_DETAIL_TXNOTEXIST = "105";
        public const string CIFREQUIRED = "106";
        public const string SME__CREATECONN__LICENSEREQUIRED = "107";
        public const string SME__CREATECONN__TAXREQUIRED = "108";
        public const string SME__CREATECONN__DoBREQUIRED = "109";
        public const string SME__CREATECONN__ExtraDataREQUIRED = "110";
        public const string SME__CREATECONN__DoB_invalid = "111";
        public const string SME__CREATESME_CifxLicensexTax_REQUIRED = "112";
        public const string SME_TX_DETAIL_TXCODEREQUIRED = "113";
        public const string GIFTYPE_VALUE_IS_NOTVALID = "114";
        public const string SME_IS_INACONNECTION = "115";
        public const string SME_CANNOT_REDEEM_AT_THIS_TIME = "1022";
        public const string SME_GIFT_IS_NOT_AVAILABLE = "1023";
        public const string SME_GIFT_OUT_OF_STOCK = "1025";
        
        public const string REWARD___CONNECTION_EXISTS_ALREADY = "201";
        public const string REWARD___SMEInactive = "202";
        public const string REWARD___CifExistsWithDifferentLicenseNumber = "203";
        public const string REWARD___LynkiDSmeHasConnectionToOtherLoyaltyCif = "204";
        public const string REWARD___MerchantNotExist = "205";
        public const string REWARD___LicenseNumberExistsInOtherSme = "206";
        public const string REWARD___TaxNumberExistsInOtherSme = "207";
        public const string REWARD___SmeIsLocked = "208";
        
        public const string SME_REDEEM_GIFTCODE_REQUIRED = "300";
        public const string SME_REDEEM_QUANTITYNOTZERO = "301";
        public const string TOKENTRASN_NOT_FOUND = "302";
        public const string TOKENTRASN_Required = "303";

        public static BaseSmeOutput UtilErrorCodeConverter(string utilCode)
        {
            var gatewayCode = new BaseSmeOutput(){code = SmeErrorCodes.UnknownError, message = "Error Happened"};
            switch (utilCode)
            {
                case "01": gatewayCode.code = SME_DOES_NOT_EXIST;
                    gatewayCode.message = "Sme doesnot exist";
                    return gatewayCode;
                case "03": gatewayCode.code = SME_LICENSENUMBER_ALREADY_EXIST;
                    gatewayCode.message = "License number already exists";
                    return gatewayCode;
                case "04": gatewayCode.code = SME_TAXNUMBER_ALREADY_EXIST;
                    gatewayCode.message = "Tax number already exists";
                    return gatewayCode;
                case "07": gatewayCode.code = LOYSME_IS_INCONNECTION_TO_SAME_LINKIDSME;
                    gatewayCode.message = "Sme is connected to the same LynkiD account";
                    return gatewayCode;
                case "12": gatewayCode.code = SME__CREATESME_CifxLicensexTax_REQUIRED;
                    gatewayCode.message = "Creating new SME. Cif / LicenseNumber / Tax Number are required";
                    return gatewayCode;
                case "15": gatewayCode.code = SME_STATUS_IS_INACTIVE;
                    gatewayCode.message = "SME Status is inactive";
                    return gatewayCode;
                default: gatewayCode.code = SmeErrorCodes.UnknownError;
                    return gatewayCode;
            }
        }

        public static BaseSmeOutput LinkIDLoyatltyErrorCode(string code)
        {
            var res = new BaseSmeOutput() { code = UnknownError };
            switch (code)
            {
                case SME_GIFT_IS_NOT_AVAILABLE: res.code = code;
                    res.message = "Gift is not available";
                    return res;
                case SME_GIFT_OUT_OF_STOCK: res.code = code;
                    res.message = "Out of stock";
                    return res;
                case SME_CANNOT_REDEEM_AT_THIS_TIME: res.code = code;
                    res.message = "Cannot Redeem At This Time";
                    return res;
                default:
                    return res;
            }
            
        }

        public static BaseSmeOutput RewardErrorConvert(RewardErrorResponse inp)
        {
            var res = new BaseSmeOutput()
            {
                code = "100", message = "Unknown Error"
            };
            if (inp == null)
            {
                return res;
            }

            if (inp.Code == "SameConnectionExists")
            {
                res.code = REWARD___CONNECTION_EXISTS_ALREADY;
                res.message = "Connection exists already";
            }
            if (inp.Code == "SMEMemberInactive")
            {
                res.code = REWARD___SMEInactive;
                res.message = "SME on LynkiD is inactive";
            }
            if (inp.Code == "CifExistsWithDifferentLicenseNumber")
            {
                res.code = REWARD___CifExistsWithDifferentLicenseNumber;
                res.message = "CifExistsWithDifferentLicenseNumber";
            }
            if (inp.Code == "LynkiDSmeHasConnectionToOtherLoyaltyCif")
            {
                res.code = REWARD___LynkiDSmeHasConnectionToOtherLoyaltyCif;
                res.message = "LynkiDSmeHasConnectionToOtherLoyaltyCif";
            }
            if (inp.Code == "MerchantNotExist")
            {
                res.code = REWARD___MerchantNotExist;
                res.message = "MerchantNotExist";
            }
            if (inp.Code == "LicenseNumberExistsInOtherSme")
            {
                res.code = REWARD___LicenseNumberExistsInOtherSme;
                res.message = "LicenseNumberExistsInOtherSme";
            }
            if (inp.Code == "TaxNumberExistsInOtherSme")
            {
                res.code = REWARD___TaxNumberExistsInOtherSme;
                res.message = "TaxNumberExistsInOtherSme";
            }
            if (inp.Code == "SmeIsLocked")
            {
                res.code = REWARD___SmeIsLocked;
                res.message = "SME on LynkiD is locked";
            }
            return res;
        }
    }
}
