﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Article
{
    public class GetAllArticleAndRelatedNewsOutput
    {
        public ListResultGetAllArticleAndRelatedNewsOutput Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }

        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }
    public class ListResultGetAllArticleAndRelatedNewsOutput
    {
        public int TotalCount { get; set; }

        public List<DataResultGetAllArticle> Items { get; set; }
    }

    public class DataResultGetAllArticle
    {
        public CreateOrEditArticleDto Article { get; set; }

        public string CreatedByUser { get; set; }

        public List<RelatedNewsDto> RelatedNews { get; set; }
    }
}
