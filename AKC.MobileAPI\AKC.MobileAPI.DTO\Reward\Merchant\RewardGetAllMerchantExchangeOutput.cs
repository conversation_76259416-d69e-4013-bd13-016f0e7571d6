﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Merchant
{
    public class RewardGetAllMerchantExchangeOutput
    {
        public int Result { get; set; }
        public List<RewardGetAllMerchantExchangeItem> Items { get; set; }
        public int TotalCount { get; set; }
        public string Message { get; set; }
    }

    public class RewardGetAllMerchantExchangeItem
    {
        public int Id { get; set; }
        public string MerchantName { get; set; }
        public string Address { get; set; }
        public double BaseUnit { get; set; }
        public double PointExchangeRate { get; set; }
    }
}
