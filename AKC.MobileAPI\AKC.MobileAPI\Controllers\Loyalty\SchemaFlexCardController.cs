﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.DTO.Loyalty.Order;
using AKC.MobileAPI.DTO.Loyalty.SchemaFlexCard;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/SchemaFlexCard")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class SchemaFlexCardController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ISchemaFlexCardService _schemaFlexCardService;
        private readonly IExceptionReponseService _exceptionReponseService;
        public SchemaFlexCardController(
            ILogger<LoyaltyOrderController> logger,
            ISchemaFlexCardService schemaFlexCardService,
            IExceptionReponseService exceptionReponseService)
        {
            _logger = logger;
            _schemaFlexCardService = schemaFlexCardService;
            _exceptionReponseService = exceptionReponseService;
        }

        [HttpGet]
        [Route("GetAll")]
        public async Task<ActionResult<GetListSchemaFlexCard>> GetAll(string Cif, string CardType, string Language = "VI", int SkipCount = 0, int MaxResultCount = 10)
        {
            try
            {
                _logger.LogInformation($">> SchemaFlexCard >> GetAll >> Input >> Cif: {Cif} - CardType: {CardType} - Language: {Language}");
                var result = await _schemaFlexCardService.GetAll(Cif, CardType, Language, SkipCount, MaxResultCount);
                _logger.LogInformation($">> SchemaFlexCard >> GetAll >> Output >> {JsonConvert.SerializeObject(result)}");
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetAll Error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionFlexCardSchemaReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }
        
        [HttpGet]
        [Route("GetHistory")]
        public async Task<ActionResult<GetListHistorySchemaFlexCard>> GetHistory(string Cif, string CardType, string Language = "VI", int SkipCount = 0, int MaxResultCount = 10)
        {
            try
            {
                _logger.LogInformation($">> SchemaFlexCard >> GetHistory >> Input >> Cif: {Cif} - CardType: {CardType} - Language: {Language}");
                var result = await _schemaFlexCardService.GetHistory(Cif, CardType, Language, SkipCount, MaxResultCount);
                _logger.LogInformation($">> SchemaFlexCard >> GetHistory >> Output >> {JsonConvert.SerializeObject(result)}");
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetHistory Error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(LoyaltyException))
                {
                var res = await _exceptionReponseService.GetExceptionFlexCardSchemaReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("Create")]
        public async Task<ActionResult<SchemaFlexCardCreateOrUpdateOutput>> Create(SchemaFlexCardCreateOrUpdate input)
        {
            try
            {
                _logger.LogInformation($">> SchemaFlexCard >> Create >> Input >> {JsonConvert.SerializeObject(input)}");
                var result = await _schemaFlexCardService.Create(input);
                _logger.LogInformation($">> SchemaFlexCard >> Create >> Output >> {JsonConvert.SerializeObject(result)}");
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Create Error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionFlexCardSchemaReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("Update")]
        public async Task<ActionResult<SchemaFlexCardCreateOrUpdateOutput>> Update(SchemaFlexCardCreateOrUpdate input)
        {
            try
            {
                _logger.LogInformation($">> SchemaFlexCard >> Update >> Input >> {JsonConvert.SerializeObject(input)}");
                var result = await _schemaFlexCardService.Update(input);
                _logger.LogInformation($">> SchemaFlexCard >> Update >> Output >> {JsonConvert.SerializeObject(result)}");
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Update Error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionFlexCardSchemaReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }
    }
}
