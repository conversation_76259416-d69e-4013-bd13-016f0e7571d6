﻿using AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction;
using AKC.MobileAPI.DTO.Reward.TopUpTransaction;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardGiftRedeemTransactionService : RewardBaseService, IRewardGiftRedeemTransactionService
    {
        public RewardGiftRedeemTransactionService(
            IConfiguration configuration) : base(configuration)
        {
        }

        public async Task<RewardCreateGiftRedeemTransactionResponse> CreateRedeem(RewardCreateGiftRedeemTransactionRequest request)
        {
            return await PostRewardAsync<RewardCreateGiftRedeemTransactionResponse>(RewardApiUrl.REDEEM_GIFT_TRANSACTION_CREATE, request, MerchantNameConfig.VPID);
        }

        public async Task<RewardNapEvoucherOutputSuccess> CreateTransForTopupVoucher(RewardNapEvoucherInput request)
        {
            return await PostRewardAsync<RewardNapEvoucherOutputSuccess>(RewardApiUrl.MEMBER_TOPUP_EVOUCHER_CREATE_TRANS, request);
        }

        public async Task<RewardRevertGiftRedeemTransactionResponse> RevertRedeem(RewardRevertGiftRedeemTransactionRequest request)
        {
            return await PostRewardAsync<RewardRevertGiftRedeemTransactionResponse>(RewardApiUrl.REDEEM_GIFT_TRANSACTION_REVERT, request, MerchantNameConfig.VPID);
        }
    }
}
