﻿using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class GatewayMemberViewPointOutput
    {
        public LoyaltyRewardMemberViewPoint Items { get; set; }

    }

    public class LoyaltyRewardMemberViewCreditBalance
    {
        public string WalletAddress { get; set; }
        public decimal CreditBalance { get; set; }
    }
    public class LoyaltyRewardMemberViewPoint
    {
        public string MemberCode { get; set; }
        public decimal TotalCoin { get; set; }
        public decimal TotalAutoExchangeCoin { get; set; }
        public decimal TotalToken { get; set; }
        public decimal? ExpiringTokenAmount { get; set; }
        public string ExpiringDate { get; set; }
        public LoyaltyViewPointResponse Loyalty { get; set; }
        public List<ViewCoinResponse> Reward { get; set; }
    }

    public class ViewCoinResponse
    {
        public string GrantType { get; set; }
        public decimal Balance { get; set; }
        public decimal NearExpired { get; set; } = 0;
        public bool? AllowSync { get; set; }
    }

    public class LoyaltyViewPointResponse
    {
        public decimal Point { get; set; }
        public List<ViewCoinResponse> Coin { get; set; }
    }

    public class RewardMemberViewPointOutput
    {
        public int Result { get; set; }
        public RewardMemberViewPoint Items { get; set; }
        public string MessageDetail { get; set; }
        public string Message { get; set; }

    }

    public class RewardMemberViewPoint
    {
        public int Id { get; set; }
        public string MemberName { get; set; }
        public string Avatar { get; set; }
        public decimal TotalEquivalentAmount { get; set; }
        public decimal TokenBalance { get; set; }
        public decimal ExpiringTokenAmount { get; set; }
        public string ExpiringDate { get; set; }
        public decimal TempPointBalance { get; set; }
        public List<PartnerPointBalance> PartnerPointBalance { get; set; }
        public decimal TotalCount { get; set; }
        public string PointUsageType { get; set; }
        public List<GrantTypeResult> GrantTypeBalance { get; set; }
        public CustomSecondaryCustomerInfoDto MemberLoyaltyInfo { get; set; }
    }

    public class PartnerPointBalance
    {
        public int MerchantId { get; set; }
        public string MerchantName { get; set; }
        public decimal PointBalance { get; set; }
    } 

    public class LoyaltyRewardMemberViewPointV2 : LoyaltyRewardMemberViewPoint
    {
        public decimal? ExpiringTokenAmount { get; set; }
        public string ExpiringDate { get; set; }
    }
}
