﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.ThirdParty
{
    public class LoyaltyThirdPartyPointExchangeInput
    {
        public string AccessToken { get; set; }
        [Required]
        [Range(1, Double.MaxValue)]
        public int MerchantId { get; set; }
        [Required]
        public string IdNumber { get; set; }
       // [Required]
        public string MemberCode { get; set; }
        [Required]
        [Range(1, Double.MaxValue)]
        public long ExchangeAmount { get; set; }
        public string NationalId { get; set; }
    }
}
