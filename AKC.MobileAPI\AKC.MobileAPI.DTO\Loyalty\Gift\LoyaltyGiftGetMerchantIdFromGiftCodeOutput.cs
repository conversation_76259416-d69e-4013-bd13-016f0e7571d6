﻿namespace AKC.MobileAPI.DTO.Loyalty
{
    public class LoyaltyGiftGetMerchantIdFromGiftCodeOutput
    {
        public LoyaltyGiftGetMerchantIdFromGiftCodeResult Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }

        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }

    public class LoyaltyGiftGetMerchantIdFromGiftCodeResult
    {
        public string GiftCode { get; set; }
        public int? MerchantId { get; set; }
        public int? BrandId { get; set; }
        public int? VendorId { get; set; }
        public string GiftCategory { get; set; }
        public string Vendor { get; set; }
        public string Brand { get; set; }
        public decimal FullPrice { get; set; }
        public decimal RequiredCoin { get; set; }
        public string Merchant { get; set; }
    }
}
