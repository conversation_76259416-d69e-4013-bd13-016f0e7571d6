﻿using System;
using System.Collections.Generic;


namespace AKC.MobileAPI.DTO.Loyalty.FAQ
{
    public class GetAllFAQOutputDto
    {
        public ListResultFAQGetAll Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }



    public class ListResultFAQGetAll
    {
        public int TotalCount { get; set; }

        public List<ListFAQGetAllItems> Items { get; set; }

    }

    public class ListFAQGetAllItems
    {
        public FAQDto FAQ { get; set; }
        public string LastModifierUserName { get; set; }

    }

    public class FAQDto
    {
        public string Code { get; set; }

        public string Question { get; set; }

        public string Answer { get; set; }

        public int? Ordinal { get; set; }

        public bool isDeleted { get; set; }

        public long? DeleterUserId { get; set; }
        public DateTime? DeletionTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public long? LastModifierUserId { get; set; }
        public DateTime CreationTime { get; set; }
        public long? CreatorUserId { get; set; }


        public int Id { get; set; }

    }

}
