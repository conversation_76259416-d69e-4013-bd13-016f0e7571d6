﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.ChallengeMaf;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.Loyalty.Transaction;
using AKC.MobileAPI.DTO.Reward.Member;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyUtilsService
    {
        Task<CreateUnConfirmConnectionOutput> CreateUnConfirmConnection(CreateUnConfirmConnectionInput input);
        Task<CreateConfirmedConnectionOutput> CreateConfirmedConnection(CreateConfirmedConnectionInput input);
        Task<GetMemberInfoByCifOutput> GetMemberInfoByCif(GetMemberInfoByCifInput input);
        Task<GetMemberInfoByCifForAdapterOutput> GetMemberInfoByCifForAdapter(GetMemberInfoByCifForAdapterInput input);
        Task<UpdateMemberFromAdapterOutput> UpdateMemberFromAdapter(UpdateMemberFromAdapterInput input);
        Task<AdminCreateRedeemTransactionResponse> AdminCreateRedeemTransaction(AdminCreateRedeemTransactionRequest input);
        Task<GetAllBirthDayTransactionOutput> GetAllBirthDayTransaction(GetAllBirthDayTransactionInput input);
        Task<AdminCreateRedeemTransactionResponse> AdminGiveGiftToMember(AdminCreateRedeemTransactionRequest input);
        Task<GetAllGiftTransactionByGiftCategoryChannelResponse> GetAllGiftRedeemTransactionByGiftCategoryChannel_V1(GetAllGiftTransactionByGiftCategoryChannelInput input);
    }
}
