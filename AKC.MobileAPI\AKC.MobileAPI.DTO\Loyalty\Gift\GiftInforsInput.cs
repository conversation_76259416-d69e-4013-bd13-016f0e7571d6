﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class GiftInforsInput
    {
        [Required]
        public string MemberCode { get; set; }
        public string Filter { get; set; }
        public int? GroupTypeFilter { get; set; }
        //public string StatusFilter { get; set; }
        public string Sorting { get; set; }

        [Range(0, int.MaxValue)]
        public int MaxResultCount { get; set; }

        [Range(0, int.MaxValue)]
        public int SkipCount { get; set; }

        [Range(0, 100)]
        public int MaxItem { get; set; }
    }
}
