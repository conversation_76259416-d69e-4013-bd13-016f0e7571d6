﻿using AKC.MobileAPI.DTO.Loyalty.Gift;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty.Gift
{
    public interface ILoyaltyGiftService
    {
        Task<LoyaltyGiftCategoryGetAllOutput> GetAll(LoyaltyGiftCategoryGetAllInput input);
        Task<LoyaltyGetAllForCategoryOutPut> GetAllForCategory(LoyaltyGetAllForCategoryInput input);
        Task<LoyaltyGiftGetAllWithImageOutput> GetAllWithImage(LoyaltyGiftGetAllWithImageInput input);
        Task<LoyaltyGiftGetAllWithImageOutput> GetAllWithImageByMemberCode(LoyaltyGetAllGiftGroupByMemberInput input);
        Task<LoyaltyGiftGetAllByMemberCodeOutput> GetAllByMemberCode(LoyaltyGiftGetAllByMemberCodeInput input);
        Task<LoyaltyGetGiftByByMemberCodeOutput> GetGiftByMemberCode(LoyaltyGetGiftByByMemberCodeInput input);
        Task<GetGiftCategoryAndInfoForView> GetAllGiftCategoriesAndInfo_V1(GetAllGiftCategoriesAndInfoInput input);
        Task<GetGiftCategoryAndInfoForView> GetAllGiftCategoriesAndInfo(GetAllGiftCategoriesAndInfoInput input);
        Task<GiftInforsOutPut> GetAllInfors(GiftInforsInput input);
        Task<GetByIdAndRelatedGiftOutput> GetByIdAndRelatedGift(GetByIdAndRelatedGiftInput input);
        Task<GetAllEffectiveCategoryOutput> GetAllEffectiveCategory_v1(GetAllEffectiveCategoryInput input);
        Task<GetAllEffectiveCategoryOutput> GetAllEffectiveCategory(GetAllEffectiveCategoryInput input);
        Task<LoyaltyGetWishlistByMemberOutput> GetWishlistByMember(LoyaltyGetWishlistByMemberInput input);
        Task<LoyaltyUpdateWishlistOutput> UpdateWishlist(LoyaltyUpdateWishlistInput input);
        Task<GetAllByMemberCodeOutput> GetAllByMemberCode(GetAllByMemberCodeGiftCategoriesInput input);
        Task<GetAllForCategoryByMemberCodeOutput> GetAllForCategoryByMemberCode(GetAllByMemberCodeGiftInforsInput input);

    }
}
