using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Sme;

namespace AKC.MobileAPI.Service.Abstract.Sme
{
    public interface ISmeService
    {
        Task<UtilGetSmeInfoOutput> GetSmeInfo(GetSmeInfoInput input);
        Task<CreateSmeConnectionOutput> CreateConnection(CreateSmeConnectionInput input);
        Task<SmeViewPointOutput> ViewPoint(SmeViewPointInput request);
        Task<SmeCateListOutput> GetAllCategory(SmeCateListInput input);
        Task<GetListAddressShipSmeOutput> GetListAddressShip(GetListAddressShipSmeInput input);
        Task<GiftLocationSmeOutput> GetAllLocation(GiftLocationSmeInput input);
        Task<GiftListSmeOutput> GiftListWithoutMemberCode(GiftListSmeInput input);
        Task<GiftDetailSmeOutput> GiftDetail(GiftDetailSmeInput input);
        Task<SmeGetListTokenTransOutput> GetListTokenTrans(SmeGetListTokenTransInput input);
        Task<SmeRedeemGiftOutput> RedeemGift(SmeRedeemGiftInput input);
        Task<SmeGetRedeemTransOutput> GetRedeemTransaction(SmeGetRedeemTransInput input);
        Task<GetTxDetailOutput> GetTxDetail(GetTxDetailInput input);
        Task<FindOneTokenTransOutput> FindOneTokenTrans(FindOneTokenTransInput input);
    }
}