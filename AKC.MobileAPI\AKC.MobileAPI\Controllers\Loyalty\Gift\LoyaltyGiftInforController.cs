//using AKC.MobileAPI.DTO.Loyalty.Gift;
//using AKC.MobileAPI.Service.Abstract.Loyalty.Gift;
//using AKC.MobileAPI.Service.Exceptions;
//using Microsoft.AspNetCore.Authorization;
//using Microsoft.AspNetCore.Mvc;
//using Microsoft.Extensions.Logging;
//using Newtonsoft.Json;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading.Tasks;

//namespace AKC.MobileAPI.Controllers.Loyalty.Gift
//{
//    [Route("api/GiftInfos")]
//    [ApiController]
//    [ApiConventionType(typeof(DefaultApiConventions))]
//    [Authorize]
//    public class LoyaltyGiftInforController : ControllerBase
//    {
//        private readonly ILogger _logger;
//        private readonly ILoyaltyGiftService _loyaltyGiftService;
//        private readonly IExceptionReponseService _exceptionReponseService;
//        public LoyaltyGiftInforController
//        (
//              ILogger<LoyaltyGiftInforController> logger,
//              ILoyaltyGiftService loyaltyGiftService,
//              IExceptionReponseService exceptionReponseService
//        )
//        {
//            _logger = logger;
//            _loyaltyGiftService = loyaltyGiftService;
//            _exceptionReponseService = exceptionReponseService;
//        }

//        [HttpGet]
//        [Route("GetAllForCategory")]
//        public ActionResult<LoyaltyGetAllForCategoryOutPut> GetAllForCategory([FromQuery] LoyaltyGetAllForCategoryInput input)
//        {
//            try
//            {
//                var result = _loyaltyGiftService.GetAllForCategory(input).Result;
//                return StatusCode(200, result);
//            }
//            catch (Exception ex)
//            {
                
//                var res = _exceptionReponseService.GetExceptionLoyaltyReponse(ex).Result;
//                _logger.LogError(ex, "GetAllForCategory Infor Error - " + JsonConvert.SerializeObject(res));
//                return StatusCode(400, res);
//            }
//        }

//        [HttpGet]
//        [Route("GetAllByMemberCode")]
//        public async Task<ActionResult<LoyaltyGiftGetAllByMemberCodeOutput>> GetAllByMemberCode([FromQuery] LoyaltyGiftGetAllByMemberCodeInput input)
//        {
//            try
//            {
//                var result = await _loyaltyGiftService.GetAllByMemberCode(input);
//                return StatusCode(200, result);
//            }
//            catch (Exception ex)
//            {
//                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
//                _logger.LogError(ex, "GetAllByMemberCode Infor Error - " + JsonConvert.SerializeObject(res));

//                return StatusCode(400, res);
//            }
//        }


//        [HttpGet]
//        [Route("GetGiftByMemberCode")]
//        public async Task<ActionResult<LoyaltyGetGiftByByMemberCodeOutput>> GetGiftByMemberCode([FromQuery] LoyaltyGetGiftByByMemberCodeInput input)
//        {
//            try
//            {
//                var result = await _loyaltyGiftService.GetGiftByMemberCode(input);
//                return StatusCode(200, result);
//            }
//            catch (Exception ex)
//            {
//                _logger.LogError(ex, "GetAllByMemberCode Infor Error");
//                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
//                return StatusCode(400, res);
//            }
//        }

//        [HttpGet]
//        [Route("GetAllInfors")]
//        public async Task<ActionResult<GiftInforsOutPut>> GetAllInfors([FromQuery] GiftInforsInput input)
//        {
//            try
//            {
//                var result = await _loyaltyGiftService.GetAllInfors(input);
//                return StatusCode(200, result);
//            }
//            catch (Exception ex)
//            {
//                _logger.LogError(ex, "GetAllByMemberCode Infor Error");
//                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
//                return StatusCode(400, res);
//            }
//        }

//        [HttpGet]
//        [Route("GetByIdAndRelatedGift")]
//        public async Task<ActionResult<GetByIdAndRelatedGiftOutput>> GetByIdAndRelatedGift([FromQuery] GetByIdAndRelatedGiftInput input)
//        {
//            try
//            {
//                var result = await _loyaltyGiftService.GetByIdAndRelatedGift(input);
//                return StatusCode(200, result);
//            }
//            catch (Exception ex)
//            {
//                _logger.LogError(ex, "GetAllByMemberCode Infor Error");
//                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
//                return StatusCode(400, res);
//            }
//        }

//        [HttpGet]
//        [Route("GetAllEffectiveCategory")]
//        public async Task<ActionResult<GetAllEffectiveCategoryOutput>> GetAllEffectiveCategory([FromQuery] GetAllEffectiveCategoryInput input)
//        {
//            try
//            {
//                var result = await _loyaltyGiftService.GetAllEffectiveCategory(input);
//                return StatusCode(200, result);
//            }
//            catch (Exception ex)
//            {
//                _logger.LogError(ex, "GetAllEffectiveCategory Error");
//                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
//                return StatusCode(400, res);
//            }
//        }

//        [HttpGet]
//        [Route("GetAllEffectiveCategory_v1")]
//        public async Task<ActionResult<GetAllEffectiveCategoryOutput>> GetAllEffectiveCategory_v1([FromQuery] GetAllEffectiveCategoryInput input)
//        {
//            try
//            {
//                var result = await _loyaltyGiftService.GetAllEffectiveCategory_v1(input);
//                return StatusCode(200, result);
//            }
//            catch (Exception ex)
//            {
//                _logger.LogError(ex, "GetAllEffectiveCategory_v1 Error");
//                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
//                return StatusCode(400, res);
//            }
//        }

//        [HttpGet]
//        [Route("GetWishlistByMember")]
//        public async Task<ActionResult<LoyaltyGetWishlistByMemberOutput>> GetWishlistByMember([FromQuery] LoyaltyGetWishlistByMemberInput input)
//        {
//            try
//            {
//                var result = await _loyaltyGiftService.GetWishlistByMember(input);
//                return StatusCode(200, result);
//            }
//            catch (Exception ex)
//            {
//                _logger.LogError("GetWishlistByMember Infor Error", ex);
//                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
//                return StatusCode(400, res);
//            }
//        }

//        [HttpPut]
//        [Route("UpdateWishlist")]
//        public async Task<ActionResult<LoyaltyUpdateWishlistOutput>> UpdateWishlist(LoyaltyUpdateWishlistInput input)
//        {
//            try
//            {
//                var result = await _loyaltyGiftService.UpdateWishlist(input);
//                return StatusCode(200, result);
//            }
//            catch (Exception ex)
//            {
//                _logger.LogError("Quest join Error", ex);
//                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
//                return StatusCode(400, res);
//            }
//        }

//        [HttpGet]
//        [Route("GetAllForCategoryByMemberCode")]
//        public async Task<ActionResult<GetAllForCategoryByMemberCodeOutput>> GetAllForCategoryByMemberCode([FromQuery] GetAllByMemberCodeGiftInforsInput input)
//        {
//            try
//            {
//                var result = await _loyaltyGiftService.GetAllForCategoryByMemberCode(input);
//                return StatusCode(200, result);
//            }
//            catch (Exception ex)
//            {
//                _logger.LogError("GetAllForCategoryByMemberCode Infor Error", ex);
//                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
//                return StatusCode(400, res);
//            }
//        }
//    }
//}
