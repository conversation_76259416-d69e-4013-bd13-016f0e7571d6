﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberSendOtpForConnectMerchantOutput
    {
        public int Result { get; set; }
        public Dictionary<string, int> ExtraInfo { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; } = true;
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class RewardMemberSendOtpForConnectMerchantDto
    {
        public int Result { get; set; }
        public string Message { get; set; }
        public RewardMemberSendOtpForConnectMerchantItem Item { get; set; }
    }

    public class RewardMemberSendOtpForConnectMerchantItem
    {
        public int MemberId { get; set; }
        public string PhoneNumber { get; set; }
        public string MemberWalletAddress { get; set; }
    }
    public class RewardMemberAutoConnectMember3Output: RewardMemberSendOtpForConnectMerchantOutput
    {
        public string LinkiDMemberCode { get; set; }
    }
}
