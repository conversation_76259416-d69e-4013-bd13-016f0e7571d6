﻿using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyGiftTransactionsService
    {
        Task<LoyaltyCreateRedeemTransactionOutput> CreateRedeemTransaction(LoyaltyCreateRedeemTransactionInput input, HttpContext context);

        Task<LoyaltyGetAllWithEGiftOutput> GetAllWithEGift(LoyaltyGetAllWithEGiftInput input);

        Task<GetMemberCodeOutput> UserVerifying(GetMemberCodeInput input);

        Task<GiftTransferOutput> GiftTransfer(UpdateGiftTransactionsForTransferInput input);
        Task<LoyaltyCreateRedeemTransactionOutput> PostLoyaltyRedeem(LoyaltyCreateRedeemTransactionDto input, HttpContext context);
    }
}
