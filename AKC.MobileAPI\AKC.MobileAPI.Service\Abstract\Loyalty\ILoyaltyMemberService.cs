﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.Reward.Member;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyMemberService
    {
        Task<LoyaltyResponse<string>> CreateOrEdit(CreateOrEditSecondaryCustomerDto input);

        Task<LoyaltyResponse<CheckCifCodeOutput>> CheckCifCode(CheckCifCodeInput input);
        Task<LoyaltyResponse<GetContactListFromIdCardOutput>> GetContactListFromIdCard(GetContactListFromIdCardInput input);
        Task<LoyaltyResponse<string>> CheckCifCode_PHASE1(CheckCifCodeInput input);

        Task<LoyaltyResponse<string>> ReceivedCifCode(ReceivedCifCodeInput input);

        Task<LoyaltyResponse<GetTransactionHistoryOutput>> GetHistory(GetTransactionHistoryInput input);
        Task<LoyaltyResponse<VerifyConfirmConnectForConnectOutput>> VerifyConfirmConnectForConnect(VerifyConfirmConnectForConnectInput input);
        Task<LoyaltyResponse<LoyaltyMemberConfirmConnectMerchantOutput>> ConfirmConnect(LoyaltyMemberConfirmConnectMerchantInput input);
        Task<LoyaltyResponse<LoyaltyMemberRemoveConnectMerchantOutput>> RemoveConnect(LoyaltyMemberRemoveConnectMerchantInput input);
        Task<LoyaltyResponse<GetLinkIdMemberByCifCodeOutput>> GetLinkIdMemberByCifCode(GetLinkIdMemberByCifCodeInput input);
        Task<LoyaltyResponse<CheckConnectHistoryOutput>> CheckConnectHistory(CheckConnectHistoryInput input);
        Task<LoyaltyResponse<CreateUnConfirmConnectionOutput>> CreateUnConfirmConnection(CreateUnConfirmConnectionInput input);
        Task<LoyaltyResponse<GetMemberInfoByCifOutput>> GetMemberInfoByCif(GetMemberInfoByCifInput input);
    }
}
