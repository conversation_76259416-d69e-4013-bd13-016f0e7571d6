﻿using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Const;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.ChallengeMaf;
using AKC.MobileAPI.DTO.Loyalty.MasterCard;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.LoyaltyVendorGift;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.LoyaltyVendorGift.LinkID;
using AKC.MobileAPI.Service.LoyaltyVpbank;
using AKC.MobileAPI.Service.Reward;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class ChallengeMafService : BaseLoyaltyUtilService, IChallengeMafService
    {
        private readonly ILogger _logger;
        private readonly IDistributedCache _cache;
        private readonly BaseLinkIdLoyaltyVendorGiftService _baseLoyaltyService;
        private ILinkIdLoyaltyVendorGiftService _linkIdLoyaltyVendorGiftService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly IRewardBaseService _rewardBaseService;
        protected readonly string GiftGroupChannelId;
        protected readonly string GiftGroupSubType;
        protected readonly string _RedeemSource;
        protected readonly string MerchantId;
        public ChallengeMafService(
            IConfiguration configuration,
            IDistributedCache cache,
            IRewardBaseService rewardBaseService,
            ILogger<MasterCardService> logger,
            ILinkIdLoyaltyVendorGiftService linkIdLoyaltyVendorGiftService,
            IExceptionReponseService exceptionReponseService,
            ILogger<BaseLinkIdLoyaltyVendorGiftService> loggerService) : base(configuration, cache)
        {
            _logger = logger;
            _cache = cache;
            _baseLoyaltyService = new BaseLinkIdLoyaltyVendorGiftService(configuration, cache, loggerService);
            _rewardBaseService = rewardBaseService;
            _exceptionReponseService = exceptionReponseService;
            _linkIdLoyaltyVendorGiftService = linkIdLoyaltyVendorGiftService;
        }

        public async Task<LynkiDResponseList<LynkiDGiftOutput>> GetGiftByGroupChannel(GetGiftByGroupChannelInput input)
        {
            return await _baseLoyaltyService.GetLoyaltyAsync<LynkiDResponseList<LynkiDGiftOutput>>(LoyaltyApiUrl.GET_GIFT_BY_GROUP_CHANNEL, input);
        }

        public async Task<LynkiDResponse<bool>> ValidGiftByBrand(ValidGiftByBrandInput input)
        {
            return await _baseLoyaltyService.PostLoyaltyAsync<LynkiDResponse<bool>>(LoyaltyApiUrl.REDEEM_CHECK_GIFT_BRAND, input);
        }

        public async Task<LynkiDResponse<CreateRedeemMafGiftOrderResponse>> VerifyAndCreateRedeemOrder(VerifyAndCreateRedeemOrderInput input)
        {
            return await _baseLoyaltyService.PostLoyaltyAsync<LynkiDResponse<CreateRedeemMafGiftOrderResponse>>(LoyaltyApiUrl.VERIFY_AND_CREATE_REDEEM_ORDER, input);
        }

        public async Task<LynkiDResponse<RedeemMultiMafGiftResponse>> CreateRedeemMultiTxWithGroupCode(CreateRedeemMultiTxWithGroupCodeInput input)
        {
            return await _baseLoyaltyService.PostLoyaltyAsync<LynkiDResponse<RedeemMultiMafGiftResponse>>(LoyaltyApiUrl.REDEEM_MULTI_GIFT_BY_GROUP_CODE, input);
        }

        public async Task<LynkiDResponseList<GiftRedeemedLoyaltyResultOuput>> GetGiftRedeemTransWithGiftGroup(GetGiftRedeemTransWithGiftGroupInput input)
        {
            return await _baseLoyaltyService.GetLoyaltyAsync<LynkiDResponseList<GiftRedeemedLoyaltyResultOuput>>(LoyaltyApiUrl.REDEEM_GET_GIFT_REDEEMED, input);
        }
        // Operator
        public async Task<GetCardMafResponse> CardTransactionGetAll(CardTransactionGetAllInput input)
        {
            return await _rewardBaseService.GetRewardAsync<GetCardMafResponse>(RewardApiUrl.MASTER_CARD_GET_CARD_LIST, input, "Challenge");
        }
        public async Task<RewardRedeemCardMafResponse> CardTransactionRedeem(CardTransactionRedeemInput input)
        {
            return await _rewardBaseService.PostRewardAsync<RewardRedeemCardMafResponse>(RewardApiUrl.MASTER_CARD_REDEEM_CARD, input, "Challenge");
        }
        public async Task<RewardReturnCardMafResponse> CardTransactionReturn(CardTransactionReturnInput input)
        {
            return await _rewardBaseService.PostRewardAsync<RewardReturnCardMafResponse>(RewardApiUrl.MASTER_CARD_RETURN_CARD, input, "Challenge");
        }
        public async Task<CreateCardCoinOutput> CardTransactionCreate(CardTransactionCreateInput input)
        {
            return await _rewardBaseService.PostRewardAsync<CreateCardCoinOutput>(RewardApiUrl.CHALLENGEMAF_CARD_CREATE, input, "Challenge");
        }
        public async Task<RewardResponse> CardTransactionRevert(CardTransactionRevertInput input)
        {
            return await _rewardBaseService.PostRewardAsync<RewardResponse>(RewardApiUrl.CHALLENGEMAF_CARD_REVERT, input, "Challenge");
        }

        public async Task<List<MasterCardRedeemMultiGiftOutput>> RedeemGiftV2(ChallengrRedeemGiftInput input)
        {
            try
            {
                _logger.LogInformation("Start RedeemGiftV2 create Redeem Order loyalty Cif:" + input.Cif + " --- Gift Code: " + input.GiftCode);
                var createRedeemOrder = await _baseLoyaltyService.PostLoyaltyAsync<LoyaltyResponse<CreateRedeemGiftOrderResponse>>(LoyaltyApiUrl.VERIFY_AND_CREATE_REDEEM_ORDER, new
                {
                    giftGroupCode = input.GiftGroupCode,
                    cardCode = input.CardCode,
                    memberCode = input.MemberCode,
                    giftCode = input.GiftCode,
                    quantity = input.Quantity,
                    totalAmount = input.TotalAmount,
                    date = DateTime.UtcNow,
                    redeemSource = input.RedeemSource,
                    merchantIdRedeem = input.MerchantIdRedeem
                });
                if (createRedeemOrder != null && createRedeemOrder.Success && createRedeemOrder.Result != null && createRedeemOrder.Result.isSuccess)
                {
                    // Trừ điểm bên reward
                    try
                    {
                        _logger.LogInformation("Start RedeemGiftV2 Reward Redeem card Cif:" + input.Cif + " --- Gift Code: " + input.GiftCode);
                        var redeemCardCoin = await _rewardBaseService.PostRewardAsync<RewardRedeemCardResponse>(RewardApiUrl.MASTER_CARD_REDEEM_CARD, new
                        {
                            MemberCode = input.MemberCode,
                            PhoneNumber = "",
                            MoneyCardIds = new List<string>() { input.CardCode },
                            TokenAmount = input.TotalAmount,
                            MerchantId = input.MerchantIdRedeem,
                            Reason = "REDEEM"
                        }, "Challenge");
                        if (redeemCardCoin != null && redeemCardCoin.result == 200 && redeemCardCoin.item != null && redeemCardCoin.item.Count > 0)
                        {
                            try
                            {
                                // Active quà cho khách hàng theo transactionCode
                                _logger.LogInformation("Start RedeemGiftV2 Redeem Gift loyalty Cif:" + input.Cif + " --- Gift Code: " + input.GiftCode);
                                var activeTransaction = await _baseLoyaltyService.PostLoyaltyAsync<LoyaltyResponse<RedeemMultiGiftResponse>>(LoyaltyApiUrl.REDEEM_MULTI_GIFT_BY_GROUP_CODE, new
                                {
                                    giftGroupCode = input.GiftGroupCode,
                                    cardCode = input.CardCode,
                                    cardCodeTx = redeemCardCoin.item[0].OrderCode,
                                    memberCode = input.MemberCode,
                                    transactionCode = createRedeemOrder.Result.transactionCode,
                                    giftCode = input.GiftCode,
                                    quantity = input.Quantity,
                                    totalAmount = input.TotalAmount,
                                    date = DateTime.UtcNow,
                                    redeemSource = input.RedeemSource,
                                    merchantIdRedeem = input.MerchantIdRedeem
                                });
                                if (activeTransaction != null && activeTransaction.Success && activeTransaction.Result != null && activeTransaction.Result.isSuccess)
                                {
                                    _logger.LogInformation("Success RedeemGiftV2 Redeem Gift loyalty Cif:" + input.Cif + " --- Gift Code: " + input.GiftCode);
                                    var result = new List<MasterCardRedeemMultiGiftOutput>();
                                    foreach (var egiftItem in activeTransaction.Result.items)
                                    {
                                        var tranItem = new MasterCardRedeemMultiGiftOutput();
                                        tranItem.Cif = input.Cif;
                                        tranItem.GiftCode = input.GiftCode;
                                        tranItem.GiftTransactionCode = egiftItem.transactionCode;
                                        tranItem.EgiftCode = egiftItem.EgiftCode;
                                        tranItem.ExpiredDate = egiftItem.ExpiredDate.HasValue ? DateTime.Parse(egiftItem.ExpiredDate.Value.AddHours(7).ToString("yyyy-MM-ddTHH:mm:ss.999")) : egiftItem.ExpiredDate;
                                        result.Add(tranItem);
                                    }
                                    return result;
                                }
                                else
                                {
                                    _logger.LogError("Error RedeemGiftV2 Redeem Gift loyalty Cif:" + input.Cif + " --- Gift Code: " + input.GiftCode + " --- Response: " + JsonConvert.SerializeObject(activeTransaction));
                                    if (!activeTransaction.Result.Timeout)
                                    {
                                        await _rewardBaseService.PostRewardAsync<RewardReturnCardResponse>(RewardApiUrl.MASTER_CARD_RETURN_CARD, new
                                        {
                                            OrderCode = redeemCardCoin.item[0].OrderCode,
                                            MemberCode = input.MemberCode,
                                            PhoneNumber = "",
                                            Reason = "RETURN",
                                        }, "MasterCard");
                                        return new List<MasterCardRedeemMultiGiftOutput>(){ new MasterCardRedeemMultiGiftOutput()
                                        {
                                            Cif = input.Cif,
                                            GiftCode = input.GiftCode,
                                            Error = new MasterCardErrorResponse()
                                            {
                                                ErrorCode = 500,
                                                ErrorMessage = activeTransaction.Result.Exception
                                            }
                                        }};
                                    }
                                    else
                                    {
                                        throw new TimeoutException("Request merchant time out!");
                                    }
                                }
                            }
                            catch (LoyaltyException ex)
                            {
                                _logger.LogError("LoyaltyException RedeemGiftV2 Redeem Gift loyalty Cif:" + input.Cif + " --- Gift Code: " + input.GiftCode + " --- Message: " + ex.Message + " --- StackTrace: " + ex.StackTrace + " --- ErrorData: " + JsonConvert.SerializeObject(ex.Data));
                                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                                if (res.Code != "0" && res.Code != "SystemError")
                                {
                                    await _rewardBaseService.PostRewardAsync<RewardReturnCardResponse>(RewardApiUrl.MASTER_CARD_RETURN_CARD, new
                                    {
                                        OrderCode = redeemCardCoin.item[0].OrderCode,
                                        MemberCode = input.MemberCode,
                                        PhoneNumber = "",
                                        Reason = "RETURN",
                                    }, "MasterCard");
                                    return new List<MasterCardRedeemMultiGiftOutput>(){ new MasterCardRedeemMultiGiftOutput()
                                    {
                                        Cif = input.Cif,
                                        GiftCode = input.GiftCode,
                                        Error = new MasterCardErrorResponse()
                                        {
                                            ErrorCode = !string.IsNullOrEmpty(res.Code) ? Int32.Parse(res.Code) : 500,
                                            ErrorMessage = res.Message
                                        }
                                    }};
                                }
                                else
                                {
                                    throw ex;
                                }
                            }
                            catch (WebException ex)
                            {
                                throw ex;
                            }
                            catch (TaskCanceledException ex)
                            {
                                throw ex;
                            }
                        }
                        else
                        {
                            _logger.LogError("Error RedeemGiftV2 Redeem Gift loyalty Cif:" + input.Cif + " --- Gift Code: " + input.GiftCode + " --- Response: " + JsonConvert.SerializeObject(redeemCardCoin));
                            await PostLoyaltyAsync<MasterCardOutput<string>>(LoyaltyApiUtilsUrl.MASTER_CARD_UPDATE_REDEEM_STATUS_CARD, new { Cif = input.Cif, CardCode = input.CardCode }, "Challenge");
                            return new List<MasterCardRedeemMultiGiftOutput>(){ new MasterCardRedeemMultiGiftOutput()
                            {
                                Cif = input.Cif,
                                GiftCode = input.GiftCode,
                                Error = new MasterCardErrorResponse()
                                {
                                    ErrorCode = redeemCardCoin.result.HasValue ? redeemCardCoin.result.Value : 500,
                                    ErrorMessage = redeemCardCoin.message
                                }
                            }};
                        }
                    }
                    catch (RewardException ex)
                    {
                        _logger.LogError("RewardException RedeemGiftV2 Reward Redeem card Cif:" + input.Cif + " --- Gift Code: " + input.GiftCode + " --- Message: " + ex.Message + " --- StackTrace: " + ex.StackTrace + " --- ErrorData: " + JsonConvert.SerializeObject(ex.Data));
                        var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                        if (!string.IsNullOrEmpty(res.Code))
                        {
                            return new List<MasterCardRedeemMultiGiftOutput>(){ new MasterCardRedeemMultiGiftOutput()
                            {
                                Cif = input.Cif,
                                GiftCode = input.GiftCode,
                                Error = new MasterCardErrorResponse()
                                {
                                    ErrorCode = res.Code,
                                    ErrorMessage = res.MessageDetail != null ? res.MessageDetail.ToString() : res.Message
                                }
                            }};
                        }
                        else
                        {
                            throw ex;
                        }
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                }
                else
                {
                    // Tạo bản ghi đổi quà trước khi trừ điểm thất bại và trả về mã mỗi khác TimeoutException
                    _logger.LogError("Error RedeemGiftV2 create Redeem Order loyalty Cif:" + input.Cif + " --- Gift Code: " + input.GiftCode + " --- Response: " + JsonConvert.SerializeObject(createRedeemOrder));
                    return new List<MasterCardRedeemMultiGiftOutput>(){ new MasterCardRedeemMultiGiftOutput()
                    {
                        Cif = input.Cif,
                        GiftCode = input.GiftCode,
                        Error = new MasterCardErrorResponse()
                        {
                            ErrorCode = 500,
                            ErrorMessage = createRedeemOrder.Error
                        }
                    }};
                }
            }
            catch (LoyaltyException ex)
            {
                _logger.LogError("LoyaltyException RedeemGiftV2 create Redeem Order loyalty Cif:" + input.Cif + " --- Gift Code: " + input.GiftCode + " --- Message: " + ex.Message + " --- StackTrace: " + ex.StackTrace + " --- ErrorData: " + JsonConvert.SerializeObject(ex.Data));
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                if (res.Code != "0" && res.Code != "SystemError")
                {
                    return new List<MasterCardRedeemMultiGiftOutput>(){ new MasterCardRedeemMultiGiftOutput()
                    {
                        Cif = input.Cif,
                        GiftCode = input.GiftCode,
                        Error = new MasterCardErrorResponse()
                        {
                            ErrorCode = !string.IsNullOrEmpty(res.Code) ? Int32.Parse(res.Code) : 500,
                            ErrorMessage = res.Message
                        }
                    }};
                }
                else
                {
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Exception RedeemGiftV2 create Redeem Order loyalty Cif:" + input.Cif + " --- Gift Code: " + input.GiftCode + " --- Message: " + ex.Message + " --- StackTrace: " + ex.StackTrace + " --- ErrorData: " + JsonConvert.SerializeObject(ex.Data));
                throw ex;
            }
        }
    }
}
