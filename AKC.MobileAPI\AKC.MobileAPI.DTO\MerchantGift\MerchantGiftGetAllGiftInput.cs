﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.MerchantGift
{
    public class MerchantGiftGetAllGiftInput
    {
        public string CifCode { get; set; }
        public string CategoryCodeFilter { get; set; }
        public decimal? FromCointFilter { get; set; }
        public decimal? ToCoinFilter { get; set; }
        public string LocationCodeFilter { get; set; }
        public string BrandIdFilter { get; set; }
        public string Sorting { get; set; }
        public int? SkipCount { get; set; }
        public int? MaxResultCount { get; set; }
        
        // Search theo NAME or CODE
        public string Keyword { get; set; }
        public string GiftCategoryChannelCode { get; set; }
    }

    public class MerchantGiftGetAllGiftInputDto
    {
        public string MemberCode { get; set; }
        public string Filter { get; set; }
        public string FullGiftCategoryCodeFilter { get; set; }
        public bool? IsEGiftFilter { get; set; }
        public int MaxItem { get; set; }
        public int? GiftGroupTypeFilter { get; set; }
        public decimal? MaxRequiredCoinFilter { get; set; }
        public decimal? FromCointFilter { get; set; }
        public decimal? ToCoinFilter { get; set; }
        public string RegionCodeFilter { get; set; }
        public string Sorting { get; set; }
        public string GiftGroupNameFilter { get; set; }
        public string BrandIdFilter { get; set; }
        public int? SkipCount { get; set; }
        public int? MaxResultCount { get; set; }
        public string GiftCategoryChannelCode { get; set; }
    }
}
