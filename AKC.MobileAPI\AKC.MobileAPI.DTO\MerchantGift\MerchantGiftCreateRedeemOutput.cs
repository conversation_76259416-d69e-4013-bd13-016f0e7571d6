﻿using AKC.MobileAPI.DTO.Base;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.MerchantGift
{
    public class MerchantGiftCreateRedeemOutput
    {
        public MerchantGiftCreateRedeemTransaction Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }
    public class MerchantGiftCreateRedeemTransaction
    {
        public List<MerchantGiftDataRedeemTransaction> Items { get; set; }
        public int TotalCount { get; set; }
    }

    public class MerchantGiftDataRedeemTransaction
    {
        public string Code { get; set; }
        public decimal TotalCoin { get; set; }
        public string Status { get; set; }
        [JsonConverter(typeof(CustomDateTimeConverter))]
        public DateTime Date { get; set; }
        public string Description { get; set; }
        public MerchantGiftEGiftData EGift { get; set; }
    }

    public class MerchantGiftEGiftData
    {
        public string Code { get; set; }
        //public string Name { get; set; }
        public string Type { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string usedStatus { get; set; }
        [JsonConverter(typeof(CustomDateTimeConverter))]
        public DateTime? ExpiredDate { get; set; }
        public string QRCode { get; set; }
    }
}