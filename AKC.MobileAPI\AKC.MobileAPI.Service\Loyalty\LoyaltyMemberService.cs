﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.DTO.Loyalty.Order;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyMemberService : BaseLoyaltyService, ILoyaltyMemberService
    {
        public LoyaltyMemberService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<LoyaltyResponse<string>> CreateOrEdit(CreateOrEditSecondaryCustomerDto input)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<string>>(LoyaltyApiUrl.CREATE_SECONDARY_CUSTOMERS, input);
        }

        public async Task<LoyaltyResponse<CheckCifCodeOutput>> CheckCifCode(CheckCifCodeInput input)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<CheckCifCodeOutput>>(LoyaltyApiUrl.CHECK_CIF_CODE, input);
        }

        public async Task<LoyaltyResponse<GetContactListFromIdCardOutput>> GetContactListFromIdCard(GetContactListFromIdCardInput input)
        {
            return await GetLoyaltyAsync<LoyaltyResponse<GetContactListFromIdCardOutput>>(LoyaltyApiUrl.GET_CONTACT_LIST_FROM_ID_CARD, input);
        }

        public async Task<LoyaltyResponse<string>> CheckCifCode_PHASE1(CheckCifCodeInput input)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<string>>(LoyaltyApiUrl.CHECK_CIF_CODE_PHASE1, input);
        }

        public async Task<LoyaltyResponse<string>> ReceivedCifCode(ReceivedCifCodeInput input)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<string>>(LoyaltyApiUrl.RECEIVED_CIF_CODE, input);
        }

        public async Task<LoyaltyResponse<GetTransactionHistoryOutput>> GetHistory(GetTransactionHistoryInput input)
        {
            return await GetLoyaltyAsync<LoyaltyResponse<GetTransactionHistoryOutput>>(LoyaltyApiUrl.GET_TRANSACTION_HISTORY, input);
        }

        public async Task<LoyaltyResponse<VerifyConfirmConnectForConnectOutput>> VerifyConfirmConnectForConnect(VerifyConfirmConnectForConnectInput input)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<VerifyConfirmConnectForConnectOutput>>(LoyaltyApiUrl.MEMBER_VERIFY_CONNECT_MERCHANT, input);
        }

        public async Task<LoyaltyResponse<LoyaltyMemberConfirmConnectMerchantOutput>> ConfirmConnect(LoyaltyMemberConfirmConnectMerchantInput input)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<LoyaltyMemberConfirmConnectMerchantOutput>>(LoyaltyApiUrl.MEMBER_CONFIRM_CONNECT_MERCHANT, input);
        }

        public async Task<LoyaltyResponse<LoyaltyMemberRemoveConnectMerchantOutput>> RemoveConnect(LoyaltyMemberRemoveConnectMerchantInput input)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<LoyaltyMemberRemoveConnectMerchantOutput>>(LoyaltyApiUrl.MEMBER_REMOVE_CONNECT_MERCHANT, input);
        }

        public async Task<LoyaltyResponse<GetLinkIdMemberByCifCodeOutput>> GetLinkIdMemberByCifCode(GetLinkIdMemberByCifCodeInput input)
        {
            return await GetLoyaltyAsync<LoyaltyResponse<GetLinkIdMemberByCifCodeOutput>>(LoyaltyApiUrl.GET_LINID_MEMBER_ID_BY_CIF_CODE, input);
        }

        public async Task<LoyaltyResponse<CheckConnectHistoryOutput>> CheckConnectHistory(CheckConnectHistoryInput input)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<CheckConnectHistoryOutput>>(LoyaltyApiUrl.MEMBER_CHECK_CONNECT_HISTORY, input);
        }

        public async Task<LoyaltyResponse<CreateUnConfirmConnectionOutput>> CreateUnConfirmConnection(
            CreateUnConfirmConnectionInput input)
        {
            return await GetLoyaltyAsync<LoyaltyResponse<CreateUnConfirmConnectionOutput>>(LoyaltyApiUrl.CreateUnConfirmConnection, input);
        }

        public async Task<LoyaltyResponse<GetMemberInfoByCifOutput>> GetMemberInfoByCif(GetMemberInfoByCifInput input)
        {
            return await GetLoyaltyAsync<LoyaltyResponse<GetMemberInfoByCifOutput>>(LoyaltyApiUrl.GetMemberInfoByCif, input);
        }
    }
}
