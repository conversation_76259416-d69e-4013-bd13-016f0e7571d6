﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.BillPayment;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyBillPaymentService
    {
        Task<LoyaltyResponse<BillPaymentCheckBalanceOutput>> CheckBalance(BillPaymentCheckBalanceInput input);
        Task<LoyaltyResponse<string>> ComfirmUsingToken(BillPaymentConfirmUsingTokenInput input);
        Task<LoyaltyResponse<string>> RevertToken(BillPaymentRevertTokenInput input);
    }
}
