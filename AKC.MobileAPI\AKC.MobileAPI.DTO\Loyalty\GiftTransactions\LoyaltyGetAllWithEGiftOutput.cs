﻿using AKC.MobileAPI.DTO.Loyalty.Gift;
using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.GiftTransactions
{
    public class LoyaltyGetAllWithEGiftOutput
    {
        public GetAllWithEGifResult Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }


        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }

    public class GetAllWithEGifForView
    {
        public GiftTransactionDto GiftTransaction { get; set; }

        public EGiftInforDto EGift { get; set; }

        public ThirdPartyGiftVendorShortDto VendorInfo { get; set; }

        public List<ImageLinkDto> ImageLinks { get; set; }
    }
    public class GetAllWithEGifResult
    {
        public int TotalCount { get; set; }

        public List<GetAllWithEGifForView> Items { get; set; }

        public string GiftCategoryGiftCategoryName { get; set; }
    }
    public class EGiftInforDto
    {
        public string Type { get; set; }

        public string Code { get; set; }

        public string Description { get; set; }

        public string Status { get; set; }

        public string UsedStatus { get; set; }

        public DateTime? ExpiredDate { get; set; }

        public string GiftCode { get; set; }


        public int? GiftId { get; set; }

        public DateTime? LastModificationTime { get; set; }
        public DateTime CreationTime { get; set; }

        public bool UsageCheck { get; set; }
    }
    public class ThirdPartyGiftVendorShortDto
    {
        public string Type { get; set; }
        public string Image { get; set; }
        public string HotLine { get; set; }
        public int? Id { get; set; }
    }

    public class GiftTransactionDto
    {
        public string Code { get; set; }

        public string BuyerCode { get; set; }
        public string OwnerCode { get; set; }

        public DateTime? TransferTime { get; set; }

        public string MemberName { get; set; }

        public string Introduce { get; set; }

        public string Tag { get; set; }

        public string Phone { get; set; }

        public string Address { get; set; }

        public string GiftCode { get; set; }

        public string GiftName { get; set; }

        public string Name { get; set; }

        public int Quantity { get; set; }
        public float? Coin { get; set; }
        public DateTime? Date { get; set; }
        public string Status { get; set; }
        public int? MemberId { get; set; }

        public int? GiftId { get; set; }

        public string Reason { get; set; }

        public decimal TotalCoin { get; set; }

        public string Description { get; set; }

        public DateTime? LastModificationTime { get; set; }

        public string LinkAvatar { get; set; }

        public string TransactionCode { get; set; }

        public string QRCode { get; set; }

        public string CodeDisplay { get; set; }
        public int? Id { get; set; }
        public string LinkShippingInfo { get; set; }
    }

  
}
