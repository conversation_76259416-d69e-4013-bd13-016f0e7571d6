﻿using AKC.MobileAPI.DTO.Loyalty.BusinessLounge;
using AKC.MobileAPI.Service.Loyalty;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/BusinessLounge")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class BusinessLoungeController : ControllerBase
    {
        private IBusinessLoungeService _businessLoungeService;
        private readonly ILogger _logger;
        public BusinessLoungeController(IBusinessLoungeService businessLoungeService, ILogger<BusinessLoungeController> logger)
        {
            _businessLoungeService = businessLoungeService;
            _logger = logger;
        }

        [HttpPost]
        [Route("CheckCif")]
        public async Task<ActionResult<CheckCifAirportLoungeResponse>> CheckCif(CheckCifAirportLoungeRequest input)
        {
            try
            {
                var output = await _businessLoungeService.CheckCif(input);
                return StatusCode(200, output);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CheckCifAirportLounge - Input: " + JsonConvert.SerializeObject(input) + " Exception: " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, ex);
            }
        }

        [HttpPost]
        [Route("GetListAirportLounge")]
        public async Task<ActionResult<GetListAirportLoungeResponse>> GetListAirportLoungeByCif(GetListAirportLoungeRequest input)
        {
            try
            {
                var output = await _businessLoungeService.GetListAirportLoungeByCif(input);
                return StatusCode(200, output);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CheckCifAirportLounge - Input: " + JsonConvert.SerializeObject(input) + " Exception: " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, ex);
            }
        }
    }
}
