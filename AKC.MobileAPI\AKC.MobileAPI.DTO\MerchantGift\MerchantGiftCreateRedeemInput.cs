﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.MerchantGift
{
    public class MerchantGiftCreateRedeemInput
    {
        public string SessionId { get; set; }
        public string OtpCode { get; set; }
        public string CifCode { get; set; }
    }

    public class MerchantGiftCreateRedeemInputDto
    {
        public string MemberCode { get; set; }
        public string GiftCode { get; set; }
        public int Quantity { get; set; }
        public decimal TotalAmount { get; set; }
        public string Description { get; set; }
        public string Date { get; set; }
        public string TransactionCode { get; set; }
        public string VpoCifCode { get; set; }
        public string OtpSession { get; set; }
        public string RedeemSource { get; set; }
        public int? MerchantIdRedeem { get; set; }
    }

    public class MerchantGiftVerifyCreateRedeemInputDto
    {
        public string MemberCode { get; set; }
        public string GiftCode { get; set; }
        public int Quantity { get; set; }
        public decimal TotalAmount { get; set; }
        public string Date { get; set; }
        public string Description { get; set; }
        public string TransactionCode { get; set; }
        public string VpoCifCode { get; set; }
        public string OtpSession { get; set; }
    }

    public class MerchantGiftGetCreateRedeemInputDto
    {
        public string VpoCifCode { get; set; }
        public string LinkIdMemberCode { get; set; }
        public string OtpSession { get; set; }
    }

    public class MerchantGiftGetCreateRedeemOutputDto
    {
        public string MemberCode { get; set; }
        public string GiftCode { get; set; }
        public int? Quantity { get; set; }
        public decimal TotalAmount { get; set; }
        public string Date { get; set; }
        public string Description { get; set; }
        public string TransactionCode { get; set; }
    }
}