﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AKC.MobileAPI.DTO.Loyalty.Member
{
    public class CheckCifCodeOutput
    {
        public string LinkID_PhoneNumber { get; set; }
        public string Status { get; set; }
        public LoyaltyInfoDto LoyaltyInfo { get; set; }
    }

    public class LoyaltyInfoDto
    {
        public string CifCode { get; set; }
        public string MemberLoyaltyCode { get; set; }
    }

    public class GetContactListFromIdCardOutput
    {
        public string Status { get; set; }
        public List<string> ListPhoneNumbers { get; set; }
        public EsbAdapterCusInfoOutput FullContent { get; set; }
    }
    // Extra data 
    public class EsbAdapterCusInfoOutput
    {
        public string error { get; set; }
        public string message { get; set; }

        public string customerNumber { get; set; }
        public string customerName { get; set; }
        public string shortName { get; set; }
        public string dateOfBirth { get; set; }
        public string permanentAddress { get; set; }
        public string currentAddress { get; set; }
        public string customerType { get; set; }
        public string segment { get; set; }
        public string residence { get; set; }
        public string companyBook { get; set; }
        public string gender { get; set; }
        public string dao { get; set; }
        public string daoPb { get; set; }
        public string jobTitle { get; set; }
        public string legalId { get; set; }
        public string sectorCode { get; set; }
        public string sbvSector { get; set; }
        public string nationalId { get; set; }
        public string maritalStatus { get; set; }
        public string education { get; set; }
        public string officeName { get; set; }
        public string officeAddress { get; set; }
        public string nationality { get; set; }
        public string provinceCity { get; set; }
        public string mobileNo { get; set; }
        public string gbName { get; set; }
        public string vnName { get; set; }
        public string language { get; set; }
        public string vpbIndustry { get; set; }
        public string industry { get; set; }
        public string pcb { get; set; }
        public string mNemonic { get; set; }
        public string addCode { get; set; }
        public string addValue { get; set; }
        public string customerStatus { get; set; }
        public string accountOfficer { get; set; }
        public string target { get; set; }
        public string subSegment { get; set; }
        public EsbAdapterCusInfoDoc[] documentList { get; set; }
        public EsbAdapterCusInfoContact[] contactList { get; set; }
    }

    public class EsbAdapterCusInfoDoc
    {
        public string type { get; set; }
        public string number { get; set; }
        public string issuePlace { get; set; }
        public DateTime? issueDate { get; set; }
        public bool primary { get; set; }
    }

    public class EsbAdapterCusInfoContact
    {
        public string contactType { get; set; }
        public string contactInfo { get; set; }
    }
}
