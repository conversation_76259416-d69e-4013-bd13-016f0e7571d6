﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.SchemaFlexCard;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.LoyaltyVpbank;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class SchemaFlexCardService : BaseLoyaltyUtilService, ISchemaFlexCardService
    {
        public SchemaFlexCardService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<SchemaFlexCardCreateOrUpdateOutput> Create(SchemaFlexCardCreateOrUpdate input)
        {
            return await PostLoyaltyAsync<SchemaFlexCardCreateOrUpdateOutput>(LoyaltyApiUtilsUrl.CREATE_SCHEMA_FLEX_CARD, input);
        }

        public async Task<GetListSchemaFlexCard> GetAll(string Cif, string CardType, string Language, int SkipCount = 0, int MaxResultCount = 10)
        {
            return await GetLoyaltyAsync<GetListSchemaFlexCard>(LoyaltyApiUtilsUrl.GET_ALL_SCHEMA_FLEX_CARD, new { Cif = Cif , CardType = CardType, Language = Language, SkipCount = SkipCount , MaxResultCount  = MaxResultCount });
        }

        public async Task<GetListHistorySchemaFlexCard> GetHistory(string Cif, string CardType, string Language, int SkipCount = 0, int MaxResultCount = 10)
        {
            return await GetLoyaltyAsync<GetListHistorySchemaFlexCard>(LoyaltyApiUtilsUrl.GET_HISTORY_SCHEMA_FLEX_CARD, new { Cif = Cif, CardType = CardType, Language = Language, SkipCount = SkipCount, MaxResultCount = MaxResultCount });
        }

        public async Task<SchemaFlexCardCreateOrUpdateOutput> Update(SchemaFlexCardCreateOrUpdate input)
        {
            return await PostLoyaltyAsync<SchemaFlexCardCreateOrUpdateOutput>(LoyaltyApiUtilsUrl.UPDATE_SCHEMA_FLEX_CARD, input);
        }
    }
}
