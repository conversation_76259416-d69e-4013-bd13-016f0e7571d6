﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.MasterCard;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface IMasterCardService
    {
        Task<MasterCardOutput<MasterCardCheckRegisterOutput>> CustomerCheck(MasterCardCustomerInput input);
        Task<LoyaltyResponsResultDto<MasterCardCampaignOutput>> GetCampaignByCustomer(string Cif, string Status, string Lang = "vi");
        Task<MasterCardOutput<string>> RegisterCampaign(RegisterCampaignInput input);
        Task<LoyaltyResponsResultDto<MasterCardGiftByCampaignOutput>> GetGiftByCampaign(string Cif, string CampaignId, string Lang = "vi");
        Task<LoyaltyResponsResultDto<MasterCardGiftByCampaignGroupBrandOutput>> GetGiftByCampaignGroupBrand(string Cif, string CampaignId, string CardCode, string Lang = "vi");
        Task<MasterCardOutput<GiftRedeemedAndCardInfor>> GetAllGiftRedeemed(string Cif, string Lang = "vi");
        Task<MasterCardRedeemGiftOutput> RedeemGift(MasterCardRedeemGiftInput input);
        Task<MasterCardRedeemMultiGiftResponse> RedeemMultiGift(MasterCardRedeemMultiGiftInput input);
        Task<ChallengeInforAfterUpdateOutput> GetInforChallenge(string Cif, string CampaignId, string TransactionCode, string Lang = "vi");
        Task<LoyaltyResponsResultDto<MasterCardCampaignOutput>> GetCardCoin(string Cif, string Lang = "vi");
    }
}
