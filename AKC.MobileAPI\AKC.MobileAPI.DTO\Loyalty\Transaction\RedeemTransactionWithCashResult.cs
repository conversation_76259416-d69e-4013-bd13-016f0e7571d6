using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Transaction
{
    public class RedeemTransactionWithCashResult
    {
        public bool Success { get; set; }
        public string ErrorCode { get; set; }
        public string ErrorMessage { get; set; }
        public RedeemTransactionWithCashResponse Data { get; set; }
        public string RequestId { get; set; }

        public static RedeemTransactionWithCashResult CreateSuccess(RedeemTransactionWithCashResponse data, string requestId = null)
        {
            return new RedeemTransactionWithCashResult
            {
                Success = true,
                Data = data,
                RequestId = requestId
            };
        }

        public static RedeemTransactionWithCashResult CreateError(string errorCode, string errorMessage, string requestId = null)
        {
            return new RedeemTransactionWithCashResult
            {
                Success = false,
                ErrorCode = errorCode,
                ErrorMessage = errorMessage,
                RequestId = requestId
            };
        }
    }
}
