﻿using System;
using System.Collections.Generic;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class CreateUnconfirmedConnectionInput
    {
        public string Cif { get; set; }
        public string LinkIdPhoneNumber { get; set; }
        public CreateUnconfirmedConnectionInputExtra ExtraInfo { get; set; }
    }

    public class CreateUnconfirmedConnectionInputExtra
    {
        public string Name { get; set; }
        public string Email { get; set; }
        public string IdCard { get; set; }
        public DateTime DoB { get; set; }
        public string Address { get; set; }
        public string Gender { get; set; }
    }
    public class CreateUnconfirmedConnectionInputSendOperator
    {
        public string MemberCode { get; set; }
        public int MerchantId { get; set; }
        public string RefreshToken { get; set; }
        public bool IsChangedLoyalty { get; set; }
        public string ReferenceData { get; set; }
        public MemberLoyaltyInfoSendOperator MemberLoyaltyInfo { get; set; }
        public string ConnectSource { get; set; }
    }

    public class MemberLoyaltyInfoSendOperator
    {
        public string Cif { get; set; }
        public string MemberLoyaltyCode { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string IdCard { get; set; }
        public string Gender { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string StandardMemberCode { get; set; }
        public string PartnerPhoneNumber { get; set; }
        public DateTime? Dob { get; set; }
    }
    
    public class GetMemberInfoByCifInput
    {
        public string Cif { get; set; }
    }

    public class GetMemberInfoByCifForAdapterInput
    {
        public string Cif { get; set; }
    }

    public class GetMemberInfoByCifForAdapterOutput
    {
        public bool IsSuccess { get; set; } = true;
        public string ErrorCode { get; set; }
        public string ErrorMessage { get; set; }
        public int LinkIdConnectionStatus { get; set; }
        public string CurrentLinkIdPhoneNumber { get; set; }
        public string CurrentLinkIdWalletAddress { get; set; }
        public string CurrentLinkIdMemberCode { get; set; }
        public int CurrentLinkIdMemberId { get; set; }
        public GetMemberInfoByCifInputBasicInfo UserInfo { get; set; }
        public List<GetMemberInfoByCifInputHisForAdapter> ConnectionHistory { get; set; }
    }

    public class GetMemberInfoByCifInputHisForAdapter
    {
        public string LinkIDPhoneNumber { get; set; }
        public int LinkIDMemberId { get; set; }
        public string LinkIDWallet { get; set; }
        public string StatusConnect { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? ConfirmedAt { get; set; }
        public DateTime? RemovedAt { get; set; }
        public int? ConnectSource { get; set; }
        public DateTime? ConnectedAt { get; set; }
    }

    public class GetMemberInfoByCifOutput
    {
        public bool IsSuccess { get; set; } = true;
        public string ErrorCode { get; set; }
        public string ErrorMessage { get; set; }
        public int LinkIdConnectionStatus { get; set; }
        public string CurrentLinkIdPhoneNumber { get; set; }
        public GetMemberInfoByCifInputBasicInfo UserInfo { get; set; }
        public List<GetMemberInfoByCifInputHis> ConnectionHistory { get; set; }
    }

    public class GetMemberInfoByCifInputHis
    {
        public string LinkIDPhoneNumber { get; set; }
        public int LinkIDMemberId { get; set; }
        public string LinkIDWallet { get; set; }
        public string StatusConnect { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? ConfirmedAt { get; set; }
        public DateTime? RemovedAt { get; set; }
    }
    public class GetMemberInfoByCifInputBasicInfo
    {
        public string Name { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public DateTime? DoB { get; set; }
        public string Gender { get; set; }
        public string Phone { get; set; }
        public string IdCard { get; set; }
        public string Segment { get; set; }
        public string VIPType { get; set; }
    }
}