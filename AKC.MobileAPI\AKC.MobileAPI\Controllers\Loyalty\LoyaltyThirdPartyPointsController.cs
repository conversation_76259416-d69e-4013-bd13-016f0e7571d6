﻿using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.Reward.ExchangeTransaction;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
//using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Net;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/3rd")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyThirdPartyPointsController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILoyaltyThirdPartyService _loyaltyThirdPartyService;
        private readonly IRewardMemberService _rewardMemberService;
        private readonly IRewardExchangeTransactionService _rewardExchangeTransactionService;
        public LoyaltyThirdPartyPointsController(
            ILogger<LoyaltyThirdPartyPointsController> logger,
            IExceptionReponseService exceptionReponseService,
            ILoyaltyThirdPartyService loyaltyThirdPartyService,
            IRewardExchangeTransactionService rewardExchangeTransactionService,
            IRewardMemberService rewardMemberService)
        {
            _logger = logger;
            _exceptionReponseService = exceptionReponseService;
            _loyaltyThirdPartyService = loyaltyThirdPartyService;
            _rewardExchangeTransactionService = rewardExchangeTransactionService;
            _rewardMemberService = rewardMemberService;
        }

        #region Points/View
        //[HttpGet]
        //[Route("Points/View")]
        //public async Task<ActionResult<LoyaltyThirdPartyPointViewOutput>> PointView([FromQuery] LoyaltyThirdPartyPointViewInput input)
        //{
        //    try
        //    {
        //        var context = HttpContext;
        //        var result = await _loyaltyThirdPartyService.PointView(input, context);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //        _logger.LogError(ex, "ThirdParty PointView Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}
        #endregion


        //[HttpPost]
        //[Route("Verification/VerifyNationalId")]
        //public async Task<ActionResult<LoyaltyThirdPartyVerifyNationalIdOutput>> VerifyNationalId(LoyaltyThirdPartyVerifyNationalIdInput input)
        //{
        //    try
        //    {
        //        var context = HttpContext;
        //        var result = await _loyaltyThirdPartyService.VerifyNationalId(input, context);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        if (ex.GetType() == typeof(RewardException))
        //        {
        //            var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //            _logger.LogError(ex, "ThirdParty VerifyNationalId Error - " + JsonConvert.SerializeObject(ex));

        //            return StatusCode(400, res);
        //        }
        //        else if (ex.Message == "YouAreNotInTheTestList")
        //        {
        //            return StatusCode(400, new RewardErrorResponse()
        //            {
        //                Result = 400,
        //                Code = "YouAreNotInTheTestList",
        //                Message = "Bạn không nằm trong danh sách thử nghiệm, vui lòng liên hệ số hotline để biết thêm chi tiết.",
        //                MessageDetail = "Bạn không nằm trong danh sách thử nghiệm, vui lòng liên hệ số hotline để biết thêm chi tiết."
        //            });
        //        }
        //        else
        //        {
        //            var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //            _logger.LogError(ex, "ThirdParty VerifyNationalId Error - " + JsonConvert.SerializeObject(ex));

        //            return StatusCode(400, res);
        //        }
        //    }
        //}

        //[HttpPost]
        //[Route("Verification/VerifyOTP")]
        //public async Task<ActionResult<LoyaltyThirdPartyVerifyOTPOutput>> VerifyOTP(LoyaltyThirdPartyVerifyOTPInput input)
        //{
        //    try
        //    {
        //        var context = HttpContext;
        //        var result = await _loyaltyThirdPartyService.VerifyOTP(input, context);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        if (ex.GetType() == typeof(RewardException))
        //        {
        //            var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //            _logger.LogError(ex, "ThirdParty VerifyOTP Error - " + JsonConvert.SerializeObject(ex));

        //            return StatusCode(400, res);
        //        }
        //        else
        //        {
        //            var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //            _logger.LogError(ex, "ThirdParty VerifyOTP Error - " + JsonConvert.SerializeObject(ex));

        //            return StatusCode(400, res);
        //        }
        //    }
        //}
        #region Points/Exchange
        //[HttpPost]
        //[Route("Points/Exchange")]
        //public async Task<ActionResult<LoyaltyThirdPartyPointExchangeOutput>> PointExchange(LoyaltyThirdPartyPointExchangeInput input)
        //{
        //    try
        //    {
        //        var context = HttpContext;
        //        var ipAddressList = Dns.GetHostEntry(Dns.GetHostName()).AddressList;
        //        var ipAddress = ipAddressList.GetValue(ipAddressList.Length - 1).ToString();
        //        _logger.LogInformation("Public_IP_" + ipAddress);
        //        var rewardResult = await _loyaltyThirdPartyService.PointExchange(input, context);
        //        return StatusCode(200, rewardResult);
        //    }
        //    catch (Exception ex)
        //    {
        //        if (ex.GetType() == typeof(RewardException))
        //        {
        //            var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //            _logger.LogError(ex, "ThirdParty Points/Exchange Error - " + JsonConvert.SerializeObject(ex));

        //            return StatusCode(400, res);
        //        }
        //        else if (ex.Message == "YouAreNotInTheTestList")
        //        {
        //            return StatusCode(400, new RewardErrorResponse()
        //            {
        //                Result = 400,
        //                Code = "YouAreNotInTheTestList",
        //                Message = "Bạn không nằm trong danh sách thử nghiệm, vui lòng liên hệ số hotline để biết thêm chi tiết.",
        //                MessageDetail = "Bạn không nằm trong danh sách thử nghiệm, vui lòng liên hệ số hotline để biết thêm chi tiết."
        //            });
        //        }
        //        else
        //        {
        //            var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //            _logger.LogError(ex, "ThirdParty Points/Exchange Error - " + JsonConvert.SerializeObject(ex));

        //            return StatusCode(400, res);
        //        }
        //    }
        //}
        #endregion

        #region Verification/RequestAccessToken
        //[HttpPost]
        //[Route("Verification/RequestAccessToken")]
        //public async Task<ActionResult<LoyaltyThirdPartyRequestAccessTokenOutput>> RequestAccessToken(LoyaltyThirdPartyRequestAccessTokenInput input)
        //{
        //    try
        //    {
        //        var context = HttpContext;
        //        var rewardResult = await _loyaltyThirdPartyService.RequestAccessToken(input, context);
        //        return StatusCode(200, rewardResult);
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, "ThirdParty RequestAccessToken Error" + JsonConvert.SerializeObject(ex));
        //        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);

        //        return StatusCode(400, res);
        //    }
        //}
        #endregion

        #region UpdatePartnerCaching
        //[HttpPost]
        //[Route("UpdatePartnerCaching")]
        //public async Task<ActionResult<LoyaltyThirdPartyUpdatePartnerCachingOutput>> UpdatePartnerCaching(LoyaltyThirdPartyUpdatePartnerCachingInput input)
        //{
        //    try
        //    {
        //        var context = HttpContext;
        //        var rewardResult = await _loyaltyThirdPartyService.UpdatePartnerCaching(input, context);

        //        return StatusCode(rewardResult.Result, rewardResult);
        //    }
        //    catch (Exception ex)
        //    {
        //        if (ex.GetType() == typeof(RewardException))
        //        {
        //            var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //            _logger.LogError(ex, "ThirdParty UpdatePartnerCaching Error - " + JsonConvert.SerializeObject(ex));

        //            return StatusCode(400, res);
        //        }
        //        else
        //        {
        //            var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //            _logger.LogError(ex, "ThirdParty UpdatePartnerCaching Error - " + JsonConvert.SerializeObject(ex));

        //            return StatusCode(400, res);
        //        }
        //    }
        //}
        #endregion


        //[HttpPost]
        //[Route("Points/Revert")]
        //public async Task<ActionResult<LoyaltyThirdPartyRevertPointOutput>> RevertPoint(LoyaltyThirdPartyRevertPointInput input)
        //{
        //    try
        //    {
        //        var result = await _loyaltyThirdPartyService.RevertPoint(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //        _logger.LogError(ex, "ThirdParty Points/Revert Error - " + JsonConvert.SerializeObject(res));

        //        return StatusCode(400, res);
        //    }
        //}
    }
}
