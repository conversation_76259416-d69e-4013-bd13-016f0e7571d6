﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.SchemaFlexCard
{
    public class GetListSchemaFlexCard
    {
        public int TotalCount { get; set; }
        public List<FlexCardPakageOutput> Datas { get; set; }
        public List<SchemaFlexCardOutput> Items { get; set; }
    }

    public class GetListHistorySchemaFlexCard
    {
        public int TotalCount { get; set; }
        public List<SchemaFlexCardHistoryOutput> Items { get; set; }
    }

    public class FlexCardPakageOutput
    {
        public string Code { get; set; }
        public string Branch { get; set; }
        public string Content { get; set; }
        public string Note { get; set; }
        public decimal? MaxCoinInCycle { get; set; }
        public List<FlexCardServiceOutput> Services { get; set; }
    }

    public class FlexCardServiceOutput
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Content { get; set; }
    }

    public class SchemaFlexCardOutput
    {
        public int Id { get; set; }
        public string Cif { get; set; }
        public string ContractNumber { get; set; }
        public string CardNumberMasked { get; set; }
        public string SchemaCode { get; set; }
        public string Status { get; set; }
        public DateTime? CreationTime { get; set; }
        public int? CreatorUserId { get; set; }
        public DateTime? LastModificationTime { get; set; }
    }

    public class SchemaFlexCardHistoryOutput
    {
        public int Id { get; set; }
        public string Cif { get; set; }
        public string ContractNumber { get; set; }
        public string CardNumberMasked { get; set; }
        public string SchemaCode { get; set; }
        public string Status { get; set; }
        public DateTime? CreationTime { get; set; }
        public int? CreatorUserId { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public DateTime? ActiveTimeFrom { get; set; }
        public DateTime? ActiveTimeTo { get; set; }
        public FlexCardPakageOutput pakageInfor { get; set; }
    }

    public class SchemaFlexCardCreateOrUpdateOutput
    {
        public bool success { get; set; }
        public string errorMsg { get; set; }
        public SchemaFlexCard data { get; set; }
    }

    public class SchemaFlexCard
    {
        public int Id { get; set; }
        public int? TenantId { get; set; }
        public string Cif { get; set; }
        public string ContractNumber { get; set; }
        public string CardNumberMasked { get; set; }
        public string SchemaCode { get; set; }
        public string Status { get; set; }
        public DateTime CreationTime { get; set; }
        public int? CreatorUserId { get; set; }
        public DateTime? LastModificationTime { get; set; }
    }
}
