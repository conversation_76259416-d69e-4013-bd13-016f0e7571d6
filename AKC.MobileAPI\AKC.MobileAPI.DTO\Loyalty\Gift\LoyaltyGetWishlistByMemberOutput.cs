﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{


    public class LoyaltyGetWishlistByMemberOutput
    {
        public GetWishlistByMemberResultData Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class LoyaltyGetWishlistByMemberOutputForView
    {
        public GiftShortInforDto WishListItem { get; set; }
        public List<ImageLinkDto> ImageLink { get; set; }
        public List<GiftShortInforForView> RelatedGifts { get; set; }
    }

    public class GetWishlistByMemberResultData
    {
        public int TotalCount { get; set; }

        public List<LoyaltyGetWishlistByMemberOutputForView> Items { get; set; }
    }
}
