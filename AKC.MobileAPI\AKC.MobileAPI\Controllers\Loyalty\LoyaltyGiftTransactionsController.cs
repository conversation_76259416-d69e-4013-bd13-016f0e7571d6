﻿using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.LoyaltyVendorGift;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/GiftTransactions")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyGiftTransactionsController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILinkIdLoyaltyVendorGiftService _giftTransactionsService;
        private readonly IExceptionReponseService _exceptionReponseService;
        public LoyaltyGiftTransactionsController(
            ILogger<LoyaltyGiftTransactionsController> logger,
            ILinkIdLoyaltyVendorGiftService giftTransactionsService,
            IExceptionReponseService exceptionReponseService)
        {
            _logger = logger;
            _giftTransactionsService = giftTransactionsService;
            _exceptionReponseService = exceptionReponseService;
        }

        [HttpPost]
        [Route("GiftTransfer")]
        public async Task<ActionResult<GiftTransferOutput>> GiftTransfer(UpdateGiftTransactionsForTransferInput input)
        {
            try
            {
                var result = await _giftTransactionsService.GiftTransfer(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GiftTransfer GiftTransactions Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("UserVerifying")]
        public async Task<ActionResult<GetMemberCodeOutput>> UserVerifying(GetMemberCodeInput input)
        {
            try
            {
                var result = await _giftTransactionsService.UserVerifying(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "UserVerifying GiftTransactions Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
    }
}

