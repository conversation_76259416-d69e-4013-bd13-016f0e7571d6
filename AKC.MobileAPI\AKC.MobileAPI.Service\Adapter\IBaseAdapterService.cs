﻿using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.CronServices;
using AKC.MobileAPI.Service.Exceptions;
using Cronos;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace AKC.MobileAPI.Service.Loyalty
{
    public interface IBaseAdapterService
    {
        Task<T> GetAdapterAsync<T>(string apiURL, object query = null);
        Task<T> DeleteAdapterAsync<T>(string apiURL, object query = null);
        Task<T> PostAdapterAsync<T>(string apiURL, object body = null, HttpContext request = null);
        Task<T> PutAdapterAsync<T>(string apiURL, object body = null);
    }
}
