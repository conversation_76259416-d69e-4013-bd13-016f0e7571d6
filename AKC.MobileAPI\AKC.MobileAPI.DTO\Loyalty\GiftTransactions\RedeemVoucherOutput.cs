﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.GiftTransactions
{
    public class RedeemVoucherOutput
    {
        public RedeemVoucherOutputResult Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class RedeemVoucherOutputResult
    {
        public string ErrorCode { get; set; }
        public string ErrorMessage { get; set; }
        public string TransactionCode { get; set; }
        public string ExpireDate { get; set; }
        public int? Count { get; set; }
        public decimal? TopupAmount { get; set; }
    }

    public class RevertVoucherOutput
    {
        public RevertVoucherOutputResult Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }
    public class RevertVoucherOutputResult
    {
        public int ErrorCode { get; set; }
        public string ErrorMessage { get; set; }
        public int? Count { get; set; }
    }
}
