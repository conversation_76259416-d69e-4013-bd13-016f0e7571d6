﻿using AKC.MobileAPI.Service.Abstract;
using AKC.MobileAPI.Service.Loyalty;
using Cronos;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.CronServices
{


    public class RefreshLoyaltyTokenJob : CronJobService
    {
        private readonly ILogger<RefreshLoyaltyTokenJob> _logger;
        private readonly IDistributedCache _cache;

        public RefreshLoyaltyTokenJob(IScheduleConfig<RefreshLoyaltyTokenJob> config,
            ILogger<RefreshLoyaltyTokenJob> logger,
            IDistributedCache cache) : base(config.CronExpression, config.TimeZoneInfo)
        {
            _logger = logger;
            _cache = cache;
        }

        public override Task StartAsync(CancellationToken cancellationToken)
        {
            var delayToNextRun = CronHelper.GetDelayToNextRefreshToken(_expression, _timeZoneInfo);
            //var delayToNextRunDummy = CronHelper.GetDelayToNextRefreshToken(_expression, _timeZoneInfo);

            // Get token after run app.
            LoyaltyHelper.RenewAccessTokenCacheValue(_cache, delayToNextRun);
            //LoyaltyHelper.RenewAccessTokenDummyCacheValue(_cache, delayToNextRunDummy);
            return base.StartAsync(cancellationToken);
        }

        public override Task DoWork(CancellationToken cancellationToken)
        {
            try
            {
                var delayToNextRun = CronHelper.GetDelayToNextRefreshToken(_expression, _timeZoneInfo);
                //var delayToNextRunDummy = CronHelper.GetDelayToNextRefreshToken(_expression, _timeZoneInfo);
                // Try renew access token.
                LoyaltyHelper.RenewAccessTokenCacheValue(_cache, delayToNextRun);
                //LoyaltyHelper.RenewAccessTokenDummyCacheValue(_cache, delayToNextRunDummy);
                _logger.LogInformation("RenewAccessToken Job run successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError("Job: RenewAccessToken error" + JsonConvert.SerializeObject(ex));
            }

            return Task.CompletedTask;
        }

        public override Task StopAsync(CancellationToken cancellationToken)
        {
            return base.StopAsync(cancellationToken);
        }
    }
}
