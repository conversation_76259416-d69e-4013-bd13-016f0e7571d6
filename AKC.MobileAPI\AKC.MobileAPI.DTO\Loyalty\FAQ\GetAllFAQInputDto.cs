﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AKC.MobileAPI.DTO.Loyalty.FAQ
{
    public class GetAllFAQInputDto
    {
		public string Filter { get; set; }

		public string CodeFilter { get; set; }

		public string QuestionFilter { get; set; }

		public string AnswerFilter { get; set; }

		public int? MaxOrdinalFilter { get; set; }

		public int? MinOrdinalFilter { get; set; }

		public string Sorting { get; set; }

		public int SkipCount { get; set; }

		public int MaxResultCount { get; set; }
	}

}
