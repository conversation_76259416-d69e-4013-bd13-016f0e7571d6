﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace AKC.MobileAPI.DTO.Loyalty.GiftTransactions
{
    public class GetAllGiftTransactionByGiftCategoryChannelResponse
    {
        public int TotalCount { get; set; }
        public List<GetAllGiftTransactionByGiftCategoryChannelData> Items { get; set; }
    }

    public class GetAllGiftTransactionByGiftCategoryChannelData
    {
        public long Id { get; set; }
        public string Code { get; set; }
        public string TransactionCode { get; set; }
        public string GiftName { get; set; }
        public string GiftCode { get; set; }
        public DateTime CreationTime { get; set; }
        public string EgiftStatus { get; set; }
        public string Status { get; set; }
        public string ProcessingErrorMessage { get; set; }
        public int? ProcessingNumberRetry { get; set; }
        public string Cif { get; set; }
        public string PhoneNumber { get; set; }
        public string Email { get; set; }
        [JsonIgnore]
        public string LinkID_PhoneNumber { get; set; }
        public DateTime? EgiftExpiredDate { get; set; }
    }
}
