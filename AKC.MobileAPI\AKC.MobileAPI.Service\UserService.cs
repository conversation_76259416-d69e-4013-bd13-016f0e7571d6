﻿using AKC.MobileAPI.DTO.ApiSMS;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.Service.Abstract;
using AKC.MobileAPI.Service.Abstract.ApiSMS;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Loyalty;
using AKC.MobileAPI.Service.Reward;
using AspNetCore.Totp;
using AspNetCore.Totp.Interface;
using FirebaseAdmin.Auth;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service
{
    /// <summary>
    /// This class using for manager user in firebase and loyalty system.
    /// </summary>
    public class UserService : RewardBaseService, IUserService
    {
        private readonly ITotpGenerator totpGenerator;
        private readonly ITotpValidator totpValidator;
        private readonly IApiSMSService _apiSMSService;
        private readonly ILoyaltySecondaryCustomersService _loyaltySecondaryCustomersService;
        private readonly IRewardMemberService _rewardMemberService;
        private readonly IExceptionReponseService _exceptionReponseService;


        public UserService(
            IConfiguration configuration,
            IApiSMSService apiSMSService,
            ILoyaltySecondaryCustomersService loyaltySecondaryCustomersService,
            IRewardMemberService rewardMemberService,
            IExceptionReponseService exceptionReponseService)
            : base(configuration)
        {
            totpGenerator = new TotpGenerator();
            totpValidator = new TotpValidator(totpGenerator);
            _apiSMSService = apiSMSService;
            _loyaltySecondaryCustomersService = loyaltySecondaryCustomersService;
            _rewardMemberService = rewardMemberService;
            _exceptionReponseService = exceptionReponseService;
        }

        //public async Task<LoyaltyMemberDTO> CreateUser(CreateUserDTO createUserDTO)
        //{
        //    //// Create user loyal ty.
        //    var result = await CreateLoyaltyMemberAsync(createUserDTO);

        //    // Create user in firebase.
        //    await CreateFirebaseUserAsync(createUserDTO.Email, createUserDTO.Password, createUserDTO.Phone);

        //    return result;
        //}

        //public async Task<ResponseApiSMSModel> GetOTP(ValidateUserInput createUserDTO)
        //{
        //    string numberCode = totpGenerator.Generate(createUserDTO.PhoneNumber).ToString();
        //    while (numberCode.Length != 6)
        //    {
        //        numberCode = "0" + numberCode;
        //    }

        //    string brand_name = _configuration.GetSection("SMS:brand_name").Value;

        //    var listSMS = new List<SMSModel>();
        //    listSMS.Add(new SMSModel()
        //    {
        //        id = Guid.NewGuid().ToString(),
        //        brandname = brand_name,
        //        text = "Your OTP code is: " + numberCode,
        //        to = createUserDTO.PhoneNumber
        //    });

        //    return await _apiSMSService.SendSMS(listSMS);
        //}

        public async Task<GetOTPOutput> GetOTP(ValidateUserInput createUserDTO)
        {
            var result =  new ResponseApiSMSModel()
            {
                response = new ResponseSubmissionModel()
                {
                    submission = new List<ResponseSMSModel>()
                    {
                        new ResponseSMSModel()
                        {
                            id = "200",
                            status = "1",
                            error_message = null
                        }
                    }
                }
            };
            if (result.response.submission[0].status == "1")
            {
                return new GetOTPOutput()
                {
                    Status = 2,
                    Message = "Success!"
                };
            }
            else
            {
                return new GetOTPOutput()
                {
                    Status = 1,
                    Message = "Send SMS fail!"
                };
            }
        }

        public async Task<ValidateUserOutput> IsExisted(ValidateUserInput input)
        {
            var result = new ValidateUserOutput() { PhoneNumber = input.PhoneNumber };

            try
            {
                var auth = FirebaseAuth.DefaultInstance;
                var phone = input.PhoneNumber;
                var userRecord = await auth.GetUserByPhoneNumberAsync(phone);
                result.IsExisted = true;
            }
            catch (Exception ex)
            {
                result.IsExisted = false;
                result.HasPinCode = false;
				result.IsLocked = false;
                return result;
            }

            try
            {
                var resultReward = await _rewardMemberService.HasPinCode(new RewardMemberHasPinCodeRequest()
                {
                    PhoneNumber = input.PhoneNumber
                });
                result.HasPinCode = resultReward.HasPinCode;
				result.IsLocked = resultReward.IsLocked;
                result.MemberCode = resultReward.MemberCode;
            }
            catch (Exception ex)
            {
                result.HasPinCode = false;
                result.IsLocked = false;
            }

            return result;
        }

        public async Task<VerifyOTPOutput> VerifyOTPAndLogin(SendOTPUserInput input)
        {
            //int otp = 0;
            //int.TryParse(input.OTP, out otp);

            //bool flag = totpValidator.IsValid(input.PhoneNumber, otp);

            bool flag = input.OTP == "111111" ? true : false;

            var output = new VerifyOTPOutput()
            {
                Status = flag ? StatusReturnConst.Success : StatusReturnConst.IncorrectPinCode,
                CustomToken = null
            };

            if (flag)
            {
                try
                {
                    var auth = FirebaseAuth.DefaultInstance;
                    var user = await auth.GetUserAsync("Yl4OZJQpVwe42ft847a4pA5vFbG3");

                    //await auth.RevokeRefreshTokensAsync(user.Uid);
                    await auth.UpdateUserAsync(new UserRecordArgs()
                    {
                        PhoneNumber = "+84969156281",
                        Uid = user.Uid
                    });

                    await auth.UpdateUserAsync(new UserRecordArgs()
                    {
                        PhoneNumber = "+84969156281",
                        Uid = user.Uid,
                        Email = "<EMAIL>"
                    });

                    var customToken = await auth.CreateCustomTokenAsync(user.Uid);

                    output.CustomToken = customToken;
                    output.Message = "Success!";
                }
                catch (Exception ex)
                {
                    output.Status = StatusReturnConst.Error;
                    output.Message = ex.Message;
                }

                //double timeNow = DateTimeToUnixTimestamp();
                //Dictionary<string, object> additionalClaims = new Dictionary<string, object>();
                //additionalClaims.Add("alg", "RS256");
                //additionalClaims.Add("uid", user.Uid);
                //additionalClaims.Add("iat", timeNow);
                //additionalClaims.Add("exp", timeNow + 3590);
                //return await auth.CreateCustomTokenAsync(user.Uid, additionalClaims);
            }
            else
            {
                output.Message = "Incorrect OTP Code!";
            }

            return output;
        }

        public async Task<VerifyOTPOutput> VerifyOTPAndRegister(CreateUserMobileDTO input)
        {
            //int otp = 0;
            //int.TryParse(input.OTP, out otp);

            //bool flag = totpValidator.IsValid(input.PhoneNumber, otp);

            bool flag = input.OTP == "111111" ? true : false;

            var output = new VerifyOTPOutput()
            {
                Status = flag ? StatusReturnConst.Success : StatusReturnConst.IncorrectPinCode,
                CustomToken = null
            };

            if (flag)
            {
                var auth = FirebaseAuth.DefaultInstance;
                UserRecord user = null;
                try
                {
                    user = await auth.CreateUserAsync(new UserRecordArgs()
                    {
                        PhoneNumber = input.Phone
                    });
                }
                catch (Exception ex)
                {
                    output.Status = StatusReturnConst.Error;
                    output.Message = ex.Message;
                    return output;
                }
                var memberCode = LoyaltyHelper.GenMemberCode(input.Phone);
                try
                {
                    var resultcreatemember = new RewardMemberCreateOutput();
                    var memberReward = await _rewardMemberService.VerifyProviderIdByPhoneNumber(
                         new VerifyProviderIdByPhoneNumberRequest()
                         {
                             ProviderName = "Firebase",
                             ProviderId = user.Uid,
                             PhoneNumber = input.Phone
                         }
                     );
                    memberCode = memberReward.Items.MemberCode;
                    // If cannot member at loyalty then create member, else create return member code
                    if (!memberReward.Items.MemberExist)
                    {
                        try
                        {
                            var res = await VerifyOrCreateMember(input, user.Uid);
                            memberCode = res.Result.MemberCode;
                        } catch (Exception exCreate)
                        {
                            var res = await _exceptionReponseService.GetExceptionRewardReponse(exCreate);
                            await auth.DeleteUserAsync(user.Uid);
                            output.Status = StatusReturnConst.Error;
                            output.Message = JsonConvert.SerializeObject(res.MessageDetail);
                            return output;
                        }
                    }
                    else
                    {
                        var updateProvider = new VerifyProviderIdByPhoneNumberRequest()
                        {
                            ProviderName = "Firebase",
                            ProviderId = user.Uid,
                            PhoneNumber = input.Phone
                        };
                        await _rewardMemberService.UpdateProvider(updateProvider);
                    }
                }
                catch (Exception ex)
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);

                    await auth.DeleteUserAsync(user.Uid);
                    output.Status = StatusReturnConst.Error;
                    output.Message = JsonConvert.SerializeObject(res.MessageDetail);
                    return output;
                }

                var customToken = await auth.CreateCustomTokenAsync(user.Uid);

                output.CustomToken = customToken;
                output.MemberCode = memberCode;
                output.Message = "Success!";

                try
                {
                    await _loyaltySecondaryCustomersService.VerifyReferralCode(new LoyaltyVerifyReferralCodeInput()
                    {
                        NationalId = user.Uid,
                        ReferralCode = input.ReferralCode,
                        DistributionChannelList = null,
                        ReferenceAmount = 0
                    });
                }
                catch
                {

                }

                //double timeNow = DateTimeToUnixTimestamp();
                //Dictionary<string, object> additionalClaims = new Dictionary<string, object>();
                //additionalClaims.Add("alg", "RS256");
                //additionalClaims.Add("uid", user.Uid);
                //additionalClaims.Add("iat", timeNow);
                //additionalClaims.Add("exp", timeNow + 3590);
                //return await auth.CreateCustomTokenAsync(user.Uid, additionalClaims);
            }
            else
            {
                output.Message = "Incorrect OTP Code";
            }

            return output;
        }

        /// <summary>
        /// Create new User in firebase.
        /// </summary>
        /// <param name="email"></param>
        /// <param name="password"></param>
        /// <param name="phone"></param>
        /// <returns></returns>
        private async Task CreateFirebaseUserAsync(string email, string password, string phone = null)
        {
            var auth = FirebaseAuth.DefaultInstance;
            await auth.CreateUserAsync(new UserRecordArgs()
            {
                Email = email,
                Password = password,
                PhoneNumber = phone,
                EmailVerified = true,
            });
        }

        //private async Task<LoyaltyMemberDTO> CreateLoyaltyMemberAsync(CreateUserDTO createUserDTO)
        //{
        //    var createLoyaltyMemberDTO = new LoyaltyMemberDTO()
        //    {
        //        TenantId = tenantId,
        //        Code = createUserDTO.IdCard,
        //        Type = "Member",
        //        Phone = createUserDTO.Phone,
        //        Gender = createUserDTO.Gender,
        //        Status = "A",
        //        RankTypeCode = "Customer",
        //        FirstName = createUserDTO.FirstName,
        //        LastName = createUserDTO.LastName
        //    };
        //    await PostLoyaltyAsync<object>(LoyaltyApiUrl.CREATE_SECONDARY_CUSTOMERS, createLoyaltyMemberDTO);

        //    return createLoyaltyMemberDTO;
        //}

        public async Task<RewardMemberCreateOutput> CreateMember(CreateUserMobileDTO input, string nationalId)
        {
            var createMemberDto = new RewardMemberCreateDto()
            {
                NationalId = nationalId,
                IdCard = input.IdCard,
                PartnerPhoneNumber = null,
                Type = "Member",
                Phone = input.Phone,
                Gender = "O",
                Status = "A",
                RankTypeCode = "Customer",
                IsDeleted = false,
                Address = "",
                Dob = DateTime.UtcNow,
                Name = "",
                Email = "",
                PointUsingOrdinary = "",
                HashAddress = "",
                D365Id = nationalId,
                MbcId = nationalId,
                MbcCardId = nationalId,
                RegionCode = "",
                FullRegionCode = "",
                MemberTypeCode = "",
                FullMemberTypeCode = "",
                ChannelType = "",
                FullChannelTypeCode = "",
                StandardMemberCode = nationalId,
                ReferralCode = GenReferralCode(input.Phone),
                Avatar = "",
            };

            return await PostRewardAsync<RewardMemberCreateOutput>(RewardApiUrl.MEMBER_CREATE, createMemberDto);
        }

        public async Task<RewardMemberVerifyOrCreateOutput> VerifyOrCreateMember(CreateUserMobileDTO input, string firebaseId)
        {
            var createMemberDto = new RewardMemberVerifyOrCreateDto()
            {
                NationalId = firebaseId,
                IdCard = input.IdCard,
                PartnerPhoneNumber = null,
                Type = "Member",
                Phone = input.Phone,
                Gender = "O",
                Status = "A",
                RankTypeCode = "Customer",
                IsDeleted = false,
                Address = "",
                Dob = DateTime.UtcNow,
                Name = "",
                Email = "",
                PointUsingOrdinary = "",
                HashAddress = "",
                RegionCode = "",
                FullRegionCode = "",
                MemberTypeCode = "",
                FullMemberTypeCode = "",
                ChannelType = "",
                FullChannelTypeCode = "",
                StandardMemberCode = firebaseId,
                ReferralCode = GenReferralCode(input.Phone),
                Avatar = "",
                FirebaseId = firebaseId,
            };

            var res = await PostRewardAsync<RewardMemberVerifyOrCreateOutputDto>(RewardApiUrl.MEMBER_VERIFY_OR_CREATE, createMemberDto);
            return new RewardMemberVerifyOrCreateOutput()
            {
                Messages = "Success",
                Result = new RewardMemberVerifyOrCreateOutputItem()
                {
                    MemberCode = res.Result.NationalId,
                    PhoneNumber = res.Result.Phone,
                },
                Status = 200,
            };
        }

        //private static double DateTimeToUnixTimestamp()
        //{
        //    return (DateTime.UtcNow -
        //           new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)).TotalSeconds;
        //}

        private string GenReferralCode(string phoneNumber)
        {
            if (!string.IsNullOrEmpty(phoneNumber))
            {
                return phoneNumber.Replace("+84", "0");
            }
            return null;
        }
    }
}
