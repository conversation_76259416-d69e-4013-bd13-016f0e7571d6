﻿using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.Service.Abstract.Loyalty.Gift;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty.Gift
{
    public class LoyaltyGiftService: BaseLoyaltyService, ILoyaltyGiftService
    {
        public LoyaltyGiftService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<LoyaltyGiftCategoryGetAllOutput> GetAll(LoyaltyGiftCategoryGetAllInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGiftCategoryGetAllOutput>(LoyaltyApiUrl.GETALL_GIFT_CATEGORY, input);
        }

        public async Task<LoyaltyGetAllForCategoryOutPut> GetAllForCategory(LoyaltyGetAllForCategoryInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGetAllForCategoryOutPut>(LoyaltyApiUrl.GIFT_INFORS_GETAll_FOR_CATEGORY, input);
        }

        public async Task<LoyaltyGiftGetAllWithImageOutput> GetAllWithImage(LoyaltyGiftGetAllWithImageInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGiftGetAllWithImageOutput>(LoyaltyApiUrl.GIFT_GROUPS_GETAll_WITH_IMAGE, input);
        }

        public async Task<LoyaltyGiftGetAllWithImageOutput> GetAllWithImageByMemberCode(LoyaltyGetAllGiftGroupByMemberInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGiftGetAllWithImageOutput>(LoyaltyApiUrl.GIFT_GROUPS_GETAll_WITH_IMAGE_BY_MEMBER, input);
        }

        public async Task<LoyaltyGiftGetAllByMemberCodeOutput> GetAllByMemberCode(LoyaltyGiftGetAllByMemberCodeInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGiftGetAllByMemberCodeOutput>(LoyaltyApiUrl.GIFT_INFORS_GETAll_MEMBER_CODE, input);
        }

        public async Task<LoyaltyGetGiftByByMemberCodeOutput> GetGiftByMemberCode(LoyaltyGetGiftByByMemberCodeInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGetGiftByByMemberCodeOutput>(LoyaltyApiUrl.GIFT_INFORS_GET_GIFT_MEMBER_CODE, input);
        }
        
        public async Task<GetGiftCategoryAndInfoForView> GetAllGiftCategoriesAndInfo(GetAllGiftCategoriesAndInfoInput input)
        {
            return await GetLoyaltyAsync<GetGiftCategoryAndInfoForView>(LoyaltyApiUrl.GIFT_INFORS_GET_GIFT_CATEGORIES_MEMBER_CODE, input);
        }
        public async Task<GetGiftCategoryAndInfoForView> GetAllGiftCategoriesAndInfo_V1(GetAllGiftCategoriesAndInfoInput input)
        {
            return await GetLoyaltyAsync<GetGiftCategoryAndInfoForView>(LoyaltyApiUrl.GIFT_INFORS_GET_GIFT_CATEGORIES_MEMBER_CODE_V1, input);
        }

        public async Task<GiftInforsOutPut> GetAllInfors(GiftInforsInput input)
        {
            return await GetLoyaltyAsync<GiftInforsOutPut>(LoyaltyApiUrl.GIFT_INFORS_GET_GIFT_ALL_INFORS_MEMBER_CODE, input);
        }

        public async Task<GetByIdAndRelatedGiftOutput> GetByIdAndRelatedGift(GetByIdAndRelatedGiftInput input)
        {
            return await GetLoyaltyAsync<GetByIdAndRelatedGiftOutput>(LoyaltyApiUrl.GIFT_INFORS_GET_GIFT_BY_ID_AND_RELATED_GIFT_MEMBER_CODE, input);
        }
        
        public async Task<GetAllEffectiveCategoryOutput> GetAllEffectiveCategory(GetAllEffectiveCategoryInput input)
        {
            return await GetLoyaltyAsync<GetAllEffectiveCategoryOutput>(LoyaltyApiUrl.GIFT_INFORS_GET_ALL_EFFECTIVE_CATEGORY, input);
        }
        public async Task<GetAllEffectiveCategoryOutput> GetAllEffectiveCategory_v1(GetAllEffectiveCategoryInput input)
        {
            return await GetLoyaltyAsync<GetAllEffectiveCategoryOutput>(LoyaltyApiUrl.GIFT_INFORS_GET_ALL_EFFECTIVE_CATEGORY_V1, input);
        }

        public async Task<LoyaltyGetWishlistByMemberOutput> GetWishlistByMember(LoyaltyGetWishlistByMemberInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGetWishlistByMemberOutput>(LoyaltyApiUrl.GIFT_INFORS_WISH_LIST_BY_MEMBER, input);
        }

        public async Task<LoyaltyUpdateWishlistOutput> UpdateWishlist(LoyaltyUpdateWishlistInput input)
        {
            return await PutLoyaltyAsync<LoyaltyUpdateWishlistOutput>(LoyaltyApiUrl.GIFT_INFORS_UPDATE_WISH_LIST, input);
        }

        public async Task<GetAllByMemberCodeOutput> GetAllByMemberCode(GetAllByMemberCodeGiftCategoriesInput input)
        {
            return await GetLoyaltyAsync<GetAllByMemberCodeOutput>(LoyaltyApiUrl.GIFT_INFORS_GET_ALL_BY_MEMBER_CODE, input);
        }

        public async Task<GetAllForCategoryByMemberCodeOutput> GetAllForCategoryByMemberCode(GetAllByMemberCodeGiftInforsInput input)
        {
            return await GetLoyaltyAsync<GetAllForCategoryByMemberCodeOutput>(LoyaltyApiUrl.GIFT_INFORS_GET_ALL_FOR_CATEGORY_BY_MEMBER_CODE, input);
        }
    }
}
