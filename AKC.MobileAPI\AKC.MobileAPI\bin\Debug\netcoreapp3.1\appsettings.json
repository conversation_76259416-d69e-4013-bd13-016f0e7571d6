{"AppSettings": {"EncryptionKey": "6193c428e471b5d308ccf56ad4819410", "Value": "****************"}, "Loyalty": {"RemoteURL": "http://***********:31999/api", "TenantId": "27", "Username": "admin", "Password": "Langha@2021", "CronExpressionRefreshToken": "*/59 * * * *"}, "Reward": {"URL": "https://vpid-operator-api-uat.linkid.vn", "MerchantId": "219", "AccessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************.c5cegrhfroSMnVI-5sg2P4g_MUoa6CL4Cyroav7YGK4"}, "RewardVPBank": {"URL": "https://vpid-operator-api-uat.linkid.vn", "MerchantId": "224", "AccessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************.ZGx9cYeAfSO-EgRWrWEcc8AF1Q1bg0J6DoVGubio08U"}, "MasterCardDataConfig": {"GiftGroupChannelId": "9", "GiftGroupSubType": "CardZone", "RedeemSource": "Mastercard"}, "RewardMasterCard": {"RemoteURL": "https://operator-api-pre.linkid.vn", "MerchantId": "252", "AccessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************.Zg1Zp74c_ZVo_zQV47AJcJmzGf5JTR3QHzGr4WhYSEc"}, "AdapterMasterCardAPI": {"RemoteURL": "http://***********:32015/api", "Username": "loyalty-api", "Password": "********", "CronExpressionRefreshToken": "*/59 * * * *"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "ThirdPartyMerchant": {"LoyaltyDummy": {"MerchantId": 158}}, "StoreFiles": {"AWSS3": {"AccessKey": "", "SecretKey": "", "BucketName": "", "KeyName": "upload-member-exchange/member.txt"}}, "EnableSwagger": "true", "IsCheckPhoneNumberExchangeList": "false", "LoyaltyLinkID": {"RemoteURL": "https://vpid-loyalty-api-uat.linkid.vn/api", "TenantId": "26", "Username": "admin", "Password": "1qaZ2wsX3edC", "CronExpressionRefreshToken": "* * */10 * *", "MerchantIdRedeem": "178", "LINKID_DefaultMemberId_ToSendGift": "2662"}, "RewardVPID": {"RemoteURL": "https://vpid-merchant-api-qa.akachains.io", "MerchantId": "178", "AccessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************.IBmqtFiUKR56EQSgxaC1Gkx6KO993ejrpTMzLiofZA4"}, "GIFT_PRICE_THAT_NEEDS_SMS_VERIFICATION": 2000000, "VPBankLoyaltyAPIUtils": {"BaseURL": "http://***********:32110/api", "TenantId": "27", "Username": "thedv1", "Password": "AQAAAAEAACcQAAAAEHnBEmY7H3XbnlDdQ0n6GeVouZ7Wc7oxl2kZhTzL0JdWJ409U/c444iNnWQHzsCpOg==", "CronExpressionRefreshToken": "* * */10 * *"}, "SmeUtilApi": {"BaseURL": "http://***********:32110/api", "TenantId": "28", "Username": "admin", "Password": "AQAAAAEAACcQAAAAEBomocr8enZ0ty3QOVHquQFnwwD4pfq/pl8k+5fOG8atYs4tWKvUroe78cNA1nUkng==", "CronExpressionRefreshToken": "* * */10 * *", "MerchantId": "218"}, "NEOBiz": {"AppSettings": {"EncryptionKey": "6o2pwxGM21YA6xpSk1eSHurestn3oppr", "Value": "L5mL5OqQ0ZP52qR0"}}, "Redis": {"InstanceName": "vporg", "Configuration": "localhost:6379,abortConnect=False,allowAdmin=true"}, "RewardChallenge": {"AccessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************.Zg1Zp74c_ZVo_zQV47AJcJmzGf5JTR3QHzGr4WhYSEc", "MerchantId": 303, "RemoteURL": "https://vpid-operator-api-uat.linkid.vn"}}