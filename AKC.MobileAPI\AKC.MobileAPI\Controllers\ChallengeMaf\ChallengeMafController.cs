using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Const;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.ChallengeMaf;
using AKC.MobileAPI.DTO.Loyalty.MasterCard;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.LoyaltyVendorGift;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using AKC.MobileAPI.Service.Loyalty;
using AKC.MobileAPI.Service.LoyaltyVendorGift;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/challengemaf")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class ChallengeMafController : ControllerBase
    {
        private IChallengeMafService _challengeMafService;
        private ILinkIdLoyaltyVendorGiftService _linkIdLoyaltyVendorGiftService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILogger _logger;
        public ChallengeMafController(
            IChallengeMafService challengeMafService,
            ILinkIdLoyaltyVendorGiftService linkIdLoyaltyVendorGiftService,
            ILogger<ChallengeMafController> logger,
            IExceptionReponseService exceptionReponseService)
        {
            _challengeMafService = challengeMafService;
            _exceptionReponseService = exceptionReponseService;
            _linkIdLoyaltyVendorGiftService = linkIdLoyaltyVendorGiftService;
            _logger = logger;
        }
        [HttpGet]
        [Route("GetGiftByGroupChannel")]
        public async Task<ActionResult<LynkiDResponseList<LynkiDGiftOutput>>> GetGiftByGroupChannel(string MemberCode, int ChannelId, string ReferenceCode, string GiftGroupCode, string Lang, int SkipCount, int MaxResultCount)
        {
            try
            {
                var input = new GetGiftByGroupChannelInput()
                {
                    ChannelId = ChannelId,
                    GiftGroupCode = GiftGroupCode,
                    Lang = Lang,
                    MaxResultCount = MaxResultCount,
                    MemberCode = MemberCode,
                    ReferenceCode = ReferenceCode,
                    SkipCount = SkipCount
                };
                _logger.LogInformation($" >> GetGiftByGroupChannel >> {JsonConvert.SerializeObject(input)}");
                var output = await _challengeMafService.GetGiftByGroupChannel(input);
                _logger.LogInformation($" >> GetGiftByGroupChannel Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ChallengeMaf GetGiftByGroupChannel Error - " + JsonConvert.SerializeObject(ex));
                var HttpStatusCode = ex.Data != null && ex.Data["StatusCode"] != null && string.IsNullOrEmpty(ex.Data["StatusCode"].ToString()) ? Int32.Parse(ex.Data["StatusCode"].ToString()) : 500;
                return StatusCode(HttpStatusCode, JsonConvert.DeserializeObject<LynkiDResponseExp>(ex.Data["ErrorData"].ToString()));
            }
        }
        [HttpPost]
        [Route("ValidGiftByBrand")]
        public async Task<ActionResult<LynkiDResponse<bool>>> ValidGiftByBrand(ValidGiftByBrandInput input)
        {
            try
            {
                _logger.LogInformation($" >> ValidGiftByBrand >> {JsonConvert.SerializeObject(input)}");
                var output = await _challengeMafService.ValidGiftByBrand(input);
                _logger.LogInformation($" >> ValidGiftByBrand Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ChallengeMaf ValidGiftByBrand Error - " + JsonConvert.SerializeObject(ex));
                var HttpStatusCode = ex.Data != null && ex.Data["StatusCode"] != null && string.IsNullOrEmpty(ex.Data["StatusCode"].ToString()) ? Int32.Parse(ex.Data["StatusCode"].ToString()) : 500;
                return StatusCode(HttpStatusCode, JsonConvert.DeserializeObject<LynkiDResponseExp>(ex.Data["ErrorData"].ToString()));
            }
        }
        [HttpPost]
        [Route("VerifyAndCreateRedeemOrder")]
        public async Task<ActionResult<LynkiDResponse<CreateRedeemMafGiftOrderResponse>>> VerifyAndCreateRedeemOrder(VerifyAndCreateRedeemOrderInput input)
        {
            try
            {
                _logger.LogInformation($" >> VerifyAndCreateRedeemOrder >> Get MerchantId infor from GiftCode");
                try
                {
                    var merchantIdFromGiftCode = await _linkIdLoyaltyVendorGiftService.GetMerchantIdFromGiftCode(new LoyaltyGiftGetMerchantIdFromGiftCodeInput()
                    {
                        GiftCode = input.giftCode
                    });
                    if (merchantIdFromGiftCode == null || !merchantIdFromGiftCode.Success || merchantIdFromGiftCode.Result == null || !merchantIdFromGiftCode.Result.MerchantId.HasValue)
                    {
                        return StatusCode(500, new LynkiDResponseExp()
                        {
                            error = new ErrorExpObject()
                            {
                                code = 3001,
                                message = "Cannot find merchant"
                            },
                            success = false,
                            result = "Cannot find merchant"
                        });
                    }
                    input.merchantIdRedeem = merchantIdFromGiftCode.Result.MerchantId.Value;
                }
                catch (Exception)
                {
                    return StatusCode(500, new LynkiDResponseExp()
                    {
                        error = new ErrorExpObject()
                        {
                            code = 3001,
                            message = "Cannot find merchant"
                        },
                        success = false,
                        result = "Cannot find merchant"
                    });
                }
                _logger.LogInformation($" >> VerifyAndCreateRedeemOrder >> {JsonConvert.SerializeObject(input)}");
                var output = await _challengeMafService.VerifyAndCreateRedeemOrder(input);
                _logger.LogInformation($" >> VerifyAndCreateRedeemOrder Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ChallengeMaf VerifyAndCreateRedeemOrder Error - " + JsonConvert.SerializeObject(ex));
                var HttpStatusCode = ex.Data != null && ex.Data["StatusCode"] != null && string.IsNullOrEmpty(ex.Data["StatusCode"].ToString()) ? Int32.Parse(ex.Data["StatusCode"].ToString()) : 500;
                return StatusCode(HttpStatusCode, JsonConvert.DeserializeObject<LynkiDResponseExp>(ex.Data["ErrorData"].ToString()));
            }
        }
        [HttpPost]
        [Route("CreateRedeemMultiTxWithGroupCode")]
        public async Task<ActionResult<LynkiDResponse<RedeemMultiMafGiftResponse>>> CreateRedeemMultiTxWithGroupCode(CreateRedeemMultiTxWithGroupCodeInput input)
        {
            try
            {
                _logger.LogInformation($" >> CreateRedeemMultiTxWithGroupCode >> Get MerchantId infor from GiftCode");
                try
                {
                    var merchantIdFromGiftCode = await _linkIdLoyaltyVendorGiftService.GetMerchantIdFromGiftCode(new LoyaltyGiftGetMerchantIdFromGiftCodeInput()
                    {
                        GiftCode = input.giftCode
                    });
                    if (merchantIdFromGiftCode == null || !merchantIdFromGiftCode.Success || merchantIdFromGiftCode.Result == null || !merchantIdFromGiftCode.Result.MerchantId.HasValue)
                    {
                        return StatusCode(500, new LynkiDResponseExp()
                        {
                            error = new ErrorExpObject()
                            {
                                code = 3001,
                                message = "Cannot find merchant"
                            },
                            success = false,
                            result = "Cannot find merchant"
                        });
                    }
                    input.merchantIdRedeem = merchantIdFromGiftCode.Result.MerchantId.Value;
                }
                catch (Exception)
                {
                    return StatusCode(500, new LynkiDResponseExp()
                    {
                        error = new ErrorExpObject()
                        {
                            code = 3001,
                            message = "Cannot find merchant"
                        },
                        success = false,
                        result = "Cannot find merchant"
                    });
                }
                _logger.LogInformation($" >> CreateRedeemMultiTxWithGroupCode >> {JsonConvert.SerializeObject(input)}");
                var output = await _challengeMafService.CreateRedeemMultiTxWithGroupCode(input);
                _logger.LogInformation($" >> CreateRedeemMultiTxWithGroupCode Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ChallengeMaf CreateRedeemMultiTxWithGroupCode Error - " + JsonConvert.SerializeObject(ex));
                var HttpStatusCode = ex.Data != null && ex.Data["StatusCode"] != null && string.IsNullOrEmpty(ex.Data["StatusCode"].ToString()) ? Int32.Parse(ex.Data["StatusCode"].ToString()) : 500;
                return StatusCode(HttpStatusCode, JsonConvert.DeserializeObject<LynkiDResponseExp>(ex.Data["ErrorData"].ToString()));
            }
        }
        [HttpGet]
        [Route("GetGiftRedeemTransWithGiftGroup")]
        public async Task<ActionResult<LynkiDResponseList<GiftRedeemedLoyaltyResultOuput>>> GetGiftRedeemTransWithGiftGroup(string MemberCode, string GiftGroupCode, string RedeemSource, string Lang, int SkipCount, int MaxResultCount)
        {
            try
            {
                var input = new GetGiftRedeemTransWithGiftGroupInput()
                {
                    GiftGroupCode = GiftGroupCode,
                    MemberCode = MemberCode,
                    RedeemSource = RedeemSource,
                    Lang = Lang,
                    SkipCount = SkipCount,
                    MaxResultCount = MaxResultCount
                };
                _logger.LogInformation($" >> GetGiftRedeemTransWithGiftGroup >> {JsonConvert.SerializeObject(input)}");
                var output = await _challengeMafService.GetGiftRedeemTransWithGiftGroup(input);
                _logger.LogInformation($" >> GetGiftRedeemTransWithGiftGroup Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ChallengeMaf GetGiftRedeemTransWithGiftGroup Error - " + JsonConvert.SerializeObject(ex));
                var HttpStatusCode = ex.Data != null && ex.Data["StatusCode"] != null && string.IsNullOrEmpty(ex.Data["StatusCode"].ToString()) ? Int32.Parse(ex.Data["StatusCode"].ToString()) : 500;
                return StatusCode(HttpStatusCode, JsonConvert.DeserializeObject<LynkiDResponseExp>(ex.Data["ErrorData"].ToString()));
            }
        }
        [HttpGet]
        [Route("CardTransactionGetAll")]
        public async Task<ActionResult<GetCardMafResponse>> CardTransactionGetAll(string MemberCode, string Status, string CardCode, string RefCode, int? IsRedeem, int? MerchantId, int skipCount, int maxResultCount)
        {
            try
            {
                var input = new CardTransactionGetAllInput()
                {
                    MemberCode = MemberCode,
                    IsRedeem = IsRedeem,
                    CardCode = CardCode,
                    RefCode = RefCode,
                    maxResultCount = maxResultCount,
                    MerchantId = MerchantId,
                    skipCount = skipCount,
                    Status = Status
                };
                _logger.LogInformation($" >> CardTransactionGetAll >> {JsonConvert.SerializeObject(input)}");
                var output = await _challengeMafService.CardTransactionGetAll(input);
                _logger.LogInformation($" >> CardTransactionGetAll Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ChallengeMaf CardTransactionGetAll Error - " + JsonConvert.SerializeObject(ex));
                var HttpStatusCode = ex.Data != null && ex.Data["StatusCode"] != null && string.IsNullOrEmpty(ex.Data["StatusCode"].ToString()) ? Int32.Parse(ex.Data["StatusCode"].ToString()) : 500;
                return StatusCode(HttpStatusCode, JsonConvert.DeserializeObject<OperatorResponseExp>(ex.Data["ErrorData"].ToString()));
            }
        }
        [HttpPost]
        [Route("CardTransactionRedeem")]
        public async Task<ActionResult<RewardRedeemCardMafResponse>> CardTransactionRedeem(CardTransactionRedeemInput input)
        {
            try
            {
                _logger.LogInformation($" >> CardTransactionRedeem >> {JsonConvert.SerializeObject(input)}");
                var output = await _challengeMafService.CardTransactionRedeem(input);
                _logger.LogInformation($" >> CardTransactionRedeem Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ChallengeMaf CardTransactionRedeem Error - " + JsonConvert.SerializeObject(ex));
                var HttpStatusCode = ex.Data != null && ex.Data["StatusCode"] != null && string.IsNullOrEmpty(ex.Data["StatusCode"].ToString()) ? Int32.Parse(ex.Data["StatusCode"].ToString()) : 500;
                return StatusCode(HttpStatusCode, JsonConvert.DeserializeObject<OperatorResponseExp>(ex.Data["ErrorData"].ToString()));
            }
        }
        [HttpPost]
        [Route("CardTransactionReturn")]
        public async Task<ActionResult<RewardReturnCardMafResponse>> CardTransactionReturn(CardTransactionReturnInput input)
        {
            try
            {
                _logger.LogInformation($" >> CardTransactionReturn >> {JsonConvert.SerializeObject(input)}");
                var output = await _challengeMafService.CardTransactionReturn(input);
                _logger.LogInformation($" >> CardTransactionReturn Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ChallengeMaf CardTransactionReturn Error - " + JsonConvert.SerializeObject(ex));
                var HttpStatusCode = ex.Data != null && ex.Data["StatusCode"] != null && string.IsNullOrEmpty(ex.Data["StatusCode"].ToString()) ? Int32.Parse(ex.Data["StatusCode"].ToString()) : 500;
                return StatusCode(HttpStatusCode, JsonConvert.DeserializeObject<OperatorResponseExp>(ex.Data["ErrorData"].ToString()));
            }
        }
        [HttpPost]
        [Route("CardTransactionCreate")]
        public async Task<ActionResult<CreateCardCoinOutput>> CardTransactionCreate(CardTransactionCreateInput input)
        {
            try
            {
                _logger.LogInformation($" >> CardTransactionCreate >> {JsonConvert.SerializeObject(input)}");
                var output = await _challengeMafService.CardTransactionCreate(input);
                _logger.LogInformation($" >> CardTransactionCreate Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ChallengeMaf CardTransactionCreate Error - " + JsonConvert.SerializeObject(ex));
                var HttpStatusCode = ex.Data != null && ex.Data["StatusCode"] != null && string.IsNullOrEmpty(ex.Data["StatusCode"].ToString()) ? Int32.Parse(ex.Data["StatusCode"].ToString()) : 500;
                return StatusCode(HttpStatusCode, JsonConvert.DeserializeObject<OperatorResponseExp>(ex.Data["ErrorData"].ToString()));
            }
        }
        [HttpPost]
        [Route("CardTransactionRevert")]
        public async Task<ActionResult<RewardResponse>> CardTransactionRevert(CardTransactionRevertInput input)
        {
            try
            {
                _logger.LogInformation($" >> CardTransactionRevert >> {JsonConvert.SerializeObject(input)}");
                var output = await _challengeMafService.CardTransactionRevert(input);
                _logger.LogInformation($" >> CardTransactionRevert Output >> {JsonConvert.SerializeObject(output)}");
                return output;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ChallengeMaf CardTransactionRevert Error - " + JsonConvert.SerializeObject(ex));
                var HttpStatusCode = ex.Data != null && ex.Data["StatusCode"] != null && string.IsNullOrEmpty(ex.Data["StatusCode"].ToString()) ? Int32.Parse(ex.Data["StatusCode"].ToString()) : 500;
                return StatusCode(HttpStatusCode, JsonConvert.DeserializeObject<OperatorResponseExp>(ex.Data["ErrorData"].ToString()));
            }
        }
        [HttpPost]
        [Route("RedeemGiftV2")]
        public async Task<ActionResult<LynkiDResponse<List<MasterCardRedeemMultiGiftOutput>>>> RedeemGiftV2(ChallengrRedeemGiftInput input)
        {
            try
            {
                _logger.LogInformation($" >> RedeemGiftV2 >> Get MerchantId infor from GiftCode");
                try
                {
                    var merchantIdFromGiftCode = await _linkIdLoyaltyVendorGiftService.GetMerchantIdFromGiftCode(new LoyaltyGiftGetMerchantIdFromGiftCodeInput()
                    {
                        GiftCode = input.GiftCode
                    });
                    if (merchantIdFromGiftCode == null || !merchantIdFromGiftCode.Success || merchantIdFromGiftCode.Result == null || !merchantIdFromGiftCode.Result.MerchantId.HasValue)
                    {
                        return StatusCode(500, new LynkiDResponseExp()
                        {
                            error = new ErrorExpObject()
                            {
                                code = 3001,
                                message = "Cannot find merchant"
                            },
                            success = false,
                            result = "Cannot find merchant"
                        });
                    }
                    input.MerchantIdRedeem = merchantIdFromGiftCode.Result.MerchantId.Value;
                }
                catch (Exception)
                {
                    return StatusCode(500, new LynkiDResponseExp()
                    {
                        error = new ErrorExpObject()
                        {
                            code = 3001,
                            message = "Cannot find merchant"
                        },
                        success = false,
                        result = "Cannot find merchant"
                    });
                }
                _logger.LogInformation($" >> ChallengeMAF RedeemGiftV2 >> {JsonConvert.SerializeObject(input)}");
                var output = await _challengeMafService.RedeemGiftV2(input);
                _logger.LogInformation($" >> ChallengeMAF RedeemGiftV2 Output >> {JsonConvert.SerializeObject(output)}");
                return new LynkiDResponse<List<MasterCardRedeemMultiGiftOutput>>()
                {
                    Result = output,
                    Success = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ChallengeMaf RedeemGiftV2 Error - " + JsonConvert.SerializeObject(ex));
                var HttpStatusCode = ex.Data != null && ex.Data["StatusCode"] != null && string.IsNullOrEmpty(ex.Data["StatusCode"].ToString()) ? Int32.Parse(ex.Data["StatusCode"].ToString()) : 500;
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    return StatusCode(HttpStatusCode, JsonConvert.DeserializeObject<LynkiDResponseExp>(ex.Data["ErrorData"].ToString()));
                }
                else if (ex.GetType() == typeof(RewardException))
                {
                    return StatusCode(HttpStatusCode, JsonConvert.DeserializeObject<OperatorResponseExp>(ex.Data["ErrorData"].ToString()));
                }
                else
                {
                    return StatusCode(HttpStatusCode, new LynkiDResponseExp()
                    {
                        success = false,
                        error = new ErrorExpObject()
                        {
                            code = "0",
                            message = ex.Message,
                        }
                    });
                }
            }
        }
    }
}
