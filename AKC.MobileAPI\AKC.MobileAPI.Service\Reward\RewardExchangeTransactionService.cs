﻿using AKC.MobileAPI.DTO.Reward.ExchangeTransaction;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardExchangeTransactionService : RewardBaseService, IRewardExchangeTransactionService
    {
        public RewardExchangeTransactionService(IConfiguration configuration) : base(configuration)
        {
        }

        public async Task<RewardCreateExchangeTransactionOutput> CreateExchangeTransactionIntegration(RewardCreateExchangeTransactionInput input)
        {
            var request = new RewardCreateExchangeTransactionDto()
            {
                NationalId = input.MemberCode,
                ExchangeAmount = input.ExchangeAmount,
                MerchantId = input.MerchantId,
                TransactionCode = input.TransactionCode,
                PartnerBindingTxId = input.PartnerBindingTxId,
            };
            return await PostRewardAsync<RewardCreateExchangeTransactionOutput>(RewardApiUrl.EXCHANGE_TRANSACTION_INTEGRATION_CREATE, request, MerchantNameConfig.VPBank);
        }

        public async Task<RewardRevertExchangeTransactionOutput> RevertExchangeTransactionIntegration(RewardRevertExchangeTransactionInput input)
        {
            return await PostRewardAsync<RewardRevertExchangeTransactionOutput>(RewardApiUrl.EXCHANGE_TRANSACTION_INTEGRATION_REVERT, input, MerchantNameConfig.VPBank);
        }

        public async Task<RewardExchangeVerifyOTPOutput> ExchangeVerifyOTP(RewardExchangeVerifyOTPInput input)
        {
            return await PostRewardAsync<RewardExchangeVerifyOTPOutput>(RewardApiUrl.EXCHANGE_VERIFITION_OTP, input);
        }

        public async Task<CheckPhoneNumberOutput> CheckPhoneNumber(CheckPhoneNumberInput input)
        {
            return await GetRewardAsync<CheckPhoneNumberOutput>(RewardApiUrl.EXCHANGE_TRANSACTION_INTEGRATION_CHECK_PHONE_NUMBER, input, MerchantNameConfig.VPBank);
        }
    }
}
