﻿
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;

namespace AKC.MobileAPI.DTO.Base
{
    public class CustomDateTimeConverter : JsonConverter
    {
        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(DateTime);
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            if (reader.Value != null)
            {
                DateTime dt;
                if (DateTime.TryParse(reader.Value.ToString(), out dt))
                    return dt.ToUniversalTime();
                else
                    return null;
            }
            return reader.Value;
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            writer.WriteValue(value);
        }
    }
}
