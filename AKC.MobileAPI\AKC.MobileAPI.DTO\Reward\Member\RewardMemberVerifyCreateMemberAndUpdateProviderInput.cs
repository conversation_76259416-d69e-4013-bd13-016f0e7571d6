﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberVerifyCreateMemberAndUpdateProviderInput
    {
        public string Status { get; set; }
        public string IdCard { get; set; }
        public string PartnerPhoneNumber { get; set; }
        public string Type { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }
        public string Gender { get; set; }
        public string Email { get; set; }
        public DateTime? Dob { get; set; }
        public string NationalId { get; set; }
        public string PointUsingOrdinary { get; set; }
        public string HashAddress { get; set; }
        public string RegionCode { get; set; }
        public string FullRegionCode { get; set; }
        public string MemberTypeCode { get; set; }
        public string FullMemberTypeCode { get; set; }
        public string ChannelType { get; set; }
        public string FullChannelTypeCode { get; set; }
        public string RankTypeCode { get; set; }
        public string StandardMemberCode { get; set; }
        public string ReferralCode { get; set; }
        public bool IsDeleted { get; set; }
        public string Avatar { get; set; }
        public string RegisterType { get; set; }
        public string ReferenceId { get; set; }
        public string ProviderId { get; set; }
        public string ProviderName { get; set; }
        public int NationId { get; set; }
        public int CityId { get; set; }
        public int DistrictId { get; set; }
        public int WardId { get; set; }
        public string StreetDetail { get; set; }
    }
}
