﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.ThirdParty
{
    public class LoyaltyThirdPartyPointExchangeOutput
    {
        public bool Success { get; set; }
        public string Error { get; set; }
        public int Result { get; set; }
        public LoyaltyThirdPartyPointExchangeResult Items { get; set; }
    }

    public class LoyaltyThirdPartyPointExchangeResult
    {
        public LoyaltyThirdPartyPointExchangeItem Transaction { get; set; }
    }

    public class LoyaltyThirdPartyPointExchangeItem
    {
        public string PartnerBindingTxId { get; set; }
        public long ExchangeAmount { get; set; }
        public long EquivalentTokenAmount { get; set; }
    }
}
