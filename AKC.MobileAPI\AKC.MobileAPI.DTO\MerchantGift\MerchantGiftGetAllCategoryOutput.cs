﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.MerchantGift
{
    public class MerchantGiftGetAllCategoryOutput
    {
		public int? Id { get; set; }
		public string Code { get; set; }
		public string Name { get; set; }
		public string Description { get; set; }
		public string Link { get; set; }
		public string FullLink { get; set; }
		public string ParentCode { get; set; }
	}

	// For DTO
	public class MerchantGiftGetAllCategoryOutputDto
	{
		public MerchantGiftGetAllCategoryOutputResultDto Result { get; set; }
		public string TargetUrl { get; set; }
		public bool Success { get; set; }
		public string Error { get; set; }
		public bool UnAuthorizedRequest { get; set; }
		public bool __abp { get; set; }
	}

	public class MerchantGiftGetAllCategoryOutputResultDto
	{
		public int TotalCount { get; set; }

		public List<MerchantGiftGetAllCategoryOutputItemDto> Items { get; set; }
	}

	public class MerchantGiftGetAllCategoryOutputItemDto
	{
		public GetAllByMemberCodeDto GiftCategory { get; set; }

		public string ParentNameGiftCategory { get; set; }
	}

	public class GetAllByMemberCodeDto
	{
		public string Code { get; set; }
		public string Name { get; set; }
		public string Description { get; set; }
		public string Status { get; set; }
		public int Level { get; set; }
		public string ParentCode { get; set; }
		public int? ParentId { get; set; }
		public ImageLinkDto ImageLink { get; set; }
		public int? VendorId { get; set; }
		public bool Is3rdPartyGiftCategory { get; set; }
		public int? ThirdPartyGiftCategoryId { get; set; }
		public string GiftListJSON { get; set; }
		public string VendorName { get; set; }
		public DateTime? LastModificationTime { get; set; }
		public long? LastModifierUserId { get; set; }
		public string LastModifierUserName { get; set; }
		public int Id { get; set; }
	}
	public class ImageLinkDto
	{
		public string Code { get; set; }
		public string Type { get; set; }
		public string Link { get; set; }
		public bool isActive { get; set; }
		public int? Ordinal { get; set; }
		public string FullLink { get; set; }
		public int Id { get; set; }
	}
}
