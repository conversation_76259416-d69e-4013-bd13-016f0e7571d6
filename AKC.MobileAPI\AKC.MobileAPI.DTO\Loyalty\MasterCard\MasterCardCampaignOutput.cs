﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AKC.MobileAPI.DTO.Loyalty.MasterCard
{
    public class MasterCardCampaignOutput
    {
        public CampaignDto Campaign { get; set; }
        public ChallengeInfor Challenge { get; set; }
        public string Cif { get; set; }
        public string Status { get; set; }
    }
    public class MasterCardCheckRegisterOutput : MasterCardCampaignOutput
    {
        public int RegisterNumber { get; set; }
    }

    public class CampaignDto
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string GiftGroupCode { get; set; }
        public string ProgramRules { get; set; }
        public string Description { get; set; }
        public DateTime? EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
    }

    public class ChallengeInfor
    {
        public string TransactionCode { get; set; }
        public int CampaignId { get; set; }
        public long? ActualAmount { get; set; }
        public long? ActualTnx { get; set; }
        public long? TotalActualAmount { get; set; }
        public long? TotalActualTnx { get; set; }
        public string Status { get; set; }
        public DateTime? GoalDate { get; set; }
        public decimal? GiftCoin { get; set; }
        public string CoinStatus { get; set; }
        public string CardCode { get; set; }
        public string SmallPhoto { get; set; } = "https://linkidstorage.s3-ap-southeast-1.amazonaws.com/upload-gift/6864c4db2659316f9d0c4254c4b17c7e.png";
        public string BigPhoto { get; set; } = "https://linkidstorage.s3-ap-southeast-1.amazonaws.com/upload-gift/32ddf688419104ce0d8d3d4e75533e72.png";
        public string CardExpiredDate { get; set; }
        public decimal? RemainGiftCoin { get; set; }
    }

    public class ChallengeInforAfterUpdateOutput : ChallengeInfor
    {
        public ChallengeInforWarning Warning { get; set; }
    }

    public class ChallengeInforWarning
    {
        public string code { get; set; }
        public string message { get; set; }
    }

    public class CampaignInfor
    {
        public CampaignDto Campaign { get; set; }
        public List<BaseSettingDto> BaseSetting { get; set; }
        public List<EventSettingDto> EventSetting { get; set; }
    }

    public class BaseSettingDto
    {
        public int CampaignId { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string GiftGroupCode { get; set; }
        public DateTime? EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
    }

    public class EventSettingDto
    {
        public int CampaignId { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public List<Amounts> Amounts { get; set; }
    }

    public class Amounts
    {
        public string EventSettingCode { get; set; }
        public long? Amount { get; set; }
        public long? CoinValue { get; set; }
        public long? PointValue { get; set; }
        public long? ActualTnx { get; set; }
    }

    // payload danh sách thẻ đã mở
    public class RewardReturnCardResponse
    {
        public int? result { get; set; }
        public string message { get; set; }
        public RewardRedeemCardItem item { get; set; }
    }
    public class RewardRedeemCardResponse
    {
        public int? result { get; set; }
        public string message { get; set; }
        public List<RewardRedeemCardItem> item { get; set; }
    }
    public class RewardRedeemCardItem
    {
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public bool IsDeleted { get; set; }
        public int CardId { get; set; }
        public string FromWalletAddress { get; set; }
        public string ToWalletAddress { get; set; }
        public decimal? TokenAmount { get; set; }
        public string ActionType { get; set; }
        public string OrderCode { get; set; }
        public string Status { get; set; }
        public string Reason { get; set; }
        public int UserId { get; set; }
        public int Id { get; set; }
    }
    public class GetCardResponse
    {
        public List<CardObject> Items { get; set; }
    }
    public class CardObject
    {
        public int Id { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public bool? IsDeleted { get; set; }
        public string MerchantID { get; set; }
        public string MemberCode { get; set; }
        public DateTime? BusinessTime { get; set; }
        public int? CampaignID { get; set; }
        public string PhoneNumber { get; set; }
        public string CardCode { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public decimal? TokenAmount { get; set; }
        public decimal? RemainingAmount { get; set; }
        public string Status { get; set; }
        public string Note { get; set; }
        public object History { get; set; }
        public string MerChantPhoto { get; set; }
    }

    public class MasterCardCustomer
    {
        public int? Id { get; set; }
        public int? TenantId { get; set; }
        public string Cif { get; set; }
        public string Name { get; set; }
        public string LegalId { get; set; }
        public string PhoneNumber { get; set; }
        public string Email { get; set; }
        public int? CampaignId { get; set; }
        public string Status { get; set; }
        public string MemberCode { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
    }

    public class AdapterResponse<T>
    {
        public bool Status { get; set; }
        public T data { get; set; }
    }
    public class ChallengeProcessingOutput
    {
        public string CIF { get; set; }
        public string RegisterCampaign { get; set; }
        public decimal? TotalActualAmount { get; set; }
        public decimal? TotalActualTnx { get; set; }
        public DateTime? FromCampaign { get; set; }
        public DateTime? ToCampaign { get; set; }
    }

    public class GiftRedeemedUtilsOuput
    {
        public string Cif { get; set; }
        public string MemberCode { get; set; }
        public string GiftGroupCode { get; set; }
    }

    public class GiftRedeemedAndCardInfor
    {
        public List<GiftRedeemedLoyaltyOuput> GiftRedeemeds { get; set; }
        public List<MasterCardCardCoinOuput> CardInfors { get; set; }
    }
    public class GiftRedeemedLoyaltyOuput
    {
        public string TransactionCode { get; set; }
        public string EGiftCode { get; set; }
        public DateTime? EGiftExpiredDate { get; set; }
        public string GiftCode { get; set; }
        public string GiftName { get; set; }
        public string GiftIntroduce { get; set; }
        public string GiftDescription { get; set; }
        public string GiftPhoto { get; set; }
        public string TransactionDescription { get; set; }
        public decimal? Amount { get; set; }
        public DateTime? CreationTime { get; set; }
        public string GiftCondition { get; set; }
        public string GiftUsageAddress { get; set; }
        public string VendorName { get; set; }
        public string VendorLogo { get; set; }
        public int? BrandId { get; set; }
        public string BrandName { get; set; }
        public string BrandLogo { get; set; }
        public string Status { get; set; }
        public bool? IsEncrypted { get; set; }
    }

    public class MasterCardListCardCoin
    {
        public CampaignDto Campaign { get; set; }
        public ChallengeInforCard Challenge { get; set; }
        public string Cif { get; set; }
        public string Status { get; set; }
    }

    public class ChallengeInforCard : ChallengeInfor
    {
        public List<string> GiftRemdeemCodes { get; set; }
    }

    public class MasterCardCardCoinOuput
    {
        public CampaignDto Campaign { get; set; }
        public ChallengeInforCardOutput Challenge { get; set; }
        public string Cif { get; set; }
        public string Status { get; set; }
    }

    public class ChallengeInforCardOutput : ChallengeInfor
    {
        public string MerChantPhoto { get; set; }
        public List<GiftRedeemedLoyaltyOuput> GiftInfor { get; set; }
    }
}
