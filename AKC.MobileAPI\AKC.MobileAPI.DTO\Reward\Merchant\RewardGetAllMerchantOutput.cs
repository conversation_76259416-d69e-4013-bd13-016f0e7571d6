﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Merchant
{
    public class RewardGetAllMerchantOutput
    {
        public int Result { get; set; }
        public List<RewardGetAllMerchantItems> Items { get; set; }
        public int TotalCount { get; set; }
        public string Message { get; set; }
    }

    public class RewardGetAllMerchantItems
    {
        public string WalletAddress { get; set; }
        public int Id { get; set; }
        public string MerchantName { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }
        public string Status { get; set; }
        public string Logo { get; set; }
        public string Type { get; set; }
        public decimal BaseUnit { get; set; }
        public decimal PointExchangeRate { get; set; }
        public string MaintenanceFrom { get; set; }
        public string MaintenanceTo { get; set; }
        public string MaintenanceStatus { get; set; }
        public bool IsAKCLoyalty { get; set; }
        public string X1lTenantId { get; set; }
        public string OrgId { get; set; }
        public List<RewardGetAllMerchantStore> StoreList { get; set; }
    }

    public class RewardGetAllMerchantStore
    {
        public int? Id { get; set; }
        public int? MerchantId { get; set; }
        public string StoreName { get; set; }
        public string UnsignName { get; set; }
        public string PhoneNumber { get; set; }
        public string Address { get; set; }
        public string Email { get; set; }
        public string Status { get; set; }
        public string Avatar { get; set; }
        public string Region { get; set; }
        public long? Longitude { get; set; }
        public string Description { get; set; }
        public long? Latitude { get; set; }
    }
}
