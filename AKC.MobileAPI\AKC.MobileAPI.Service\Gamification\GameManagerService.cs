﻿using AKC.MobileAPI.DTO.Gamification;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.Service.Abstract.Gamification;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.Extensions.Configuration;

using System.Threading.Tasks;
using System.Web;

namespace AKC.MobileAPI.Service.Gamification
{
    public class GamificationManagementService : GameManagementBaseService, IGameManagementService
    {
        protected readonly IConfiguration _configuration;
        protected static string pubKey;
        protected static string priKey;
        ILoyaltySecondaryCustomersService _customersService;
        public GamificationManagementService(
            IConfiguration configuration,
            ILoyaltySecondaryCustomersService customersService
            ) : base(configuration)
        {
            _configuration = configuration;
            pubKey = _configuration.GetSection("keys:publickey").Value;
            priKey = _configuration.GetSection("keys:privatekey").Value;
            _customersService = customersService;
        }
        public async Task<ViewGameListOutput> ViewGameList(ViewGameListInput input)
        {
            return await GetGamificationManagementAsync<ViewGameListOutput>(GamificationApiUrl.VIEW_GAME_LIST, input);
        }

        public async Task<GetGamificationLinkOutput> GetGamificationLink(GetGamificationLinkInput input)
        {
            var tenantId = RSAHelper.Encrypt(input.TenantId.ToString(), pubKey);
            var memberCodeEncrypt = RSAHelper.Encrypt(input.MemberCode, pubKey);
            //var data = RSAHelper.Decrypt(memberCodeEncrypt, priKey);
            var link = new GetGamificationLinkDto()
            {
                url = GamificationApiUrl.GAMIFICATION_LINK + HttpUtility.UrlEncode(tenantId)  + "&code=" + HttpUtility.UrlEncode(memberCodeEncrypt)
            };

            return new GetGamificationLinkOutput() 
            {
                item = link
            };
        }
    }
}
