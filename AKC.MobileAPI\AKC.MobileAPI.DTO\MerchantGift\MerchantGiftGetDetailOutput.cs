﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.MerchantGift
{
    public class MerchantGiftGetDetailOutput
    {
		public int? Id { get; set; }
		public string Code { get; set; }
		public string Name { get; set; }
		public string Description { get; set; }
		public string ShortDescription { get; set; }
		public decimal? RequiredCoin { get; set; }
		public string CategoryCode { get; set; }
		public decimal? InStock { get; set; }
		public bool? IsEgift { get; set; }
		public string VendorHotline { get; set; }
		//public string Office { get; set; }
		public string Vendor { get; set; }
		
		public string VendorImage { get; set; }
		public string VendorDescription { get; set; }
		public string BrandName { get; set; }
		public string BrandLinkLogo { get; set; }
		public string BrandAddress { get; set; }
		public string BrandDescription { get; set; }
		public List<MerchantGiftImageLinkShortDto> ImageLinks { get; set; }
	}

	// For DTO
	public class MerchantGiftGetDetailOutputDto
	{
		public ListResultGetByIdAndRelatedGiftDto Result { get; set; }
		public string TargetUrl { get; set; }
		public bool Success { get; set; }
		public string Error { get; set; }
		public bool UnAuthorizedRequest { get; set; }
		public bool __abp { get; set; }
	}

	public class ListResultGetByIdAndRelatedGiftDto
	{
		public MerchantGiftShortInforDto GiftInfor { get; set; }
		public List<ImageLinkDto> ImageLink { get; set; }
		public List<MerchantGiftShortInforForView> RelatedGiftInfor { get; set; }
		public string ErrorCode { get; set; }
	}
}
