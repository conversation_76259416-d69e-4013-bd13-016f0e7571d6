﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Auth;
using Microsoft.WindowsAzure.Storage.Blob;
using Minio;
using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Common
{
    public class UploadImageSevice : IUploadImageSevice
    {
        private readonly IConfiguration _configuration;
        private string _azureAccount;
        private string _azureAccessKey;
        private string _linkUpload;
        private bool _useAzureStorage;
        private string _memberAvatarBucketStorageAzure;
        // S3 Or Minio
        private string _endpoint;
        private string _accessKey;
        private readonly string _secretKey;
        private bool _isHttps;
        private string _memberAvatarBucketStorageMiniO;
        private readonly ILogger _logger;
        public UploadImageSevice(
            IConfiguration configuration,
            ILogger<UploadImageSevice> logger
        )
        {
            _logger = logger;
            _configuration = configuration;
            _endpoint = _configuration.GetSection("StoreFiles:AWSS3OrMinIO:LinkUpload").Value;
            _accessKey = _configuration.GetSection("StoreFiles:AWSS3OrMinIO:AccessKey").Value;
            _secretKey = _configuration.GetSection("StoreFiles:AWSS3OrMinIO:SecretKey").Value;
            _isHttps = bool.Parse(_configuration.GetSection("StoreFiles:AWSS3OrMinIO:Https").Value);
            _memberAvatarBucketStorageMiniO = _configuration.GetSection("StoreFiles:AWSS3OrMinIO:MemberAvatarBucket").Value;

            //_azureAccount = _configuration.GetSection("StoreFiles:AzureStorage:Account").Value;
            //_azureAccessKey = _configuration.GetSection("StoreFiles:AzureStorage:AccessKey").Value;
            //_linkUpload = _configuration.GetSection("StoreFiles:AzureStorage:LinkUpload").Value;
            //_useAzureStorage = bool.Parse(_configuration.GetSection("StoreFiles:AzureStorage:Enable").Value);
            _useAzureStorage = false;
            //_memberAvatarBucketStorageAzure = _configuration.GetSection("StoreFiles:AzureStorage:MemberAvatarBucket").Value;
        }

        public async Task<string> UploadImage(string base64String)
        {
            if (_useAzureStorage)
            {
                return await UploadImageGiftToAuzre(base64String);
            }
            else
            {
                return await UploadImageToMinio(base64String);
            }
        }
        private async Task<string> UploadImageToMinio(string base64String)
        {
            var ext = "png";
            
            var fileNameUpload = GenerateFileName() + "." + ext;
            byte[] data = GetDataFromBase64String(base64String);
            try
            {
                var minio = new MinioClient(_endpoint, _accessKey, _secretKey).WithSSL();
                // Make a bucket on the server, if not already present.
                bool found = await minio.BucketExistsAsync(_memberAvatarBucketStorageMiniO);
                if (!found)
                {
                    await minio.MakeBucketAsync(_memberAvatarBucketStorageMiniO);
                }

                using (var stream = new MemoryStream(data, 0, data.Length))
                {
                    await minio.PutObjectAsync(_memberAvatarBucketStorageMiniO,
                                  fileNameUpload,
                                  stream,
                                  stream.Length
                                  );
                    _logger.LogInformation("Successfully uploaded " + fileNameUpload);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
                throw new Exception(ex.Message);
            }
            if (_isHttps)
            {
                _endpoint = "https://" + _endpoint;
            }
            else
            {
                _endpoint = "http://" + _endpoint;
            }
            var link = _endpoint + "/" + _memberAvatarBucketStorageMiniO + "/" + fileNameUpload;
            return await Task.FromResult(link);
        }

        private async Task<string> UploadImageGiftToAuzre(string base64String)
        {
            var ext = "png";
            var fileNameUpload = GenerateFileName() + "." + ext;
            byte[] data = GetDataFromBase64String(base64String);

            StorageCredentials storageCredentials = new StorageCredentials(_azureAccount, _azureAccessKey);
            CloudStorageAccount storageAccountB = new CloudStorageAccount(storageCredentials, true);
            CloudBlobClient blobClient = storageAccountB.CreateCloudBlobClient();
            CloudBlobContainer container = blobClient.GetContainerReference(_memberAvatarBucketStorageAzure);
            using (var stream = new MemoryStream(data, 0, data.Length))
            {
                CloudBlockBlob blockBlob = container.GetBlockBlobReference(fileNameUpload);
                await blockBlob.UploadFromStreamAsync(stream);
            }

            var link = _linkUpload + "/" + _memberAvatarBucketStorageAzure + "/" + fileNameUpload;
            return await Task.FromResult(link);
        }

        private byte[] GetDataFromBase64String(string base64String)
        {
            try
            {
                byte[] data = Convert.FromBase64String(base64String);
                return data;
            }
            catch
            {
                throw new ArgumentException("Invalid data from base 64");
            }
        }

        private string GenerateFileName()
        {
            string fromString = DateTime.Now.ToString("yyyyMMddHHmmss") + DateTime.UtcNow.Ticks;
            using (var md5 = MD5.Create())
            {
                var data = md5.ComputeHash(Encoding.UTF8.GetBytes(fromString));
                StringBuilder sBuilder = new StringBuilder();

                for (int i = 0; i < data.Length; i++)
                {
                    sBuilder.Append(data[i].ToString("x2"));
                }

                return sBuilder.ToString();
            }
        }
    }
}
