using AKC.MobileAPI.DTO.Const;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Loyalty.LocationManagement;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.Loyalty.Transaction;
using AKC.MobileAPI.DTO.MerchantGift;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.TopUpTransaction;
using AKC.MobileAPI.DTO.Sme;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.LoyaltyVendorGift;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using AKC.MobileAPI.Service.Loyalty;
using AKC.MobileAPI.Service.LoyaltyVendorGift.LinkID;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.LoyaltyVendorGift
{
    public class LinkIdLoyaltyVendorGiftService : BaseLinkIdLoyaltyVendorGiftService, ILinkIdLoyaltyVendorGiftService
    {
        private readonly IRewardMemberService _rewardMemberService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILoyaltyUtilsService _loyaltyUtilsService;
        protected readonly ILogger _logger;
        private readonly Lazy<IMerchantGiftService> _merchantGiftService;

        public LinkIdLoyaltyVendorGiftService(
            IConfiguration configuration,
            IDistributedCache cache,
            ILogger<LinkIdLoyaltyVendorGiftService> logger,
            IRewardMemberService rewardMemberService,
            IExceptionReponseService exceptionReponseService,
            ILoyaltyUtilsService loyaltyUtilsService,
            Lazy<IMerchantGiftService> merchantGiftService
            ) : base(configuration, cache, logger)
        {
            _rewardMemberService = rewardMemberService;
            _exceptionReponseService = exceptionReponseService;
            _logger = logger;
            _loyaltyUtilsService = loyaltyUtilsService;
            _merchantGiftService = merchantGiftService;
        }
        public async Task<MerchantGiftCreateRedeemOutputDto> CreateRedeem(int memberId, MerchantGiftCreateRedeemInputDto input)
        {
            var merchantIdFromGiftCode = await GetMerchantIdFromGiftCode(new LoyaltyGiftGetMerchantIdFromGiftCodeInput()
            {
                GiftCode = input.GiftCode
            });
            _logger.LogInformation($"GetMerchantIdFromGiftCode({input.GiftCode})___Response:{JsonConvert.SerializeObject(merchantIdFromGiftCode)}");
            if (merchantIdFromGiftCode == null || !merchantIdFromGiftCode.Success || merchantIdFromGiftCode.Result == null || !merchantIdFromGiftCode.Result.MerchantId.HasValue)
            {
                CommonHelper.GetErrorValidation("924", "Cannot find merchant");
            }
            var merchantId = merchantIdFromGiftCode.Result.MerchantId.Value;
            //var merchantId = Convert.ToInt32(_configuration.GetSection("LoyaltyLinkID:MerchantIdRedeem").Value);
            // Gửi merchant id redeem cho linkid
            input.MerchantIdRedeem = merchantId;
            var orderCode = input.TransactionCode;
            var cts = new CancellationTokenSource();
            var requestRedeem = new RewardMemberRedeemInput()
            {
                OrderCode = orderCode,
                MerchantId = merchantId,
                MemberId = memberId,
                TotalRequestedAmount = input.TotalAmount,
            };
            var requestRevert = new RewardMemberRevertRedeemInput()
            {
                MerchantId = merchantId,
                MemberId = memberId,
                OrderCode = orderCode,
            };
            // Kiểm tra quà bên loyalty và tạo đơn hàng redeem tạm. nếu tạo thành công thì mới process thanh toán (gọi sang reward)
            var checkTransactionPending = await PostLoyaltyAsync<VerifyAndCreateRedeemOrderOutput>(LoyaltyApiUrl.VERIFY_OR_CREATE_REDEEM_ORDER, input);
            if ((!checkTransactionPending.Success || (checkTransactionPending.Success && !checkTransactionPending.Result.IsSuccess)))
            {
                // Can not redeem at this time
                CommonHelper.GetErrorValidation(CommonHelper.GetCodeRedeemFromCode("1022"), CommonHelper.GetMessageRedeemFromCode("1022"));
                return new MerchantGiftCreateRedeemOutputDto()
                {
                    Result = new MerchantGiftCreateRedeemTransactionDto()
                    {
                        SuccessedRedeem = false,
                    }
                };
            }
            var successPaymentToken = false;
            var hasReverted = false;
            try
            {
                var resultRedeemReward = await _rewardMemberService.RedeemTransaction(requestRedeem);
                if (resultRedeemReward.result == 202)
                {
                    successPaymentToken = true;
                    _logger.LogError($"Create redeem reward with status 202 {JsonConvert.SerializeObject(resultRedeemReward)}");
                    CommonHelper.GetErrorValidation("RedeemRewardTransactionStatus202", "Create redeem reward with status 202");
                }
                requestRevert.OriginalTokenTransID = resultRedeemReward.items.TokenTransID;
                requestRevert.TokenAmount = resultRedeemReward.items.TokenAmount;
                successPaymentToken = true;

                var result = new MerchantGiftCreateRedeemOutputDto()
                {
                    Result = new MerchantGiftCreateRedeemTransactionDto()
                    {
                        SuccessedRedeem = false,
                    }
                };
                result = await PostLoyaltyAsync<MerchantGiftCreateRedeemOutputDto>(LoyaltyApiUrl.MERCHANT_GIFT_CREATE_REDEEM, input);
                if (!string.IsNullOrWhiteSpace(result.Result.Exception) || !result.Result.SuccessedRedeem)
                {
                    if (!result.Result.Timeout)
                    {
                        hasReverted = true;
                        await retryRevertToken(requestRevert);
                    }
                    await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = input.TransactionCode,
                        ErrorCode = result.Result.Exception,
                        ErrorMessage = result.Result.Messages
                    }, successPaymentToken);
                    CommonHelper.GetErrorValidation(CommonHelper.GetCodeRedeemFromCode(result.Result.Exception), CommonHelper.GetMessageRedeemFromCode(result.Result.Exception));
                }
                return result;
            }
            catch (WebException ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    if (res.Code != "0" && res.Code != "SystemError" && !hasReverted)
                    {
                        await retryRevertToken(requestRevert);
                    }
                }
                await GetErrorFromExeption(ex, input.TransactionCode, successPaymentToken);
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    if (res.Code != "0" && res.Code != "SystemError" && !hasReverted)
                    {
                        await retryRevertToken(requestRevert);
                    }
                }

                await GetErrorFromExeption(ex, input.TransactionCode, successPaymentToken);
                if (!cts.Token.IsCancellationRequested)
                {
                    await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = input.TransactionCode,
                        ErrorCode = "408",
                        ErrorMessage = "Timed Out with: " + ex.Message,
                    }, successPaymentToken);
                    throw new Exception("Timed Out with: ", ex);
                }
                else
                {
                    await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = input.TransactionCode,
                        ErrorCode = "504",
                        ErrorMessage = "Cancelled for some other reason: " + ex.Message,
                    }, successPaymentToken);
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    if (res.Code == "RedeemRewardTransactionStatus202")
                    {
                        await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                        {
                            TransactionCode = input.TransactionCode,
                            ErrorCode = "202",
                            ErrorMessage = "" + res.MessageDetail,
                        }, successPaymentToken);
                        throw new Exception("" + res.MessageDetail);
                    }
                }

                // Nếu không phải exception của reward thì mới revert
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    if (res.Code != "0" && res.Code != "SystemError" && !hasReverted)
                    {
                        await retryRevertToken(requestRevert);
                    }
                }
                await GetErrorFromExeption(ex, input.TransactionCode, successPaymentToken);
                throw ex;
            }
        }

        public async Task<MerchantGiftGetAllCategoryOutputDto> GetAllCategory(MerchantGiftGetAllCategoryInputDto input)
        {
            return await GetLoyaltyAsync<MerchantGiftGetAllCategoryOutputDto>(LoyaltyApiUrl.MERCHANT_GIFT_GET_ALL_CATEGORY, input);
        }
        public async Task<MerchantGiftGetAllCategoryOutputDto> GetAllCategory_v1(MerchantGiftGetAllCategoryInputDto input)
        {
            _logger.LogInformation(" LinKID GetAllCategory_v1 >> Start ");
            var keyOfCache = "GET_ALL_CATEGORY_LINKID_DEFAULT";
            var cachedValue = await _cache.GetStringAsync(keyOfCache);
            if (!string.IsNullOrEmpty(cachedValue))
            {
                _logger.LogInformation(" LinKID GetAllCategory_v1 >> Cache hit ");
                try
                {
                    var deserialized = JsonConvert.DeserializeObject<MerchantGiftGetAllCategoryOutputDto>(cachedValue);
                    return deserialized;
                }
                catch (Exception)
                {
                    // Not valid String, call to LinkID to get latest data...
                    _logger.LogInformation(" LinKID GetAllCategory_v1 >> Cache hit BUT Invalid data. ");
                }
            }
            _logger.LogInformation(" LinKID GetAllCategory_v1 >> Cache missed ");
            var res = await GetLoyaltyAsync<MerchantGiftGetAllCategoryOutputDto>(LoyaltyApiUrl.MERCHANT_GIFT_GET_ALL_CATEGORY_V1, input);
            // Serialize res and save to cache
            var cacheOption = new DistributedCacheEntryOptions()
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5)
            };
            _logger.LogInformation(" LinKID GetAllCategory_v1 >> Got res from LinkID and save to cache and return... DONE! ");
            await _cache.SetStringAsync(keyOfCache, JsonConvert.SerializeObject(res), cacheOption);

            return res;
        }

        public async Task<GetAllCategoryWithoutMemberCodeOutput> GetAllCategoryWithoutMemberCode(GetAllCategoryWithoutMemberCodeInput input)
        {
            var ret = await GetLoyaltyAsync<LoyaltyResponse<GetAllCategoryWithoutMemberCodeOutput>>(LoyaltyApiUrl.GetAllGiftCategoryWithoutMemberCode, input);
            return ret.Result;
        }

        public async Task<LoyaltyResponse<MerchantGiftGetCreateRedeemOutputDto>> GetCreateRedeemCache(MerchantGiftGetCreateRedeemInputDto input)
        {
            return await GetLoyaltyAsync<LoyaltyResponse<MerchantGiftGetCreateRedeemOutputDto>>(LoyaltyApiUrl.MERCHANT_GIFT_GET_REDEEM_CACHE, input);
        }

        public async Task<LoyaltyResponseList<MerchantGiftLocationDto>> GetAllLocation(MerchantGiftLocationInputDto input)
        {
            return await GetLoyaltyAsync<LoyaltyResponseList<MerchantGiftLocationDto>>(LoyaltyApiUrl.MERCHANT_GIFT_GET_ALL_LOCATION, input);
        }

        public async Task<MerchantGiftGetDetailOutputDto> GiftDetail(MerchantGiftGetDetailInputDto input)
        {
            return await GetLoyaltyAsync<MerchantGiftGetDetailOutputDto>(LoyaltyApiUrl.MERCHANT_GIFT_GET_GIFT_DETAIL, input);
        }

        public async Task<LoyaltyResponseList<MerchantGiftGetAllGiftOutputDto>> GiftList(MerchantGiftGetAllGiftInputDto input)
        {
            // LOGIC CACHE CHỈ ÁP DỤNG CHO CASE QUERY DEFAULT FILTER (trừ cate code)
            bool needCache = !input.FromCointFilter.HasValue && !input.ToCoinFilter.HasValue
                                                             && string.Equals(input.Sorting, " UsedQuantity DESC ")
                                                             && string.IsNullOrEmpty(input.Filter)
                                                             && input.SkipCount == 0 && string.IsNullOrWhiteSpace(input.GiftCategoryChannelCode);
            _logger.LogInformation($" LinKID GiftList {input.FullGiftCategoryCodeFilter} >> Start ");

            if (!needCache)
            {
                _logger.LogInformation($" LinKID GiftList {input.FullGiftCategoryCodeFilter} >> Custom filter, cache not work here ");
                return await GetLoyaltyAsync<LoyaltyResponseList<MerchantGiftGetAllGiftOutputDto>>(LoyaltyApiUrl.MERCHANT_GIFT_GET_GIFT_LIST, input);
            }

            var keyOfCache = "LINKID_DEFAULT_GIFT_LIST_OF_CATE_" + input.FullGiftCategoryCodeFilter + "_" + input.MaxResultCount;
            var cachedValue = await _cache.GetStringAsync(keyOfCache);
            if (!string.IsNullOrEmpty(cachedValue))
            {
                _logger.LogInformation($" LinKID GiftList {input.FullGiftCategoryCodeFilter} >> Cache hit ");
                try
                {
                    var deserialized = JsonConvert.DeserializeObject<LoyaltyResponseList<MerchantGiftGetAllGiftOutputDto>>(cachedValue);
                    return deserialized;
                }
                catch (Exception)
                {
                    // Not valid String, call to LinkID to get latest data...
                    _logger.LogInformation($" LinKID GiftList {input.FullGiftCategoryCodeFilter} >> Cache hit BUT Invalid data. ");
                }
            }
            _logger.LogInformation($" LinKID GiftList {input.FullGiftCategoryCodeFilter} >> Cache missed ");
            // Call API to LinkID
            var res = await GetLoyaltyAsync<LoyaltyResponseList<MerchantGiftGetAllGiftOutputDto>>(LoyaltyApiUrl.MERCHANT_GIFT_GET_GIFT_LIST, input);
            // Serialize res and save to cache
            var cacheOption = new DistributedCacheEntryOptions()
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5)
            };
            _logger.LogInformation($" LinKID GiftList {input.FullGiftCategoryCodeFilter} >> Got res from LinkID and save to cache and return... DONE! ");
            await _cache.SetStringAsync(keyOfCache, JsonConvert.SerializeObject(res), cacheOption);

            return res;
        }

        public async Task<MerchantGiftTransactionHistoryOutputDto> TransactionDetail(MerchantGiftTransactionDetailInputDto input)
        {
            return await GetLoyaltyAsync<MerchantGiftTransactionHistoryOutputDto>(LoyaltyApiUrl.MERCHANT_GIFT_GET_ORDER_DETAIL, input);
        }

        public async Task<TransactionHistorySmeOutput> TransactionHistorySme(TransactionHistorySmeInput input)
        {
            return await GetLoyaltyAsync<TransactionHistorySmeOutput>(LoyaltyApiUrl.GetGiftTransForNEOBiz, input);
        }
        public async Task<TransactionHistorySmeOutput> TransactionHistorySmeDetail(TransactionHistorySmeInput input)
        {
            return await GetLoyaltyAsync<TransactionHistorySmeOutput>(LoyaltyApiUrl.GetGiftTransForNEOBiz, input);
        }
        public async Task<MerchantGiftTransactionHistoryOutputDto> TransactionHistory(MerchantGiftTransactionDetailInputDto input)
        {
            return await GetLoyaltyAsync<MerchantGiftTransactionHistoryOutputDto>(LoyaltyApiUrl.GetAllWithEGift, input);
        }

        public async Task UpdateErrorWhenCreateRedeem(UpdateErrorWhenCreateRedeemPayment input, bool successPaymentToken)
        {
            try
            {
                if (!successPaymentToken)
                {
                    input.Status = "Rejected";
                }
                await PostLoyaltyAsync<UpdateErrorWhenCreateRedeemPaymentOutput>(LoyaltyApiUrl.UPDATE_ERROR_WHEN_CREATE_REDEEM, input);
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Call UPDATE_ERROR_WHEN_CREATE_REDEEM fail: " + ex.Message);
            }
        }

        // For redeem
        private string genOrderCode(string orderCode, string memberCode)
        {
            return LoyaltyHelper.GenTransactionCode(orderCode + memberCode + DateTime.Now.Ticks);
        }

        public async Task retryRevertToken(RewardMemberRevertRedeemInput request, string url = null)
        {
            var retryNum = 5;
            while (retryNum != 0)
            {
                var result = await revertToken(request, url);
                if (retryNum != 5)
                {
                    await Task.Delay(1500);
                }
                if (result)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        private async Task<bool> revertToken(RewardMemberRevertRedeemInput request, string url = null)
        {
            try
            {
                await _rewardMemberService.RevertRedeemTransaction(request, url);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task GetErrorFromExeption(Exception ex, string transactionCode, bool successPaymentToken)
        {
            if (ex.GetType() == typeof(LoyaltyException))
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                {
                    TransactionCode = transactionCode,
                    ErrorCode = res.Code == "SystemError" ? "504" : res.Code,
                    ErrorMessage = res.Code != "SystemError" ? res.Message?.ToString() : res.MessageDetail?.ToString(),
                }, successPaymentToken);
                CommonHelper.GetErrorValidation(CommonHelper.GetCodeRedeemFromCode(res.Code), CommonHelper.GetMessageRedeemFromCode(res.Code));
            }
        }

        public async Task<GetAllLocationManagementDto> GetAllLocationShip(GetAllLocationInput input)
        {
            return await GetLoyaltyAsync<GetAllLocationManagementDto>(LoyaltyApiUrl.LOCATION_GET_ALL, input);
        }

        public async Task<ViewLocationByIdsOutput> ViewLocationShipByIds(ViewLocationByIds input)
        {
            return await PostLoyaltyAsync<ViewLocationByIdsOutput>(LoyaltyApiUrl.LOCATION_VIEW_BY_IDS, input);
        }

        public async Task<LoyaltyResponse<MerchantGiftVerifyCreateRedeemOutput>> VerifyCreateRedeem(MerchantGiftVerifyCreateRedeemInputDto input)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<MerchantGiftVerifyCreateRedeemOutput>>(LoyaltyApiUrl.VERIFY_CREATE_REDEEM, input);
        }

        public async Task<GetMemberCodeOutput> UserVerifying(GetMemberCodeInput input)
        {
            return await PostLoyaltyAsync<GetMemberCodeOutput>(LoyaltyApiUrl.GIFTTRANSACTION_USER_VERIFYING, input);
        }

        public async Task<GiftTransferOutput> GiftTransfer(UpdateGiftTransactionsForTransferInput input)
        {
            return await PostLoyaltyAsync<GiftTransferOutput>(LoyaltyApiUrl.GIFTTRANSACTION_GIFT_TRANSFER, input);
        }

        public async Task<LoyaltyResponseList<MerchantGiftGetAllGiftOutputDto>> GiftListWithoutMemberCode(MerchantGetGiftWithoutMemberCodeInput input)
        {
            // LOGIC CACHE CHỈ ÁP DỤNG CHO CASE QUERY DEFAULT FILTER (trừ cate code)
            bool needCache = !input.FromCointFilter.HasValue && !input.ToCoinFilter.HasValue
                                                             && string.IsNullOrEmpty(input.Filter)
                                                             && input.SkipCount == 0;
            _logger.LogInformation($" LinKID GiftListWithoutMemberCode {input.FullGiftCategoryCodeFilter} >> Start ");

            if (!needCache)
            {
                _logger.LogInformation($" LinKID GiftListWithoutMemberCode {input.FullGiftCategoryCodeFilter} >> Custom filter, cache not work here ");
                return await GetLoyaltyAsync<LoyaltyResponseList<MerchantGiftGetAllGiftOutputDto>>(LoyaltyApiUrl.GET_GIFT_WITHOUT_MEMBER_CODE, input);
            }

            var keyOfCache = "LINKID_DEFAULT_GIFT_LIST_WITHOUT_MEMBERCODE_OF_CATE_" + input.FullGiftCategoryCodeFilter + "_" + input.MaxResultCount;
            var cachedValue = await _cache.GetStringAsync(keyOfCache);
            if (!string.IsNullOrEmpty(cachedValue))
            {
                _logger.LogInformation($" LinKID GiftListWithoutMemberCode {input.FullGiftCategoryCodeFilter} >> Cache hit ");
                try
                {
                    var deserialized = JsonConvert.DeserializeObject<LoyaltyResponseList<MerchantGiftGetAllGiftOutputDto>>(cachedValue);
                    return deserialized;
                }
                catch (Exception)
                {
                    // Not valid String, call to LinkID to get latest data...
                    _logger.LogInformation($" LinKID GiftListWithoutMemberCode {input.FullGiftCategoryCodeFilter} >> Cache hit BUT Invalid data. ");
                }
            }
            _logger.LogInformation($" LinKID GiftListWithoutMemberCode {input.FullGiftCategoryCodeFilter} >> Cache missed ");
            // Call API to LinkID
            var res = await GetLoyaltyAsync<LoyaltyResponseList<MerchantGiftGetAllGiftOutputDto>>(LoyaltyApiUrl.GET_GIFT_WITHOUT_MEMBER_CODE, input);
            // Serialize res and save to cache
            var cacheOption = new DistributedCacheEntryOptions()
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(15)
            };
            _logger.LogInformation($" LinKID GiftListWithoutMemberCode {input.FullGiftCategoryCodeFilter} >> Got res from LinkID and save to cache and return... DONE! ");
            await _cache.SetStringAsync(keyOfCache, JsonConvert.SerializeObject(res), cacheOption);

            return res;
        }

        public async Task<MerchantGiftGetDetailOutputDto> GiftDetailWithoutMemberCode(MerchantGiftGetDetailWithoutMemberCodeInputDto input)
        {
            return await GetLoyaltyAsync<MerchantGiftGetDetailOutputDto>(LoyaltyApiUrl.GET_GIFT_DETAIL_WITHOUT_MEMBER_CODE, input);
        }

        public async Task<MerchantGiftTransactionHistoryOutputDto> TransactionHistoryForCardZone247(MerchantGiftTransactionDetailInputDto input)
        {
            return await GetLoyaltyAsync<MerchantGiftTransactionHistoryOutputDto>(LoyaltyApiUrl.GetAllWithEGiftForCardZone247, input);
        }

        public async Task<RedeemVoucherOutput> UseEVoucherTopup(UseEVoucherTopupInput input)
        {
            return await PostLoyaltyAsync<RedeemVoucherOutput>(LoyaltyApiUrl.EGIFT_INFORS_USE_VOUCHER_TOPUP, input);
        }

        public async Task<RevertVoucherOutput> RevertEVoucherCode(RevertEVoucherInput input)
        {
            return await PostLoyaltyAsync<RevertVoucherOutput>(LoyaltyApiUrl.EGIFT_INFORS_REVERT_VOUCHER_TOPUP, input);
        }

        public async Task<AdminCreateRedeemTransactionResponse> AdminCreateRedeemTransaction(AdminCreateRedeemTransactionRequest input)
        {
            var merchantIdFromGiftCode = await GetMerchantIdFromGiftCode(new LoyaltyGiftGetMerchantIdFromGiftCodeInput()
            {
                GiftCode = input.GiftCode
            });
            _logger.LogInformation($"GetMerchantIdFromGiftCode({input.GiftCode})___Response:{JsonConvert.SerializeObject(merchantIdFromGiftCode)}");
            if (merchantIdFromGiftCode == null || !merchantIdFromGiftCode.Success || merchantIdFromGiftCode.Result == null || !merchantIdFromGiftCode.Result.MerchantId.HasValue)
            {
                CommonHelper.GetErrorValidation("924", "Cannot find merchant");
            }
            var merchantId = merchantIdFromGiftCode.Result.MerchantId.Value;
            //var merchantId = Convert.ToInt32(_configuration.GetSection("LoyaltyLinkID:MerchantIdRedeem").Value);
            var adminMemberId = Convert.ToInt32(_configuration.GetSection("LoyaltyLinkID:LINKID_DefaultMemberId_ToSendGift").Value);
            // Gửi merchant id redeem cho linkid
            var orderCode = input.TransactionCode;
            var requestRedeem = new RewardMemberRedeemInput()
            {
                OrderCode = orderCode,
                MerchantId = merchantId,
                MemberId = adminMemberId,
                TotalRequestedAmount = input.TotalAmount,
            };
            var successPaymentToken = false;
            var errorMessage = string.Empty;
            var result = new AdminCreateRedeemTransactionResponse()
            {
                IsSuccess = false
            };
            try
            {
                var resultRedeemReward = await _rewardMemberService.RedeemTransaction(requestRedeem);
                _logger.LogInformation($"AdminCreateRedeemTransaction_resultRedeemReward:{JsonConvert.SerializeObject(resultRedeemReward)}");
                if (resultRedeemReward.result == 202)
                {
                    successPaymentToken = true;
                    _logger.LogError($"Create redeem reward with status 202 {JsonConvert.SerializeObject(resultRedeemReward)}");
                    CommonHelper.GetErrorValidation("RedeemRewardTransactionStatus202", "Create redeem reward with status 202");
                }
                successPaymentToken = true;

                _logger.LogInformation($"AdminCreateRedeemTransaction_input:{JsonConvert.SerializeObject(input)}");
                result = await _loyaltyUtilsService.AdminCreateRedeemTransaction(input);
                _logger.LogInformation($"AdminCreateRedeemTransaction_result:{JsonConvert.SerializeObject(result)}");

            }
            catch (WebException ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    errorMessage = res.Code;
                }
                await GetErrorFromExeption(ex, input.TransactionCode, successPaymentToken);
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    errorMessage = res.Code;
                }

                await GetErrorFromExeption(ex, input.TransactionCode, successPaymentToken);
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    errorMessage = res.Code;
                }
            }
            result.Exception = !string.IsNullOrEmpty(errorMessage) ? errorMessage : result.Exception;

            return result;
        }

        public async Task<CheckVoucherOutput> CheckEVoucherTopup(CheckVoucherInput input)
        {
            return await PostLoyaltyAsync<CheckVoucherOutput>(LoyaltyApiUrl.EGIFT_INFORS_CHECK_VOUCHER_TOPUP, input);
        }

        public async Task<LoyaltyGiftGetMerchantIdFromGiftCodeOutput> GetMerchantIdFromGiftCode(LoyaltyGiftGetMerchantIdFromGiftCodeInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGiftGetMerchantIdFromGiftCodeOutput>(LoyaltyApiUrl.GET_MERCHANT_ID_FROM_GIFT_CODE, input);
        }

        public async Task<AdminCreateRedeemTransactionResponse> AdminGiveGiftToMember(AdminCreateRedeemTransactionRequest input)
        {
            var merchantIdFromGiftCode = await GetMerchantIdFromGiftCode(new LoyaltyGiftGetMerchantIdFromGiftCodeInput()
            {
                GiftCode = input.GiftCode
            });
            _logger.LogInformation($"GetMerchantIdFromGiftCode({input.GiftCode})___Response:{JsonConvert.SerializeObject(merchantIdFromGiftCode)}");
            if (merchantIdFromGiftCode == null || !merchantIdFromGiftCode.Success || merchantIdFromGiftCode.Result == null || !merchantIdFromGiftCode.Result.MerchantId.HasValue)
            {
                CommonHelper.GetErrorValidation("924", "Cannot find merchant");
            }
            var merchantId = merchantIdFromGiftCode.Result.MerchantId.Value;
            //var merchantId = Convert.ToInt32(_configuration.GetSection("LoyaltyLinkID:MerchantIdRedeem").Value);
            var adminMemberId = Convert.ToInt32(_configuration.GetSection("LoyaltyLinkID:LINKID_DefaultMemberId_ToSendGift").Value);
            // Gửi merchant id redeem cho linkid
            var orderCode = input.TransactionCode;
            var requestRedeem = new RewardMemberRedeemInput()
            {
                OrderCode = orderCode,
                MerchantId = merchantId,
                MemberId = adminMemberId,
                TotalRequestedAmount = input.TotalAmount,
            };
            // Kiểm tra quà bên loyalty và tạo đơn hàng redeem tạm. nếu tạo thành công thì mới process thanh toán (gọi sang reward)
            var checkTransactionPending = await PostLoyaltyAsync<VerifyAndCreateRedeemOrderOutput>(LoyaltyApiUrl.VERIFY_OR_CREATE_REDEEM_ORDER, input);
            if ((!checkTransactionPending.Success || (checkTransactionPending.Success && !checkTransactionPending.Result.IsSuccess)))
            {
                // Can not redeem at this time
                CommonHelper.GetErrorValidation(CommonHelper.GetCodeRedeemFromCode("1022"), CommonHelper.GetMessageRedeemFromCode("1022"));
            }
            var successPaymentToken = false;
            var errorMessage = string.Empty;
            var result = new AdminCreateRedeemTransactionResponse()
            {
                IsSuccess = false
            };
            try
            {
                var resultRedeemReward = await _rewardMemberService.RedeemTransaction(requestRedeem);
                _logger.LogInformation($"AdminGiveGiftToMember_resultRedeemReward:{JsonConvert.SerializeObject(resultRedeemReward)}");
                if (resultRedeemReward.result == 202)
                {
                    successPaymentToken = true;
                    _logger.LogError($"Create redeem reward with status 202 {JsonConvert.SerializeObject(resultRedeemReward)}");
                    CommonHelper.GetErrorValidation("RedeemRewardTransactionStatus202", "Create redeem reward with status 202");
                }
                successPaymentToken = true;

                _logger.LogInformation($"AdminGiveGiftToMember_input:{JsonConvert.SerializeObject(input)}");
                result = await _loyaltyUtilsService.AdminGiveGiftToMember(input);
                _logger.LogInformation($"AdminGiveGiftToMember_result:{JsonConvert.SerializeObject(result)}");

            }
            catch (WebException ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    errorMessage = res.Code;
                }
                await GetErrorFromExeption(ex, input.TransactionCode, successPaymentToken);
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    errorMessage = res.Code;
                }

                await GetErrorFromExeption(ex, input.TransactionCode, successPaymentToken);
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    errorMessage = res.Code;
                }
            }
            result.Exception = !string.IsNullOrEmpty(errorMessage) ? errorMessage : result.Exception;

            return result;
        }

        public async Task<RedeemTransactionWithCashResult> CreateRedeemTransactionWithCash(RedeemTransactionWithCashRequest request)
        {
            string requestId = Guid.NewGuid().ToString("N");

            // Input validation
            if (string.IsNullOrWhiteSpace(request.CifCode))
                return RedeemTransactionWithCashResult.CreateError("902", ResponseCodeConstants.GetResponseMessage("902"), requestId);

            if (string.IsNullOrWhiteSpace(request.GiftCode))
                return RedeemTransactionWithCashResult.CreateError("921", ResponseCodeConstants.GetResponseMessage("921"), requestId);

            if (request.Quantity <= 0)
                return RedeemTransactionWithCashResult.CreateError("922", ResponseCodeConstants.GetResponseMessage("922"), requestId);

            if (request.TotalAmount <= 0)
                return RedeemTransactionWithCashResult.CreateError("923", ResponseCodeConstants.GetResponseMessage("923"), requestId);

            // Chạy song song 2 task độc lập để tối ưu performance với exception handling
            _logger.LogInformation($"CreateRedeemTransactionWithCash___Steep1___MerchantId___Input({requestId}):{request.GiftCode}");
            var merchantIdTask = GetMerchantIdFromGiftCode(new LoyaltyGiftGetMerchantIdFromGiftCodeInput()
            {
                GiftCode = request.GiftCode,
            });

            _logger.LogInformation($"CreateRedeemTransactionWithCash___Steep1___Member___Input({requestId}):{request.CifCode}");
            var memberTask = _merchantGiftService.Value.CheckAndGetMemberInfo(new GetLinkIdMemberByCifCodeInput
            {
                CifCode = request.CifCode
            });

            // Chờ cả 2 task hoàn thành SONG SONG và xử lý exception
            LoyaltyGiftGetMerchantIdFromGiftCodeOutput merchantIdFromGiftCode = null;
            CheckAndGetMemberInfoOutput member = null;
            Exception merchantIdException = null;
            Exception memberException = null;

            try
            {
                // Task.WhenAll chạy song song và chờ cả 2 hoàn thành
                await Task.WhenAll(merchantIdTask, memberTask);

                // Lấy kết quả sau khi cả 2 đã hoàn thành
                merchantIdFromGiftCode = merchantIdTask.Result;
                member = memberTask.Result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"CreateRedeemTransactionWithCash___Steep1___Error({requestId})({request.CifCode}-{request.GiftCode}):{ex.Message}-{ex.StackTrace}");

                // Kiểm tra từng task để xem task nào bị exception
                if (merchantIdTask.IsFaulted)
                {
                    merchantIdException = merchantIdTask.Exception?.GetBaseException();
                    _logger.LogError($"CreateRedeemTransactionWithCash___Steep1___MerchantId___Error({requestId})({request.GiftCode}): {ex.Message}-{ex.StackTrace}");
                }
                else if (merchantIdTask.IsCompletedSuccessfully)
                {
                    merchantIdFromGiftCode = merchantIdTask.Result;
                }

                if (memberTask.IsFaulted)
                {
                    memberException = memberTask.Exception?.GetBaseException();
                    _logger.LogError($"CreateRedeemTransactionWithCash___Steep1___Member___Error({requestId})({request.CifCode}): {ex.Message}-{ex.StackTrace}");
                }
                else if (memberTask.IsCompletedSuccessfully)
                {
                    member = memberTask.Result;
                }
            }

            // Validate kết quả merchantIdFromGiftCode
            if (merchantIdException != null || merchantIdFromGiftCode == null || !merchantIdFromGiftCode.Success ||
                merchantIdFromGiftCode.Result == null || !merchantIdFromGiftCode.Result.MerchantId.HasValue)
            {
                return RedeemTransactionWithCashResult.CreateError("5000", ResponseCodeConstants.GetResponseMessage("5000"), requestId);
            }

            // Validate kết quả member
            if (memberException != null || member == null || string.IsNullOrWhiteSpace(member.NationalId))
            {
                return RedeemTransactionWithCashResult.CreateError("1018", ResponseCodeConstants.GetResponseMessage("1018"), requestId);
            }
            var orderCode = LoyaltyHelper.GenTransactionCode($"{requestId}{request.CifCode}{DateTime.UtcNow.Ticks}");
            VerifyAndCreateRedeemOrderOutput resultCreateGiftRedeem = null;

            var inputCreateRedeem = new MerchantGiftCreateRedeemInputDto()
            {
                MemberCode = member.NationalId,
                GiftCode = request.GiftCode,
                MerchantIdRedeem = merchantIdFromGiftCode.Result.MerchantId,
                Quantity = request.Quantity,
                TotalAmount = request.TotalAmount,
                RedeemSource = "Vpbank",
                Date = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                Description = request.Description,
                TransactionCode = orderCode
            };
            try
            {
                _logger.LogInformation($"CreateRedeemTransactionWithCash___Steep2___CreateGiftRedeem___Input({requestId}):{JsonConvert.SerializeObject(inputCreateRedeem)}");
                resultCreateGiftRedeem = await PostLoyaltyAsyncV2<VerifyAndCreateRedeemOrderOutput>(LoyaltyApiUrl.VERIFY_OR_CREATE_REDEEM_ORDER, inputCreateRedeem);
                _logger.LogInformation($"CreateRedeemTransactionWithCash___Steep2___CreateGiftRedeem___Result({requestId}):{JsonConvert.SerializeObject(resultCreateGiftRedeem)}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"CreateRedeemTransactionWithCash___Steep2___CreateGiftRedeem___Error({requestId}):{ex.Message}-{ex.StackTrace}");
                return RedeemTransactionWithCashResult.CreateError("1022", ResponseCodeConstants.GetResponseMessage("1022"), requestId);
            }

            if (resultCreateGiftRedeem == null || !resultCreateGiftRedeem.Success || (resultCreateGiftRedeem.Success && !resultCreateGiftRedeem.Result.IsSuccess))
            {
                return RedeemTransactionWithCashResult.CreateError("1022", ResponseCodeConstants.GetResponseMessage("1022"), requestId);
            }

            RewardMemberRedeemOutput resultRedeemReward = null;
            bool successPaymentToken = false;
            try
            {
                var requestRedeem = new RewardMemberRedeemInput()
                {
                    OrderCode = orderCode,
                    MerchantId = merchantIdFromGiftCode.Result.MerchantId.Value,
                    MemberId = member.LinkID_MemberID,
                    TotalRequestedAmount = request.TotalAmount
                };
                _logger.LogInformation($"CreateRedeemTransactionWithCash___Steep3___RedeemTransaction___Input({requestId}):{JsonConvert.SerializeObject(requestRedeem)}");
                resultRedeemReward = await _rewardMemberService.RedeemTransaction(requestRedeem);
                _logger.LogInformation($"CreateRedeemTransactionWithCash___Steep3___RedeemTransaction___Result({requestId}):{JsonConvert.SerializeObject(resultRedeemReward)}");
            }
            catch (TimeoutException ex)
            {
                _logger.LogError($"CreateRedeemTransactionWithCash___Steep3___RedeemTransaction___Timeout({requestId}):{ex.Message}-{ex.StackTrace}");
                return RedeemTransactionWithCashResult.CreateError("RWTO001", ResponseCodeConstants.GetResponseMessage("RWTO001"), requestId);
            }
            catch (Exception ex)
            {
                _logger.LogError($"CreateRedeemTransactionWithCash___Steep3___RedeemTransaction___Error({requestId}):{ex.Message}-{ex.StackTrace}");
                return RedeemTransactionWithCashResult.CreateError("1022", ResponseCodeConstants.GetResponseMessage("1022"), requestId);
            }

            if (resultRedeemReward == null || resultRedeemReward.result == 202)
            {
                successPaymentToken = true;
                return RedeemTransactionWithCashResult.CreateError("1022", ResponseCodeConstants.GetResponseMessage("1022"), requestId);
            }

            RedeemTransactionWithCashResponse resultGiftRedeem = null;
            try
            {
                _logger.LogInformation($"CreateRedeemTransactionWithCash___Steep4___GiftRedeemTransaction___Input({requestId}):{JsonConvert.SerializeObject(inputCreateRedeem)}");
                resultGiftRedeem = await PostLoyaltyAsyncV2<RedeemTransactionWithCashResponse>(LoyaltyApiUrl.MERCHANT_GIFT_CREATE_REDEEM, inputCreateRedeem);
                _logger.LogInformation($"CreateRedeemTransactionWithCash___Steep4___GiftRedeemTransaction___Result({requestId}):{JsonConvert.SerializeObject(resultGiftRedeem)}");
            }
            catch (LoyaltyTimeoutException ex)
            {
                _logger.LogError(ex, $"CreateRedeemTransactionWithCash___Steep4___Timeout({requestId})({request.CifCode}-{request.GiftCode}): API {ex.ApiUrl} timed out after {ex.Timeout?.TotalSeconds} seconds");
                await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                {
                    TransactionCode = orderCode,
                    ErrorCode = "LOYTO001",
                    ErrorMessage = ResponseCodeConstants.GetResponseMessage("LOYTO001")
                }, successPaymentToken);
                return RedeemTransactionWithCashResult.CreateError("LOYTO001", ResponseCodeConstants.GetResponseMessage("LOYTO001"), requestId);
            }
            catch (LoyaltyGatewayTimeoutException ex)
            {
                _logger.LogError(ex, $"CreateRedeemTransactionWithCash___Steep4___GatewayTimeout({requestId})({request.CifCode}-{request.GiftCode}): Gateway timeout for API {ex.ApiUrl}");
                await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                {
                    TransactionCode = orderCode,
                    ErrorCode = "LOYTO001",
                    ErrorMessage = ResponseCodeConstants.GetResponseMessage("LOYTO001")
                }, successPaymentToken);
                return RedeemTransactionWithCashResult.CreateError("LOYTO001", ResponseCodeConstants.GetResponseMessage("LOYTO001"), requestId);
            }
            catch (LoyaltyCancelledException ex)
            {
                _logger.LogError(ex, $"CreateRedeemTransactionWithCash___Steep4___Cancelled({requestId})({request.CifCode}-{request.GiftCode}): Request to API {ex.ApiUrl} was cancelled");
                await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                {
                    TransactionCode = orderCode,
                    ErrorCode = "LOYTO001",
                    ErrorMessage = ResponseCodeConstants.GetResponseMessage("LOYTO001")
                }, successPaymentToken);
                return RedeemTransactionWithCashResult.CreateError("LOYTO001", ResponseCodeConstants.GetResponseMessage("LOYTO001"), requestId);
            }
            catch (LoyaltyBadRequestException ex)
            {
                _logger.LogError(ex, $"CreateRedeemTransactionWithCash___Steep4___BadRequest({requestId})({request.CifCode}-{request.GiftCode}): Bad request for API {ex.ApiUrl}: {ex.ResponseContent}");
                await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                {
                    TransactionCode = orderCode,
                    ErrorCode = "LOY002",
                    ErrorMessage = ResponseCodeConstants.GetResponseMessage("LOY002")
                }, successPaymentToken);
                return RedeemTransactionWithCashResult.CreateError("LOY002", ResponseCodeConstants.GetResponseMessage("LOY002"), requestId);
            }
            catch (LoyaltyUnauthorizedException ex)
            {
                _logger.LogError(ex, $"CreateRedeemTransactionWithCash___Steep4___Unauthorized({requestId})({request.CifCode}-{request.GiftCode}): Unauthorized access to API {ex.ApiUrl}: {ex.ResponseContent}");
                await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                {
                    TransactionCode = orderCode,
                    ErrorCode = "LOY001",
                    ErrorMessage = ResponseCodeConstants.GetResponseMessage("LOY001")
                }, successPaymentToken);
                return RedeemTransactionWithCashResult.CreateError("LOY001", ResponseCodeConstants.GetResponseMessage("LOY001"), requestId);
            }
            catch (LoyaltyHttpException ex)
            {
                _logger.LogError(ex, $"CreateRedeemTransactionWithCash___Steep4___HttpError({requestId})({request.CifCode}-{request.GiftCode}): HTTP {(int)ex.StatusCode} error for API {ex.ApiUrl}: {ex.ResponseContent}");
                await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                {
                    TransactionCode = orderCode,
                    ErrorCode = "LOY001",
                    ErrorMessage = ResponseCodeConstants.GetResponseMessage("LOY001")
                }, successPaymentToken);
                return RedeemTransactionWithCashResult.CreateError("LOY001", ResponseCodeConstants.GetResponseMessage("LOY001"), requestId);
            }
            catch (LoyaltyNetworkException ex)
            {
                _logger.LogError(ex, $"CreateRedeemTransactionWithCash___Steep4___NetworkError({requestId})({request.CifCode}-{request.GiftCode}): Network error for API {ex.ApiUrl}: {ex.Message}");
                await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                {
                    TransactionCode = orderCode,
                    ErrorCode = "RWTO001",
                    ErrorMessage = ResponseCodeConstants.GetResponseMessage("RWTO001")
                }, successPaymentToken);
                return RedeemTransactionWithCashResult.CreateError("RWTO001", ResponseCodeConstants.GetResponseMessage("RWTO001"), requestId);
            }
            catch (LoyaltyException ex)
            {
                _logger.LogError(ex, $"CreateRedeemTransactionWithCash___Steep4___LoyaltyError({requestId})({request.CifCode}-{request.GiftCode}): Loyalty error: {ex.Message}");
                await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                {
                    TransactionCode = orderCode,
                    ErrorCode = "RW001",
                    ErrorMessage = ResponseCodeConstants.GetResponseMessage("RW001")
                }, successPaymentToken);
                return RedeemTransactionWithCashResult.CreateError("RW001", ResponseCodeConstants.GetResponseMessage("RW001"), requestId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"CreateRedeemTransactionWithCash___Steep4___UnexpectedError({requestId})({request.CifCode}-{request.GiftCode}): Unexpected error: {ex.Message}");
                await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                {
                    TransactionCode = orderCode,
                    ErrorCode = "100",
                    ErrorMessage = ResponseCodeConstants.GetResponseMessage("100")
                }, successPaymentToken);
                return RedeemTransactionWithCashResult.CreateError("100", ResponseCodeConstants.GetResponseMessage("100"), requestId);
            }

            if (resultGiftRedeem == null || !resultGiftRedeem.Success)
            {
                return RedeemTransactionWithCashResult.CreateError("1022", ResponseCodeConstants.GetResponseMessage("1022"), requestId);
            }

            if (resultGiftRedeem.Result != null && resultGiftRedeem.Result.Timeout)
            {
                return RedeemTransactionWithCashResult.CreateError("LOYTO001", ResponseCodeConstants.GetResponseMessage("LOYTO001"), requestId);
            }

            if (!string.IsNullOrWhiteSpace(resultGiftRedeem.Result.Exception) || !resultGiftRedeem.Result.SuccessedRedeem)
            {
                await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                {
                    TransactionCode = orderCode,
                    ErrorCode = resultGiftRedeem.Result.Exception,
                    ErrorMessage = resultGiftRedeem.Result.Messages
                }, successPaymentToken);

                var errorCode = CommonHelper.GetCodeRedeemFromCode(resultGiftRedeem.Result.Exception);
                var errorMessage = CommonHelper.GetMessageRedeemFromCode(resultGiftRedeem.Result.Exception);
                return RedeemTransactionWithCashResult.CreateError(errorCode, errorMessage, requestId);
            }

            return RedeemTransactionWithCashResult.CreateSuccess(resultGiftRedeem, requestId);
        }
    }   
}
