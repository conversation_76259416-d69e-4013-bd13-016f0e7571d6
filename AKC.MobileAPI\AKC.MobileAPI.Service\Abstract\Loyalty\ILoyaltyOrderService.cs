﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Order;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyOrderService
    {
        Task<LoyaltyPurchaseAgentOutput> PurchaseAgent(LoyaltyPurchaseAgentInput input);

        Task<LoyaltyResponse<string>> UsePoint(LoyaltyUsePointInput input);
    }
}
