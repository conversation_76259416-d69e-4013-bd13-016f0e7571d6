﻿using AKC.MobileAPI.DTO.ApiSMS;
using AKC.MobileAPI.DTO.User;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract
{
    public interface IUserService
    {
        //Task<LoyaltyMemberDTO> CreateUser(CreateUserDTO createUserDTO);
        //Task<ResponseApiSMSModel> GetOTP(ValidateUserInput createUserDTO);
        Task<GetOTPOutput> GetOTP(ValidateUserInput createUserDTO);
        Task<VerifyOTPOutput> VerifyOTPAndLogin(SendOTPUserInput input);
        Task<VerifyOTPOutput> VerifyOTPAndRegister(CreateUserMobileDTO input);

        Task<ValidateUserOutput> IsExisted(ValidateUserInput model);
    }
}
