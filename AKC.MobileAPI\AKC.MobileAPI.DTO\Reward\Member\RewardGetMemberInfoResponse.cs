﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardGetMemberInfoResponse
    {
        public int Id { get; set; }
        public string UserAddress { get; set; }
        public string Status { get; set; }
        public string Type { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string PhoneNumber { get; set; }
        public string PartnerPhoneNumber { get; set; }
        public DateTime? Dob { get; set; }
        public string NationalId { get; set; }
        public string PointUsingOrdinary { get; set; }
        public string Gender { get; set; }
        public string Email { get; set; }
        public string HashAddress { get; set; }
        public string RegionCode { get; set; }
        public string FullRegionCode { get; set; }
        public string MemberTypeCode { get; set; }
        public string FullMemberTypeCode { get; set; }
        public string ChannelType { get; set; }
        public string FullChannelTypeCode { get; set; }
        public string RankTypeCode { get; set; }
        public string StandardMemberCode { get; set; }
        public decimal Balance { get; set; }
        public string ReferralCode { get; set; }
        public DateTime? LastCashoutTime { get; set; }
        public string IdCard { get; set; }
    }
}
