﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Runtime.Intrinsics.X86;
using System.Text;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.BillPayment;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.MobileAPI;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Reward
{
    [Route("api/Member")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class RewardMemberController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IRewardMemberService _memberService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILoyaltySecondaryCustomersService _loyaltySecondaryCustomersService;
        private readonly ILoyaltyMemberService _loyaltyMemberService;
        private readonly IConfiguration _configuration;
        private readonly ILoyaltyBillPaymentService _loyaltyBillPaymentService;
        private readonly ILoyaltyUtilsService _loyaltyUtilsService;
        private readonly IDistributedCache _cache;
        private int vpbankMerId = 0;
        public RewardMemberController(
            ILogger<RewardMemberController> logger,
            IRewardMemberService memberService,
            IConfiguration configuration,
            IExceptionReponseService exceptionReponseService,
            ILoyaltySecondaryCustomersService loyaltySecondaryCustomersService,
            ILoyaltyBillPaymentService loyaltyBillPaymentService,
            ILoyaltyUtilsService xt,
            IDistributedCache cache,
            ILoyaltyMemberService loyaltyMemberService)
        {
            _loyaltyUtilsService = xt;
            _logger = logger;
            _configuration = configuration;
            _memberService = memberService;
            _exceptionReponseService = exceptionReponseService;
            _loyaltySecondaryCustomersService = loyaltySecondaryCustomersService;
            _loyaltyBillPaymentService = loyaltyBillPaymentService;
            _loyaltyMemberService = loyaltyMemberService;
            vpbankMerId = Convert.ToInt32(_configuration.GetSection("RewardVPBank:MerchantId").Value);
            _cache = cache;
        }

        //[HttpGet]
        //[Route("View")]
        //public async Task<ActionResult<RewardMemberViewOutput>> View([FromQuery] RewardMemberRequestInput request)
        //{
        //    try
        //    {
        //        var authorization = Request.Headers["Authorization"].ToString();
        //        var result = await _memberService.View(request, authorization);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //        _logger.LogError(ex, "View Member Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        //[HttpPost]
        //[Route("Update")]
        //public async Task<ActionResult<RewardMemberUpdateOutput>> Update(RewardMemberUpdateInput input)
        //{
        //    try
        //    {
        //        var result = await _memberService.Update(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //        _logger.LogError(ex, "Update Member User Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        [HttpGet]
        [Route("ViewPoint")]
        public async Task<ActionResult<LoyaltyResponse<LoyaltyRewardMemberViewPoint>>> ViewPoint([FromQuery] RewardMemberRequestInput request)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                _logger.LogInformation(" >> ViewPoint >> " + request.MemberCode);
                var result = await _memberService.ViewPoint(request, authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "View point Member User Error - " + request.MemberCode + " - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }
        
        [HttpGet]
        [Route("ViewBalanceWithExpiringCoins")]
        public async Task<ActionResult<LoyaltyResponse<ViewBalanceWithExpiringCoinOutput>>>
            ViewBalanceWithExpiringCoins([FromQuery] ViewBalanceWithExpiringCoinInput request)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var result = await _memberService.ViewBalanceWithExpiringCoins(request, authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "View point Member User Error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }
        [HttpGet]
        [Route("GetCreditBalance")]
        public async Task<ActionResult<LoyaltyResponse<LoyaltyRewardMemberViewCreditBalance>>> GetCreditBalance([FromQuery] RewardViewCreditBalanceInput request)
        {
            try
            {
                _logger.LogInformation("GetCreditBalance >> get value of " + request.WalletAddress);
                var result = await _memberService.ViewCreditBalance(request);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetCreditBalance - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        /// <summary>
        /// Tính năng cho portal 247 gỡ kết nối của member và merchant vpbank
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("AdminRemoveMemberConnection")]
        public async Task<ActionResult<LoyaltyResponse<LoyaltyRewardMemberViewCreditBalance>>> AdminRemoveMemberConnection([FromBody] RewardRemoveConnectionInput request)
        {
            try
            {
                _logger.LogInformation("AdminRemoveMemberConnection >> body " + JsonConvert.SerializeObject(request));
                var result = await _memberService.RemoveConnection(request);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AdminRemoveMemberConnection - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }
        
        /**
         * API cho bên portal247 gọi sang. Ko liên quan API cho NEO.
         * LINKID-185
         */
        // Phiên bản 2022. It works, ko đụng.
        [HttpPost]
        [Route("AutoConnectMember")]
        public async Task<ActionResult<RewardMemberSendOtpForConnectMerchantOutput>> AutoConnectMember([FromBody] RewardMemberAutoConnectMemberInput input)
        {
            try
            {
                var result = await _memberService.AutoConnectMember(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AutoConnectMember error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }
        
        /**
         * Phiên bản 2023, cần add cho flow của NEO, LINKID-794
         * Xử lý tự động liên kết cho Cif mà có UserMapping.Status = 0
         */
        [HttpPost]
        [Route("AutoConnectMember2")]
        public async Task<ActionResult<RewardMemberSendOtpForConnectMerchantOutput>> AutoConnectMember2([FromBody] RewardMemberAutoConnectMemberInput input)
        {
            try
            {
                var result = await _memberService.AutoConnectMember2(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AutoConnectMember2 error - " + ex.Message + " - " + ex.StackTrace);
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    _logger.LogError(ex, "AutoConnectMember2 error RewardException - " + JsonConvert.SerializeObject(res));
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    _logger.LogError(ex, "AutoConnectMember2 error LoyaltyException - " + JsonConvert.SerializeObject(res));
                    return StatusCode(400, res);
                }
            }
        }
        /**
         * Version 2024 - Tich hop voi MasterCard. Có nhận thêm Prefix làm tiền tố memcode, trả về membercode  so với bản V3 
         */
        [HttpPost]
        [Route("AutoConnectMember3MasterCard")]
        public async Task<ActionResult<RewardMemberAutoConnectMember3Output>> AutoConnectMember3MasterCard([FromBody] RewardMemberAutoConnectMember3Input input)
        {
            try
            {
                var result = await _memberService.AutoConnectMember3MasterCard(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AutoConnectMember3MasterCard error - " + ex.Message + " - " + ex.StackTrace);
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    _logger.LogError(ex, "AutoConnectMember3MasterCard error RewardException - " + JsonConvert.SerializeObject(res));
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    _logger.LogError(ex, "AutoConnectMember3MasterCard error LoyaltyException - " + JsonConvert.SerializeObject(res));
                    return StatusCode(400, res);
                }
            }
        }

        
        [HttpPost]
        [Route("ConnectMerchant")]
        public async Task<ActionResult<RewardMemberSendOtpForConnectMerchantOutput>> SendOtpForConnectMerchant([FromBody] RewardMemberSendOtpForConnectMerchantInput input)
        {
            try
            {
                var result = await _memberService.SendOtpForConnectMerchant(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Send otp for connect merchant error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        /**
         * Truyền CIF vào, sẽ nhận ra basic info của member ở loyalty
         * Kèm trạng thái kết nối với LinkID (integer constants)
         * Và List lịch sử kết nối
         */
        [HttpGet]
        [Route("GetMemberInfoByCif")]
        public async Task<ActionResult<GetMemberInfoByCifOutput>> GetMemberInfoByCif([FromQuery] GetMemberInfoByCifInput input)
        {
            try
            {
                var result = await _loyaltyUtilsService.GetMemberInfoByCif(input);
                var statusCode = 200;
                if (!result.IsSuccess)
                {
                    statusCode = 400;
                }
                return StatusCode(statusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetMemberInfoByCif error - " + JsonConvert.SerializeObject(ex));
                if (ex.Data != null && ex.Data.Contains("ErrorData"))
                {
                    var errorDataString = ex.Data["ErrorData"].ToString();
                    try
                    {
                        var dictionary = JsonConvert.DeserializeObject<IDictionary<string, object>>(errorDataString);
                        if (dictionary != null && dictionary.ContainsKey("errorCode"))
                        {
                            var errorCode = dictionary["errorCode"].ToString();
                            var res = new GetMemberInfoByCifOutput()
                            {
                                IsSuccess = false, ErrorCode = errorCode,
                            };
                            return StatusCode(400, res);
                        }
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(" >> GetMemberInfoByCif - Error happened when parsing field ErrorData; Cif = " + input.Cif + " _ " + e.StackTrace);
                    }
                }
                var ret = new GetMemberInfoByCifOutput
                {
                    IsSuccess = false,
                    ErrorCode = "ServerError",
                    ErrorMessage = ""
                };
                return StatusCode(400, ret);
            }
        }
        /***
         * GetMemberInfoByCif version for Adapter (Add more field to response)
         */
        [HttpGet]
        [Route("GetMemberInfoByCifForAdapter")]
        public async Task<ActionResult<GetMemberInfoByCifForAdapterOutput>> GetMemberInfoByCifForAdapter([FromQuery] GetMemberInfoByCifForAdapterInput input)
        {
            try
            {
                var result = await _loyaltyUtilsService.GetMemberInfoByCifForAdapter(input);
                var statusCode = 200;
                if (!result.IsSuccess)
                {
                    statusCode = 400;
                }

                if (result.IsSuccess && result.CurrentLinkIdMemberId > 0
                                     && !string.IsNullOrEmpty(result.CurrentLinkIdWalletAddress))
                {
                    // Check if local cache has data of this walletaddress
                    var cacheKey = "MEMBER_" + result.CurrentLinkIdWalletAddress;
                    var memCode = await _cache.GetStringAsync(cacheKey);
                    if (string.IsNullOrEmpty(memCode))
                    {
                        // Call leen operator get memcode, and then 
                        var memobj = await _memberService.GetInfo(new RewardMemberGetInfoInput()
                        {
                            MemberId = result.CurrentLinkIdMemberId
                        });
                        if (memobj != null)
                        {
                            _logger.LogInformation(" >> GetMemberInfoByCifForAdapter >> get from operator >> " + memobj.NationalId);
                            result.CurrentLinkIdMemberCode = memobj.NationalId;
                            // set to cache
                            await _cache.SetStringAsync(cacheKey, result.CurrentLinkIdMemberCode, new DistributedCacheEntryOptions()
                                .SetAbsoluteExpiration(TimeSpan.FromHours(24)));
                        }
                    }
                    else
                    {
                        result.CurrentLinkIdMemberCode = memCode;
                    }
                }
                _logger.LogInformation(" >> Output GetMemberInfoByCifForAdapter" + JsonConvert.SerializeObject(result));
                return StatusCode(statusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetMemberInfoByCif error - " + JsonConvert.SerializeObject(ex));
                if (ex.Data != null && ex.Data.Contains("ErrorData"))
                {
                    var errorDataString = ex.Data["ErrorData"].ToString();
                    try
                    {
                        var dictionary = JsonConvert.DeserializeObject<IDictionary<string, object>>(errorDataString);
                        if (dictionary != null && dictionary.ContainsKey("errorCode"))
                        {
                            var errorCode = dictionary["errorCode"].ToString();
                            var res = new GetMemberInfoByCifOutput()
                            {
                                IsSuccess = false, ErrorCode = errorCode,
                            };
                            return StatusCode(400, res);
                        }
                    }
                    catch (Exception e)
                    {
                        // Do nothing
                    }
                }
                var ret = new GetMemberInfoByCifOutput
                {
                    IsSuccess = false,
                    ErrorCode = "ServerError",
                    ErrorMessage = ""
                };
                return StatusCode(400, ret);
            }
        }
        
        [HttpPost]
        [Route("UpdateLoyaltyMemberData")]
        public async Task<ActionResult<UpdateMemberFromAdapterOutput>> UpdateMemberFromAdapter(UpdateMemberFromAdapterInput input)
        {
            try
            {
                var result = await _loyaltyUtilsService.UpdateMemberFromAdapter(input);
                var statusCode = 200;
                if (!result.IsSuccess)
                {
                    statusCode = 400;
                }
                return StatusCode(statusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetMemberInfoByCif error - " + JsonConvert.SerializeObject(ex));
                if (ex.Data != null && ex.Data.Contains("ErrorData"))
                {
                    var errorDataString = ex.Data["ErrorData"].ToString();
                    try
                    {
                        var dictionary = JsonConvert.DeserializeObject<IDictionary<string, object>>(errorDataString);
                        if (dictionary != null && dictionary.ContainsKey("errorCode"))
                        {
                            var errorCode = dictionary["errorCode"].ToString();
                            var res = new GetMemberInfoByCifOutput()
                            {
                                IsSuccess = false, ErrorCode = errorCode,
                            };
                            return StatusCode(400, res);
                        }
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(" >> UpdateMemberFromAdapter - Error happened when parsing field ErrorData; Cif = " + input.CifCode
                          + " - "   + e.StackTrace);
                    }
                }
                var ret = new GetMemberInfoByCifOutput
                {
                    IsSuccess = false,
                    ErrorCode = "ServerError",
                    ErrorMessage = ""
                };
                return StatusCode(400, ret);
            }
        }
        private string GenerateMemberCode()
        {
            var ret = "AUTO";
            var guid = Guid.NewGuid();
            ret += guid.ToString().Replace("-", "");
            return ret.ToUpper();
        }


        [HttpPost]
        [Route("VerifyOtpForConnectMerchant")]
        public async Task<ActionResult<RewardMemberVerifyOtpForConnectMerchantOutput>> VerifyOtpForConnectMerchant([FromBody] RewardMemberVerifyOtpForConnectMerchantInput input)
        {
            try
            {
                var result = await _memberService.VerifyOtpForConnectMerchant(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Verify otp for connect merchant error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("ConfirmConnect")]
        public async Task<ActionResult<RewardMemberConfirmConnectOutput>> ConfirmConnect(RewardMemberConfirmConnectInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var result = await _memberService.ConfirmConnect(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    _logger.LogError(ex, "Confirm connect error - " + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    _logger.LogError(ex, "Confirm connect error - " + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("CheckBalance")]
        public async Task<ActionResult<LoyaltyResponse<BillPaymentCheckBalanceOutput>>> CheckBalance(BillPaymentCheckBalanceInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var result = await _loyaltyBillPaymentService.CheckBalance(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Check balance error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("ConfirmUsingToken")]
        public async Task<ActionResult<LoyaltyResponse<string>>> ConfirmUsingToken(BillPaymentConfirmUsingTokenInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var result = await _loyaltyBillPaymentService.ComfirmUsingToken(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Confirm using token error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("RevertToken")]
        public async Task<ActionResult<LoyaltyResponse<string>>> RevertToken(BillPaymentRevertTokenInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var result = await _loyaltyBillPaymentService.RevertToken(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Revert token error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("GetCifByPhoneNumber")]
        public async Task<ActionResult<RewardMemberGetCifByPhoneNumberOutput>> GetCifByPhoneNumber([FromBody] RewardMemberGetCifByPhoneNumberInput input)
        {
            try
            {
                var res = await _memberService.GetCifByPhoneNumber(input);
                return StatusCode(200, res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetCifByPhoneNumber error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("sendOtpForConnectMerchantView")]
        public async Task<ActionResult<RewardMemberVerifyOtpMerchantViewOutput>> sendOtpForConnectMerchantView([FromBody] RewardMemberSendOtpForConnectMerchantViewInput input)
        {
            try
            {
                var res = await _memberService.sendOtpForConnectMerchantView(input);
                return StatusCode(200, res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "sendOtpForConnectMerchantView error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("verifyOtpForConnectMerchantView")]
        public async Task<ActionResult<RewardMemberGetCifByPhoneNumberOutput>> verifyOtpForConnectMerchantView([FromBody] RewardMemberVerifyOtpMerchantViewInput input)
        {
            try
            {
                var res = await _memberService.verifyOtpForConnectMerchantView(input);
                return StatusCode(200, res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "verifyOtpForConnectMerchantView error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        //[HttpPost]
        //[Route("Create")]
        //public async Task<ActionResult<RewardMemberVerifyOrCreateOutput>> Create(RewardMemberCreateInput input)
        //{
        //    try
        //    {
        //        var authorization = Request.Headers["Authorization"].ToString();
        //        var result = await _memberService.Create(input, authorization);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //        _logger.LogError(ex, "Create Member User Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        //[HttpPost]
        //[Route("CreateRegisterLog")]
        //[AllowAnonymous]
        //public async Task<ActionResult<RewardMemberCreateRegisterLogOutput>> CreateRegisterLog(RewardMemberCreateRegisterLog input)
        //{
        //    try
        //    {
        //        var secretKey = _configuration.GetSection("AES:SecretKey").Value;
        //        var rawData = AESEncrytDecry.DecryptStringAES(input.Data, secretKey);

        //        if (rawData != "keyError")
        //        {
        //            var dto = JsonConvert.DeserializeObject<RewardMemberCreateRegisterLogInput>(rawData);
        //            var result = await _memberService.CreateRegisterLog(dto);
        //            return StatusCode(200, result);
        //        }

        //        return StatusCode(400, new Exception("Invalid request"));
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //        _logger.LogError(ex, "Create register member log - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}


        //[HttpPost]
        //[Route("VerifyReferralCode")]
        //public async Task<ActionResult<LoyaltyVerifyReferralCodeOutputDto>> VerifyReferralCode(LoyaltyVerifyReferralCodeInput input)
        //{
        //    try
        //    {
        //        var result = await _loyaltySecondaryCustomersService.VerifyReferralCode(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //        _logger.LogError(ex, "Verify referral code error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        //[HttpPost]
        //[Route("CreateOrUpdatePinCode")]
        //public async Task<ActionResult<RewardMemberCreateOrUpdatePinCodeResponse>> CreateOrUpdatePinCode(MobileAPICreateOrUpdatePinCodeInput input)
        //{
        //    try
        //    {
        //        var result = await _memberService.CreateOrUpdatePinCode(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //        _logger.LogError(ex, "CreateOrUpdatePinCode Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        //[HttpGet]
        //[Route("HasPinCode")]
        //public async Task<ActionResult<RewardMemberHasPinCodeResponse>> HasPinCode(RewardMemberHasPinCodeRequest input)
        //{
        //    try
        //    {
        //        var result = await _memberService.HasPinCode(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //        _logger.LogError(ex, "HasPinCode Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        //[HttpPost]
        //[AllowAnonymous]
        //[Route("VerifyPinCode")]
        //public async Task<ActionResult<RewardMemberVerifyPinCodeResponse>> VerifyPinCode(RewardMemberVerifyPinCodeRequest input)
        //{
        //    try
        //    {
        //        var result = await _memberService.VerifyPinCode(input);

        //        if (!result.Success)
        //        {
        //            return StatusCode(400, new RewardErrorResponse()
        //            {
        //                Result = 400,
        //                Code = "CanNotVerifyPinCodeNow",
        //                Message = "Can Not Verify PinCode Now"
        //            });
        //        }

        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //        _logger.LogError(ex, "Verify PinCode Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        //[HttpPost]
        //[AllowAnonymous]
        //[Route("CheckReferralCodeExistance")]
        //public async Task<ActionResult<LoyaltyMemberCheckReferralCodeExistanceOutput>> CheckReferralCodeExistance(LoyaltyMemberCheckReferralCodeExistanceInput input)
        //{
        //    try
        //    {
        //        var result = await _loyaltySecondaryCustomersService.CheckReferralCodeExistance(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //        _logger.LogError(ex, "Check ReferralCode Existance - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        //[HttpGet]
        //[Route("TempPointTrans/GetByMemberId")]
        //public async Task<ActionResult<RewardMemberTempPointTransGetByIdOutput>> GetTempPointTransByMemberId([FromQuery] RewardMemberTempPointTransGetByIdInput input)
        //{
        //    try
        //    {
        //        var result = await _memberService.GetTempPointTransByMemberId(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //        _logger.LogError(ex, "GetAllByMemberID error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        [HttpGet]
        [Route("Transaction/GetHistory")]
        public async Task<ActionResult<LoyaltyResponse<GetTransactionHistoryOutput>>> GetHistory([FromQuery] GetTransactionHistoryInput input)
        {
            try
            {
                var result = await _loyaltyMemberService.GetHistory(input);
                if (result != null)
                {
                    var list = result.Result?.Items ?? new List<TransactionForView>();
                    list.ForEach(x =>
                    {
                        if (x.Transaction != null)
                        {
                            var actionType = x.Transaction.ActionType;
                            var descriptionEn = ActionTypeToMessageUtils.GetActionTypeDescription(actionType, "EN");
                            var descriptionVi = ActionTypeToMessageUtils.GetActionTypeDescription(actionType, "VI");
                            var desc = new Dictionary<string, string>()
                            {
                                { "EN", descriptionEn },
                                { "VI", descriptionVi },
                            };
                            x.Transaction.ActionTypeDescription = JsonConvert.SerializeObject(desc);
                        }
                    });
                }
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "Transaction/GetHistory error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        ////Loyalty
        //[HttpPut]
        ////[ApiExplorerSettings(IgnoreApi = true)]
        //[Route("UpdateNotificationSetting")]
        //public async Task<ActionResult<LoyaltyResponse<string>>> UpdateNotificationSetting([FromBody] UpdateNotificationSettingInput input)
        //{
        //    try
        //    {
        //        var result = await _loyaltySecondaryCustomersService.UpdateNotificationSetting(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //        _logger.LogError(ex, "UpdateNotificationSetting Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        [HttpPost]
        [Route("GetListMemberCodeByListPhoneNumber")]
        public async Task<ActionResult<GetListMemberCodeByListPhoneNumberOutput>>
            GetListMemberCodeByListPhoneNumber([FromBody] GetListMemberCodeByListPhoneNumberInput input)
        {
            try
            {
                var res = await _memberService.GetListMemberCodeByListPhoneNumber(input);
                return StatusCode(200, res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetListMemberCodeByListPhoneNumber error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }


        //[HttpPost]
        //[Route("VerifyProviderIdByPhoneNumber")]
        //public async Task<ActionResult<VerifyProviderIdByPhoneNumberResponse>> VerifyProviderIdByPhoneNumber(VerifyProviderIdByPhoneNumberRequest input)
        //{
        //    try
        //    {
        //        var result = await _memberService.VerifyProviderIdByPhoneNumber(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //        _logger.LogError(ex, "Verify Provider Id By PhoneNumber Error - " + JsonConvert.SerializeObject(ex));
        //        return StatusCode(400, res);
        //    }
        //}

        ////[HttpPost]
        ////[Route("VerifyOrCreate")]
        ////public async Task<ActionResult<RewardMemberVerifyOrCreateOutput>> VerifyOrCreate(RewardMemberVerifyOrCreateInput input)
        ////{
        ////    try
        ////    {
        ////        var authorization = Request.Headers["Authorization"].ToString();
        ////        var result = await _memberService.VerifyOrCreate(input, authorization);
        ////        return StatusCode(200, result);
        ////    }
        ////    catch (Exception ex)
        ////    {
        ////        var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        ////        _logger.LogError(ex, "Verify or create Error - " + JsonConvert.SerializeObject(ex));
        ////        return StatusCode(400, res);
        ////    }
        ////}

        //[HttpPut]
        ////[ApiExplorerSettings(IgnoreApi = true)]
        //[Route("UpdatePhoneNumber")]
        //public async Task<ActionResult<UpdatePhoneNumberOutput>> UpdatePhoneNumber([FromBody] MobileUpdatePhoneNumberInput input)
        //{
        //    var result = await _memberService.UpdatePhoneNumber(input);
        //    return StatusCode(200, result);
        //}

        //[HttpPost]
        //[Route("AccountHavePhoneNumber")]
        //public async Task<ActionResult<RewardMemberAccountHavePhoneNumberOutput>> AccountHavePhoneNumber(RewardMemberAccountHavePhoneNumberInput input)
        //{
        //    var result = await _memberService.AccountHavePhoneNumber(input);
        //    return StatusCode(200, result);
        //}

        //[HttpGet]
        //[Route("RevokeToken")]
        //public async Task<ActionResult<RewardMemberRevokeTokenResponse>> RevokeToken()
        //{
        //    var authorization = Request.Headers["Authorization"].ToString();
        //    var result = await _memberService.RevokeToken(authorization);
        //    return StatusCode(200, result);
        //}

        //[HttpGet]
        //[Route("GetMemberLoginByFirebaseId")]
        //public async Task<ActionResult<RewardMemberGetMemberLoginByFirebaseIdOutput>> GetMemberLoginByFirebaseId()
        //{
        //    try
        //    {
        //        var authorization = Request.Headers["Authorization"].ToString();
        //        var result = await _memberService.GetMemberLoginByFirebaseId(authorization);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //        _logger.LogError(ex, "GetMemberLoginByFirebaseId Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        //[HttpGet]
        //[Route("GetUsagePriority")]
        //public async Task<ActionResult<RewardMemberGetUsagePriorityOutput>> GetUsagePriority([FromQuery] RewardMemberGetUsagePriorityInput input)
        //{
        //    try
        //    {
        //        var result = await _memberService.GetUsagePriority(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //        _logger.LogError(ex, "GetUsagePriority Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        //[HttpPost]
        //[Route("UpdateUsagePriority")]
        //public async Task<ActionResult<RewardMemberUpdateUsagePriorityOutput>> UpdateUsagePriority([FromBody] RewardMemberUpdateUsagePriorityInput input)
        //{
        //    try
        //    {
        //        var result = await _memberService.UpdateUsagePriority(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //        _logger.LogError(ex, "UpdateUsagePriority Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        //[HttpPost]
        //[Route("UpdatePointUsageType")]
        //public async Task<ActionResult<RewardMemberUpdatePointUsageTypeOutput>> UpdatePointUsageType([FromBody] RewardMemberUpdatePointUsageTypeInput input)
        //{
        //    try
        //    {
        //        var result = await _memberService.UpdatePointUsageType(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //        _logger.LogError(ex, "UpdateUsagePriority Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        //[HttpGet]
        //[Route("GetCashoutAndTopupInfo")]
        //public async Task<ActionResult<RewardMemberGetCashoutAndTopupInfoOutput>> GetCashoutAndTopupInfo([FromQuery] RewardMemberGetCashoutAndTopupInfoInput input)
        //{
        //    try
        //    {
        //        var result = await _memberService.GetCashoutAndTopupInfo(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //        _logger.LogError(ex, "GetCashoutAndTopupInfo Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        [HttpGet]
        [Route("ViewBalanceWithExpiringCoinsByMemberCode")]
        public async Task<ActionResult<LoyaltyResponse<LoyaltyRewardMemberViewPointV2>>> ViewBalanceWithExpiringCoinsByMemberCode([FromQuery] RewardMemberRequestInput request)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var result = await _memberService.ViewBalanceWithExpiringCoinsByMemberCode(request, authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ViewBalanceWithExpiringCoinsByMemberCode Error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }
    }
}
