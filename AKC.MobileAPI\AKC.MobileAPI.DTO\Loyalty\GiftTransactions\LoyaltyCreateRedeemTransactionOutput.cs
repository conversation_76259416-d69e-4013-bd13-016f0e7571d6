﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.GiftTransactions
{
    public class LoyaltyCreateRedeemTransactionOutput
    {
        public LoyaltyCreateRedeemTransaction Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }
    public class LoyaltyCreateRedeemTransaction
    {
        public List<DataRedeemTransaction> Items { get; set; }
        public int TotalCount { get; set; }
        public string Exception { get; set; }
        public string Messages { get; set; }
        public bool SuccessedRedeem { get; set; }
        public bool IsNotEnoughBalance { get; set; }

        public bool Timeout { get; set; }
    }

    public class DataRedeemTransaction
    {
        public string Code { get; set; }
        public decimal TotalCoin { get; set; }
        public string Status { get; set; }
        public DateTime Date { get; set; }
        public string Description { get; set; }
        public EGiftData EGift { get; set; }
    }

    public class EGiftData
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string usedStatus { get; set; }
        public DateTime? ExpiredDate { get; set; }
        public string QRCode { get; set; }
    }
}
