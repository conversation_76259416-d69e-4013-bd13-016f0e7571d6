﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Rewards
{
    public class LoyaltyInputActionInput
    {
        public List<ActionRequestDto> Actions { get; set; }
    }

    public class ActionRequestDto
    {
        public string TransactionCode { get; set; }
        public int Type { get; set; }
        public string OriginalTransactionCode { get; set; }
        public string ActionCode { get; set; }
        public ActionListValueFilterReward ActionFilter { get; set; }
        public string MemberCode { get; set; }
        public int Value { get; set; }
        public DateTime ActionTime { get; set; }
        public string Tag { get; set; }
        public string ReferenceCode { get; set; }
        public string MLMDealerChannel { get; set; }
        public string MLMDistributionChannel { get; set; }
        public bool MlmApplied { get; set; }
        public string RankCode { get; set; }
    }

    public class ActionListValueFilterReward
    {
        public string ListCodeFilter { get; set; }
        public string Device { get; set; }
        public string RegisterType { get; set; }
    }
}
