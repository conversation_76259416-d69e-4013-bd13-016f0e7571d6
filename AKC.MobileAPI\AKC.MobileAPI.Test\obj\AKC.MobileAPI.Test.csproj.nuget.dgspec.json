{"format": 1, "restore": {"D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.Test\\AKC.MobileAPI.Test.csproj": {}}, "projects": {"D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.DTO\\AKC.MobileAPI.DTO.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.DTO\\AKC.MobileAPI.DTO.csproj", "projectName": "AKC.MobileAPI.DTO", "projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.DTO\\AKC.MobileAPI.DTO.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.DTO\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[12.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}, "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\AKC.MobileAPI.Service.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\AKC.MobileAPI.Service.csproj", "projectName": "AKC.MobileAPI.Service", "projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\AKC.MobileAPI.Service.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.DTO\\AKC.MobileAPI.DTO.csproj": {"projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.DTO\\AKC.MobileAPI.DTO.csproj"}, "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj": {"projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"AWSSDK.S3": {"target": "Package", "version": "[3.5.7.3, )"}, "AspNetCore.TotpGenerator": {"target": "Package", "version": "[1.0.0, )"}, "Azure.Storage.Blobs": {"target": "Package", "version": "[12.4.3, )"}, "Cronos": {"target": "Package", "version": "[0.7.0, )"}, "FirebaseAdmin": {"target": "Package", "version": "[1.14.0, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Caching.Abstractions": {"target": "Package", "version": "[3.1.2, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[3.1.2, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[3.1.2, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[3.1.2, )"}, "Microsoft.Extensions.Configuration.FileExtensions": {"target": "Package", "version": "[3.1.2, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[3.1.2, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[3.1.2, )"}, "Minio": {"target": "Package", "version": "[3.1.13, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[12.0.2, )"}, "Quartz": {"target": "Package", "version": "[3.0.7, )"}, "WindowsAzure.Storage": {"target": "Package", "version": "[9.3.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}, "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.Test\\AKC.MobileAPI.Test.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.Test\\AKC.MobileAPI.Test.csproj", "projectName": "AKC.MobileAPI.Test", "projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.Test\\AKC.MobileAPI.Test.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.Test\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI\\AKC.MobileAPI.csproj": {"projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI\\AKC.MobileAPI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[16.2.0, )"}, "coverlet.collector": {"target": "Package", "version": "[1.0.1, )"}, "xunit": {"target": "Package", "version": "[2.4.0, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}, "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI\\AKC.MobileAPI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI\\AKC.MobileAPI.csproj", "projectName": "AKC.MobileAPI", "projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI\\AKC.MobileAPI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.DTO\\AKC.MobileAPI.DTO.csproj": {"projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.DTO\\AKC.MobileAPI.DTO.csproj"}, "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\AKC.MobileAPI.Service.csproj": {"projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\AKC.MobileAPI.Service.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Cronos": {"target": "Package", "version": "[0.7.0, )"}, "FirebaseAdmin": {"target": "Package", "version": "[1.14.0, )"}, "Gelf.Extensions.Logging": {"target": "Package", "version": "[2.0.0, )"}, "Microsoft.AspNet.WebApi.Core": {"target": "Package", "version": "[5.2.7, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[3.1.2, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[3.1.0-preview1.19506.2, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[3.1.0-preview1.19506.2, )"}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"target": "Package", "version": "[8.0.6, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[3.1.0-preview1.19506.1, )"}, "Microsoft.Extensions.Logging.Log4Net.AspNetCore": {"target": "Package", "version": "[3.1.0, )"}, "Microsoft.IO.RecyclableMemoryStream": {"target": "Package", "version": "[1.3.4, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.9.5, )"}, "Portable.BouncyCastle": {"target": "Package", "version": "[1.8.10, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}, "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "projectName": "AKC.RabbitMQ", "projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp2.1": {"targetAlias": "netcoreapp2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netcoreapp2.1": {"targetAlias": "netcoreapp2.1", "dependencies": {"Castle.Core": {"target": "Package", "version": "[4.3.1, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[2.1.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.Extensions.ObjectPool": {"target": "Package", "version": "[2.1.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[2.1.0, )"}, "Microsoft.NETCore.App": {"suppressParent": "All", "target": "Package", "version": "[2.1.0, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[11.0.2, )"}, "RabbitMQ.Client": {"target": "Package", "version": "[5.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}}}