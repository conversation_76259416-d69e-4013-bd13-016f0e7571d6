﻿using System.Collections.Generic;

namespace AKC.MobileAPI.DTO.Loyalty.LocationManagement
{
    public class GetAllLocationManagementDto
    {
		public ListResultGetLocationAll Result { get; set; }
		public string TargetUrl { get; set; }
		public bool Success { get; set; }
		public string Error { get; set; }
		public bool UnAuthorizedRequest { get; set; }
		public bool __abp { get; set; }
	}

	public class ListResultGetLocationAll
	{
		public int TotalCount { get; set; }

		public List<GetAllLocationItemDto> Items { get; set; }

	}

	public class ViewLocationByIdsOutput
	{
		public List<GetAllLocationItemDto> Result { get; set; }

	}

	public class GetAllLocationItemDto
	{
		public int Id { get; set; }
		public virtual string Code { get; set; }
		public virtual string Name { get; set; }
		public virtual string Description { get; set; }
		public virtual string ParentCode { get; set; }
		public virtual string InternalCode { get; set; }
		public virtual string Level { get; set; }
	}
}
