﻿using AKC.MobileAPI.DTO.Const;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using System.Text.Json.Serialization;

namespace AKC.MobileAPI.DTO.Loyalty.Member
{
    public class CreateOrEditSecondaryCustomerDto
    {
        public int Id { get; set; }

        //[Required]
        //public int TenantId { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxCodeLength, MinimumLength = SecondaryCustomerConsts.MinCodeLength)]
        public string Code { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxFirstNameLength, MinimumLength = SecondaryCustomerConsts.MinFirstNameLength)]
        public string FirstName { get; set; }


        [StringLength(SecondaryCustomerConsts.MaxLastNameLength, MinimumLength = SecondaryCustomerConsts.MinLastNameLength)]
        public string LastName { get; set; }

        [JsonIgnore]
        public string Type { get; set; }

        public DateTime? Birthday { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxPhoneLength, MinimumLength = SecondaryCustomerConsts.MinPhoneLength)]
        public string Phone { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxEmailLength, MinimumLength = SecondaryCustomerConsts.MinEmailLength)]
        public string Email { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxRegionLength, MinimumLength = SecondaryCustomerConsts.MinRegionLength)]
        public string RegionCode { get; set; }

        [JsonIgnore]
        [StringLength(SecondaryCustomerConsts.MaxSecondaryCustomerTypeLength, MinimumLength = SecondaryCustomerConsts.MinSecondaryCustomerTypeLength)]
        public string MemberTypeCode { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxChannelTypeLength, MinimumLength = SecondaryCustomerConsts.MinChannelTypeLength)]
        public string ChannelType { get; set; }

        [JsonIgnore]
        [StringLength(SecondaryCustomerConsts.MaxRankTypeLength, MinimumLength = SecondaryCustomerConsts.MinRankTypeLength)]
        public string RankTypeCode { get; set; }

        public string Address { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxGenderLength, MinimumLength = SecondaryCustomerConsts.MinGenderLength)]
        public string Gender { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxCardsLength, MinimumLength = SecondaryCustomerConsts.MinCardsLength)]
        public string Cards { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxFullRegionCodeLength, MinimumLength = SecondaryCustomerConsts.MinFullRegionCodeLength)]
        public string FullRegionCode { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxFullSecondaryCustomerTypeCodeLength, MinimumLength = SecondaryCustomerConsts.MinFullSecondaryCustomerTypeCodeLength)]
        private string fullMemberTypeCode { get; set; }
        public string FullMemberTypeCode
        {
            get { return fullMemberTypeCode; }
            set
            {
                fullMemberTypeCode = value;
                Type = value;
                MemberTypeCode = value;
                RankTypeCode = value;
            }
        }

        [StringLength(SecondaryCustomerConsts.MaxFullChannelTypeCodeLength, MinimumLength = SecondaryCustomerConsts.MinFullChannelTypeCodeLength)]
        public string FullChannelTypeCode { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxIdCardLength, MinimumLength = SecondaryCustomerConsts.MinIdCardLength)]
        public string IdCard { get; set; }

        public int? MemberLoyaltyInfoId { get; set; }
        public string StandardMemberCode { get; set; }
        public string AccountType { get; set; }
        public DateTime? JoiningDate { get; set; }
        public DateTime? WithdrawnDate { get; set; }
        public string Status { get; set; }
        public string ReferenceInfo { get; set; }
        public string RankType { get; set; }
        public string ContractCode { get; set; }
        public string FacebookId { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxReferralCodeLength, MinimumLength = SecondaryCustomerConsts.MinReferralCodeLength)]
        public string ReferralCode { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxIdNumLength, MinimumLength = SecondaryCustomerConsts.MinIdNumLength)]
        public string IdNum { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxPartnerPhoneNumberLength, MinimumLength = SecondaryCustomerConsts.MinPartnerPhoneNumberLength)]
        public string PartnerPhoneNumber { get; set; }

        public string Avatar { get; set; }

        public bool IsIdCardVerified { get; set; }

        public int NotificationCount { get; set; }

        public bool? IsAutoChangeRank { get; set; }

        public decimal? YearlyLimit { get; set; } = null;

        [StringLength(SecondaryCustomerConsts.MaxCifString, MinimumLength = SecondaryCustomerConsts.MinCifString)]
        private string cif;
        public string Cif
        {
            get { return cif; }
            set
            {
                cif = value;
                Code = value;
            }
        }

        public DateTime? CertIDDate { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxCertIdPlaceString, MinimumLength = SecondaryCustomerConsts.MinCertIdPlaceString)]
        public string CertIDPlace { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxDocTypeString, MinimumLength = SecondaryCustomerConsts.MinDocTypeString)]
        public string DocType { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxSegmentString, MinimumLength = SecondaryCustomerConsts.MinSegmentString)]
        public string Segment { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxMaritalStatusString, MinimumLength = SecondaryCustomerConsts.MinMaritalStatusString)]
        public string MaritalStatus { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxVipTypeString, MinimumLength = SecondaryCustomerConsts.MinVipTypeString)]
        public string VipType { get; set; }

        [StringLength(SecondaryCustomerConsts.MaxLinkIDPhoneNumberString, MinimumLength = SecondaryCustomerConsts.MinLinkIDPhoneNumberString)]
        public string LinkID_PhoneNumber { get; set; }
    }
}
