﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Const
{
    public class SecondaryCustomerConsts
    {

        public const int MinCodeLength = 0;
        public const int MaxCodeLength = 255;
        public const string CodeMgsError = "StringValidLength255";

        public const int MinFirstNameLength = 0;
        public const int MaxFirstNameLength = 255;
        public const string FirstNameMgsError = "StringValidLength255";

        public const int MinLastNameLength = 0;
        public const int MaxLastNameLength = 255;
        public const string LastNameMgsError = "StringValidLength255";

        public const int MinPhoneLength = 0;
        public const int MaxPhoneLength = 255;
        public const string PhoneMgsError = "StringValidLength255";

        public const int MinEmailLength = 0;
        public const int MaxEmailLength = 255;
        public const string EmailMgsError = "StringValidLength255";

        public const int MinRegionLength = 0;
        public const int MaxRegionLength = 255;
        public const string RegionMgsError = "StringValidLength255";

        public const int MinSecondaryCustomerTypeLength = 0;
        public const int MaxSecondaryCustomerTypeLength = 255;
        public const string SecondaryCustomerTypeMgsError = "StringValidLength255";

        public const int MinChannelTypeLength = 0;
        public const int MaxChannelTypeLength = 255;
        public const string ChanelTypeMgsError = "StringValidLength255";

        public const int MinRankTypeLength = 0;
        public const int MaxRankTypeLength = 255;
        public const string RankTypeMgsError = "StringValidLength255";

        public const int MinGenderLength = 0;
        public const int MaxGenderLength = 255;
        public const string GenderMgsError = "StringValidLength255";

        public const int MinCardsLength = 0;
        public const int MaxCardsLength = 255;
        public const string CardsMgsError = "StringValidLength255";

        public const int MinFullRegionCodeLength = 0;
        public const int MaxFullRegionCodeLength = 1000;
        public const string FullRegionCodeMgsError = "StringValidLength1000";

        public const int MinFullSecondaryCustomerTypeCodeLength = 0;
        public const int MaxFullSecondaryCustomerTypeCodeLength = 1000;
        public const string FullSecondaryCustomerTypeCodeMgsError = "StringValidLength1000";

        public const int MinFullChannelTypeCodeLength = 0;
        public const int MaxFullChannelTypeCodeLength = 1000;
        public const string FullChanelTypeCodeMgsError = "StringValidLength1000";

        public const int MinIdCardLength = 0;
        public const int MaxIdCardLength = 30;
        public const string IdCardMgsError = "StringValidLength30";

        public const int MinFacebookIdLength = 0;
        public const int MaxFacebookIdLength = 255;
        public const string FacebookIdMgsError = "StringValidLength255";

        public const int MiniCloudIdLength = 0;
        public const int MaxiCloudIdLength = 255;
        public const string iCloudIdMgsError = "StringValidLength255";

        public const int MinSamsungIdLength = 0;
        public const int MaxSamsungIdLength = 255;
        public const string SamsungIdMgsError = "StringValidLength255";

        public const int MinTypeLength = 0;
        public const int MaxTypeLength = 255;
        public const string TypeMgsError = "StringValidLength255";

        public const int MinReferralCodeLength = 0;
        public const int MaxReferralCodeLength = 50;

        public const int MinIdNumLength = 0;
        public const int MaxIdNumLength = 50;

        public const int MinPartnerPhoneNumberLength = 0;
        public const int MaxPartnerPhoneNumberLength = 30;

        public const int MinNameSearchLength = 0;
        public const int MaxNameSearchLength = 1000;

        public const int MinNotificationString = 0;
        public const int MaxNotificationString = 255;

        public const int MinCifString = 0;
        public const int MaxCifString = 100;

        public const int MinCertIdPlaceString = 0;
        public const int MaxCertIdPlaceString = 100;

        public const int MinDocTypeString = 0;
        public const int MaxDocTypeString = 100;

        public const int MinSegmentString = 0;
        public const int MaxSegmentString = 100;

        public const int MinMaritalStatusString = 0;
        public const int MaxMaritalStatusString = 50;

        public const int MinVipTypeString = 0;
        public const int MaxVipTypeString = 255;

        public const int MinLinkIDPhoneNumberString = 0;
        public const int MaxLinkIDPhoneNumberString = 50;

        public const int MinLinkIDWalletAddressString = 0;
        public const int MaxLinkIDWalletAddressString = 255;
    }
}
