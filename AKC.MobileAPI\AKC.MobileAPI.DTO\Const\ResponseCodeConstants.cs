using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Const
{
    /// <summary>
    /// Tập hợp các mã phản hồi và thông báo tương ứng
    /// Sử dụng: ResponseCodeConstants.GetResponseMessage("00") để lấy message từ code
    /// </summary>
    public static class ResponseCodeConstants
    {
        /// <summary>
        /// Dictionary chứa mapping giữa response code và response message
        /// </summary>
        private static readonly Dictionary<string, string> ResponseMessages = new Dictionary<string, string>
        {
            { "00", "Thành công" },
            { "901", "Mã khách hàng không tồn tại" },
            { "902", "Mã CIF không được rỗng" },
            { "922", "Quantity phải là số nguyên dương" },
            { "923", "Total Amount phải là số nguyên dương" },
            { "921", "Gift Code không được để trống" },
            { "940", "Mã giao dịch qua không đúng" },
            { "1022", "Không thể đổi quà lúc này" },
            { "1023", "Mã quà không tồn tại" },
            { "1018", "LinkID MemberCode không đúng" },
            { "1019", "Mã quà không hợp lệ" },
            { "1025", "Quà hết số lượng" },
            { "1028", "Quà hết số lượng" },
            { "1026", "Amount điền vào không khớp RequiredCoin của quà" },
            { "1050", "Quà chỉ đổi một lần cho mỗi member" },
            { "1051", "Quà chỉ đổi một lần, số lượng là 1, đối với mỗi member" },
            { "2001", "Khi đánh dấu quà đã dùng, thì membercode và mã giao dịch quà không khớp" },
            { "2003", "Mã giao dịch quà đã được đánh dấu là Used rồi" },
            { "3000", "Đối tác không thể đổi quà thuộc danh mục này" },
            { "3001", "Đối tác không có danh mục quà nào khả dụng " },
            { "3002", "Đối tác chưa được gắn khai báo channel" },
            { "3100", "Channel không được phép redeem với Payment Type đó" },
            { "3101", "Channel hết hạn mức redeem với PaymentType đó" },
            { "3102", "Channel hết hạn mức redeem theo ngày" },
            { "3103", "Channel hết hạn mức redeem theo tuần" },
            { "3104", "Channel hết hạn mức redeem theo năm" },
            { "3105", "Channel hết hạn mức redeem theo năm" },
            { "4000", "Mã giao dịch không được để trống" },
            { "4003", "Mã giao dịch không đúng" },
            { "4004", "Payment Type không hợp lệ" },
            { "5000", "Không lấy được thông tin MerchantId của quà" }
        };

        /// <summary>
        /// Lấy thông báo phản hồi từ mã phản hồi
        /// </summary>
        /// <param name="responseCode">Mã phản hồi</param>
        /// <returns>Thông báo phản hồi tương ứng, nếu không tồn tại trả về "Lỗi hệ thống"</returns>
        public static string GetResponseMessage(string responseCode)
        {
            if (string.IsNullOrEmpty(responseCode))
                return "Lỗi hệ thống";

            return ResponseMessages.TryGetValue(responseCode, out string message)
                ? message
                : "Lỗi hệ thống";
        }
    }
}
