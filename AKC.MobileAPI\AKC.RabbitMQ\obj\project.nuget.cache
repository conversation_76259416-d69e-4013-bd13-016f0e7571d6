{"version": 2, "dgSpecHash": "LeI2YZP8kBE=", "success": true, "projectFilePath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\castle.core\\4.3.1\\castle.core.4.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\2.1.0\\microsoft.extensions.configuration.abstractions.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\2.1.0\\microsoft.extensions.dependencyinjection.abstractions.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\2.1.1\\microsoft.extensions.logging.abstractions.2.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.objectpool\\2.1.0\\microsoft.extensions.objectpool.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\2.1.0\\microsoft.extensions.options.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\2.1.0\\microsoft.extensions.primitives.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app\\2.1.0\\microsoft.netcore.app.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.dotnetapphost\\2.1.0\\microsoft.netcore.dotnetapphost.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.dotnethostpolicy\\2.1.0\\microsoft.netcore.dotnethostpolicy.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.dotnethostresolver\\2.1.0\\microsoft.netcore.dotnethostresolver.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\2.1.0\\microsoft.netcore.platforms.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\2.1.0\\microsoft.netcore.targets.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\netstandard.library\\2.0.3\\netstandard.library.2.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\11.0.2\\newtonsoft.json.11.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\rabbitmq.client\\5.2.0\\rabbitmq.client.5.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.nongeneric\\4.3.0\\system.collections.nongeneric.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.specialized\\4.3.0\\system.collections.specialized.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel\\4.3.0\\system.componentmodel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.primitives\\4.3.0\\system.componentmodel.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.typeconverter\\4.3.0\\system.componentmodel.typeconverter.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tracesource\\4.3.0\\system.diagnostics.tracesource.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.dynamic.runtime\\4.3.0\\system.dynamic.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.0\\system.memory.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit\\4.3.0\\system.reflection.emit.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.typeextensions\\4.3.0\\system.reflection.typeextensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\4.5.0\\system.runtime.compilerservices.unsafe.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.regularexpressions\\4.3.0\\system.text.regularexpressions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.3.0\\system.threading.tasks.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.readerwriter\\4.3.0\\system.xml.readerwriter.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xmldocument\\4.3.0\\system.xml.xmldocument.4.3.0.nupkg.sha512"], "logs": [{"code": "NU1903", "level": "Warning", "message": "Package 'Microsoft.NETCore.App' 2.1.0 has a known high severity vulnerability, https://github.com/advisories/GHSA-2xjx-v99w-gqf3", "projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "warningLevel": 1, "filePath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "libraryId": "Microsoft.NETCore.App", "targetGraphs": [".NETCoreApp,Version=v2.1"]}, {"code": "NU1902", "level": "Warning", "message": "Package 'Microsoft.NETCore.App' 2.1.0 has a known moderate severity vulnerability, https://github.com/advisories/GHSA-3gp9-h8hw-pxpw", "projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "warningLevel": 1, "filePath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "libraryId": "Microsoft.NETCore.App", "targetGraphs": [".NETCoreApp,Version=v2.1"]}, {"code": "NU1903", "level": "Warning", "message": "Package 'Microsoft.NETCore.App' 2.1.0 has a known high severity vulnerability, https://github.com/advisories/GHSA-3w5p-jhp5-c29q", "projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "warningLevel": 1, "filePath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "libraryId": "Microsoft.NETCore.App", "targetGraphs": [".NETCoreApp,Version=v2.1"]}, {"code": "NU1902", "level": "Warning", "message": "Package 'Microsoft.NETCore.App' 2.1.0 has a known moderate severity vulnerability, https://github.com/advisories/GHSA-5633-f33j-c6f7", "projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "warningLevel": 1, "filePath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "libraryId": "Microsoft.NETCore.App", "targetGraphs": [".NETCoreApp,Version=v2.1"]}, {"code": "NU1903", "level": "Warning", "message": "Package 'Microsoft.NETCore.App' 2.1.0 has a known high severity vulnerability, https://github.com/advisories/GHSA-6px8-22w5-w334", "projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "warningLevel": 1, "filePath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "libraryId": "Microsoft.NETCore.App", "targetGraphs": [".NETCoreApp,Version=v2.1"]}, {"code": "NU1903", "level": "Warning", "message": "Package 'Microsoft.NETCore.App' 2.1.0 has a known high severity vulnerability, https://github.com/advisories/GHSA-g5vf-38cp-4px9", "projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "warningLevel": 1, "filePath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "libraryId": "Microsoft.NETCore.App", "targetGraphs": [".NETCoreApp,Version=v2.1"]}, {"code": "NU1902", "level": "Warning", "message": "Package 'Microsoft.NETCore.App' 2.1.0 has a known moderate severity vulnerability, https://github.com/advisories/GHSA-vgwq-hfqc-58wv", "projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "warningLevel": 1, "filePath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "libraryId": "Microsoft.NETCore.App", "targetGraphs": [".NETCoreApp,Version=v2.1"]}, {"code": "NU1902", "level": "Warning", "message": "Package 'Microsoft.NETCore.App' 2.1.0 has a known moderate severity vulnerability, https://github.com/advisories/GHSA-x5qj-9vmx-7g6g", "projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "warningLevel": 1, "filePath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "libraryId": "Microsoft.NETCore.App", "targetGraphs": [".NETCoreApp,Version=v2.1"]}, {"code": "NU1903", "level": "Warning", "message": "Package 'Newtonsoft.Json' 11.0.2 has a known high severity vulnerability, https://github.com/advisories/GHSA-5crp-9r3c-p9vr", "projectPath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "warningLevel": 1, "filePath": "D:\\working\\vporg-api\\AKC.MobileAPI\\AKC.RabbitMQ\\AKC.RabbitMQ.csproj", "libraryId": "Newtonsoft.Json", "targetGraphs": [".NETCoreApp,Version=v2.1"]}]}