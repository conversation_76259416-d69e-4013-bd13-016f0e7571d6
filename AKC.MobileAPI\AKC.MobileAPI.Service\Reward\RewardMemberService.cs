using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.Merchant;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FirebaseAdmin.Auth;
using AKC.MobileAPI.DTO.MobileAPI;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.DTO.Loyalty;
using Microsoft.Extensions.Caching.Distributed;
using AKC.MobileAPI.Service.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.Service.Exceptions;
using RestSharp.Extensions;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Sme;
using AKC.MobileAPI.Service.Helpers;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardMemberService : RewardBaseService, IRewardMemberService
    {
        private ILoyaltySecondaryCustomersService _customersService;
        //private IUploadImageSevice _uploadImageSevice;
        private IExceptionReponseService _exceptionReponseService;
        protected readonly ILogger _logger;

        private readonly IDistributedCache _cache;
        private readonly ILoyaltyMemberService _loyaltyMemberService;
        private readonly ILoyaltyUtilsService _loyaltyUtilsService;
        public RewardMemberService(IConfiguration configuration,
            ILoyaltySecondaryCustomersService customersService,
            //IUploadImageSevice uploadImageSevice,
            IDistributedCache cache,
            IExceptionReponseService exceptionReponseService,
            ILoyaltyMemberService loyaltyMemberService,
            ILoyaltyUtilsService x,
            ILogger<RewardMemberService> logger) : base(configuration)
        {
            _customersService = customersService;
            //_uploadImageSevice = uploadImageSevice;
            _cache = cache;
            _exceptionReponseService = exceptionReponseService;
            _logger = logger;
            _loyaltyMemberService = loyaltyMemberService;
            _loyaltyUtilsService = x;
        }

        public async Task<RewardMemberViewOutput> View(ViewMemberInput input)
        {
            var nationalId = "";
            if (!string.IsNullOrEmpty(input.NationalId))
            {
                nationalId = input.NationalId;
            }
            var request = new ViewMemberInput()
            {
                NationalId = nationalId,
            };
            if (!string.IsNullOrWhiteSpace(input.PhoneNumber))
            {
                request.PhoneNumber = input.PhoneNumber;
            }
            return await GetRewardAsync<RewardMemberViewOutput>(RewardApiUrl.MEMBER_VIEW, request);
        }


        public async Task<RewardMemberUpdateOutput> Update(RewardMemberUpdateInput input)
        {
            var avatarLink = await GetAvatarLink(input.AvatarFromBase64String);
            var updateMemberDto = new RewardMemberUpdateDto()
            {
                NationalId = input.NationalId,
                IdCard = input.IdCard,
                PartnerPhoneNumber = input.PartnerPhoneNumber,
                Type = "Member",
                Phone = input.Phone,
                Gender = !(new List<string>{ "M", "F", "O" }).Contains(input.Gender) ? "O" : input.Gender,
                Status = "A",
                RankTypeCode = "Customer",
                IsDeleted = false,
                Address = input.Address,
                Dob = input.Dob,
                Name = input.Name,
                Email = input.Email,
                PointUsingOrdinary = input.PointUsingOrdinary,
                HashAddress = "",
                D365Id = input.NationalId,
                MbcId = input.NationalId,
                MbcCardId = input.NationalId,
                RegionCode = "",
                FullRegionCode = "",
                MemberTypeCode = "",
                FullMemberTypeCode = "",
                ChannelType = "",
                FullChannelTypeCode = "",
                StandardMemberCode = input.NationalId,
                ReferralCode = GenReferralCode(input.Phone, input.NationalId),
                Avatar = avatarLink,
                IsIdCardVerified = input.IsIdCardVerified,
                NationId = input.NationId != 0 ? input.NationId : 0,
                CityId = input.CityId != 0 ? input.CityId : 0,
                DistrictId = input.DistrictId != 0 ? input.DistrictId : 0,
                WardId = input.WardId != 0 ? input.WardId : 0,
                StreetDetail = input.StreetDetail,
            };
            return await PutRewardAsync<RewardMemberUpdateOutput>(RewardApiUrl.MEMBER_UPDATE, updateMemberDto);
        }

        public async Task<LoyaltyResponse<LoyaltyRewardMemberViewCreditBalance>> ViewCreditBalance(RewardViewCreditBalanceInput input)
        {
            _logger.LogInformation("Get credit balance of member: wallet = " + input.WalletAddress);
            if (string.IsNullOrWhiteSpace(input.WalletAddress))
            {
                CommonHelper.GetErrorValidation("911", "Wallet Address is required");
            }
            // Get credit balance
            var creditBalance = await GetRewardAsync<RewardViewCreditBalanceOutput>(RewardApiUrl.MEMBER_VIEW_CREDIT_BALANCE_BYWALLETADDRESS,
                    new RewardViewCreditBalanceInput { WalletAddress = input.WalletAddress });
            var result = new LoyaltyResponse<LoyaltyRewardMemberViewCreditBalance>
            {
                Result = new LoyaltyRewardMemberViewCreditBalance
                {
                    CreditBalance = creditBalance.Items?.CreditBalance ?? 0,
                    WalletAddress = input.WalletAddress
                }
            };
            _logger.LogInformation("Result Get credit balance of member: wallet = " + input.WalletAddress + "; credit balance = " + result?.Result?.CreditBalance);
            return result;
        }

        public async Task<LoyaltyResponse<string>> RemoveConnection(
            RewardRemoveConnectionInput input)
        {
            _logger.LogInformation(">> RemoveConnection = " + JsonConvert.SerializeObject(input));
            var v1String = _configuration.GetSection("RewardVPBank:MerchantId").Value;
            var vpbankMerchantId = int.Parse(v1String);
            var rnInput = new Dictionary<string, int>();
            rnInput.Add("MerchantId", vpbankMerchantId);
            rnInput.Add("MemberId", input.LinkIdMemberId);
            
            await PostRewardAsync<RewardRemoveConnectionOutput>(RewardApiUrl.MEMBER_REMOVE_CONNECTION, rnInput);
            await _loyaltyMemberService.RemoveConnect(new LoyaltyMemberRemoveConnectMerchantInput()
            {
                LinkID_MemberID = input.LinkIdMemberId
            });
            return new LoyaltyResponse<string>()
            {
                Success = true,
                Result = "Success"
            };
        }
        public async Task<LoyaltyResponse<LoyaltyRewardMemberViewPoint>> ViewPoint(RewardMemberRequestInput input, string authorization)
        {
            if (string.IsNullOrWhiteSpace(input.MemberCode))
            {
                CommonHelper.GetErrorValidation("911", "Member code is required");
            }
            var request = new RewardMemberViewPointInput()
            {
                NationalId = input.MemberCode
            };

            var exception = new Exception();

            var loyaltyViewPointResult = new LoyaltyResponse<LoyaltyViewPointWithGrantTypeOutput>();
            try
            {
                loyaltyViewPointResult = await _customersService.ViewPointWithGrantType(input.MemberCode, true);
            }
            catch (Exception ex)
            {
                exception = ex;
                loyaltyViewPointResult = null;
            }
            var rewardViewPointResult = new ViewBalanceByWalletAddressOutput();
            // View reward token when member has connect LinkID
            if (loyaltyViewPointResult != null && !string.IsNullOrWhiteSpace(loyaltyViewPointResult.Result.LinkID_WalletAddress))
            {
                try
                {
                    rewardViewPointResult = await GetRewardAsync<ViewBalanceByWalletAddressOutput>(RewardApiUrl.OFFCHAIN_GET_BALANCE_BY_WALLET,
                        new { WalletAddress = loyaltyViewPointResult.Result.LinkID_WalletAddress, Mode = "BalanceOnly" });
                }
                catch (Exception ex)
                {
                    exception = ex;
                    rewardViewPointResult = null;
                }
            }

            if (rewardViewPointResult == null && loyaltyViewPointResult == null)
            {
                throw exception;
            }
            else
            {
                var result = new LoyaltyResponse<LoyaltyRewardMemberViewPoint>()
                {
                    Result = new LoyaltyRewardMemberViewPoint()
                    {
                        MemberCode = input.MemberCode
                    },
                    Success = loyaltyViewPointResult != null ? loyaltyViewPointResult.Success : false,
                };
                
                if (rewardViewPointResult != null && rewardViewPointResult.Items != null)
                {
                    result.Result.TotalToken = rewardViewPointResult.Items.Balance;
                }

                if (loyaltyViewPointResult != null)
                {
                    result.Result.TotalCoin = loyaltyViewPointResult.Result.TotalCoin;
                    result.Result.TotalAutoExchangeCoin = loyaltyViewPointResult.Result.TotalAutoExchangeCoin;
                }
                if (input.SimpleMode == false)
                {
                    result.Result.ExpiringTokenAmount = rewardViewPointResult?.Items?.ExpiringAmount ?? null;
                    result.Result.ExpiringDate = rewardViewPointResult?.Items?.ExpiringDate;
                }
                return result;
            }
        }

        public async Task<ViewBalanceWithExpiringCoinOutput> ViewBalanceWithExpiringCoins(
            ViewBalanceWithExpiringCoinInput request, string authorization)
        {
            var rewardViewPointResult = await GetRewardAsync<ViewBalanceByWalletAddressOutput>(RewardApiUrl.OFFCHAIN_GET_BALANCE_BY_WALLET,
                new { WalletAddress = request.UserAddress, Mode = "WithExpiring" });
            return new ViewBalanceWithExpiringCoinOutput()
            {
                Result = 200, Message = "Success", Items = new RewardMemberViewPoint()
                {
                    TokenBalance = rewardViewPointResult.Items.Balance,
                    ExpiringDate = rewardViewPointResult.Items.ExpiringDate,
                    ExpiringTokenAmount = rewardViewPointResult.Items.ExpiringAmount ?? 0
                }
            };
        }

        public async Task<RewardMemberVerifyOrCreateOutput> Create(RewardMemberCreateInput input, string authorization)
        {
            var nationalId = await getNational(authorization);
            var avatarLink = await GetAvatarLink(input.AvatarFromBase64String);
            var createMemberDto = new RewardMemberVerifyCreateMemberAndUpdateProviderInput()
            {
                NationalId = nationalId,
                IdCard = input.IdCard,
                PartnerPhoneNumber = input.PartnerPhoneNumber,
                Type = "Member",
                Phone = input.Phone,
                Gender = !(new List<string> { "M", "F", "O" }).Contains(input.Gender) ? "O" : input.Gender,
                Status = "A",
                RankTypeCode = "Customer",
                IsDeleted = false,
                Address = input.Address,
                Dob = input.Dob,
                Name = input.Name,
                Email = input.Email,
                PointUsingOrdinary = input.PointUsingOrdinary,
                HashAddress = "",
                RegionCode = "",
                FullRegionCode = "",
                MemberTypeCode = "",
                FullMemberTypeCode = "",
                ChannelType = "",
                FullChannelTypeCode = "",
                StandardMemberCode = nationalId,
                ReferralCode = GenReferralCode(input.Phone, nationalId),
                Avatar = avatarLink,
                ReferenceId = input.ReferenceId,
                RegisterType = input.RegisterType,
                NationId = 0,
                CityId = 0,
                DistrictId = 0,
                WardId = 0,
                StreetDetail = "",
                ProviderId = nationalId,
                ProviderName = "Firebase",
            };
            var result = await PostRewardAsync<RewardMemberVerifyCreateMemberAndUpdateProviderOutput>(RewardApiUrl.MEMBER_VERIFY_AND_UPDATE_PROVIDER, createMemberDto);
            return new RewardMemberVerifyOrCreateOutput()
            {
                Messages = "Success",
                Result = new RewardMemberVerifyOrCreateOutputItem()
                {
                    MemberCode = result.MemberCode,
                    PhoneNumber = input.Phone,
                },
                Status = 200,
            };
        }

        public async Task<RewardRegisterWithEmailAndPasswordOutput> RegisterWithEmailAndPassword(RewardRegisterWithEmailAndPasswordInput input)
        {
            var userCheck = new object();
            try
            {
                if (await CheckMemberFromFirebase(input.Email))
                {
                    return new RewardRegisterWithEmailAndPasswordOutput()
                    {
                        Status = true,
                        Messages = "That email is exists",
                    };
                }
                var auth = FirebaseAdmin.Auth.FirebaseAuth.DefaultInstance;
                await auth.CreateUserAsync(new FirebaseAdmin.Auth.UserRecordArgs()
                {
                    Email = input.Email,
                    Password = input.Password,
                    EmailVerified = false,
                });
                return new RewardRegisterWithEmailAndPasswordOutput()
                {
                    Status = true,
                    Messages = "Register member success"
                };

            }
            catch (Exception e)
            {
                return new RewardRegisterWithEmailAndPasswordOutput()
                {
                    Status = false,
                    Messages = e.Message
                };
            }

        }

        public async Task<RewardGetMemberBalanceByNationalIdOutput> GetBalanceMember(string memberCode)
        {
            var request = new RewardGetMemberBalanceByNationalIdInput()
            {
                NationalId = memberCode,
            };
            return await GetRewardAsync<RewardGetMemberBalanceByNationalIdOutput>(RewardApiUrl.MEMBER_GET_TOKEN_BALANCE, request);
        }

        private async Task<bool> CheckMemberFromFirebase(string email)
        {
            try
            {
                var auth = FirebaseAdmin.Auth.FirebaseAuth.DefaultInstance;
                var userCheck = await auth.GetUserByEmailAsync(email);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<RewardMemberVerifyReferralCodeOutput> VerifyReferralCode(RewardMemberVerifyReferralCodeInput input)
        {
            var referralDto = new RewardMemberVerifyReferralCodeDto()
            {
                MerchantId = Convert.ToInt32(_configuration.GetSection("Reward:MerchantId").Value),
                DistributionChannelList = input.DistributionChannelList,
                NationalId = input.NationalId,
                ReferenceAmount = input.ReferenceAmount,
                ReferralCode = input.ReferralCode,
            };
            return await PostRewardAsync<RewardMemberVerifyReferralCodeOutput>(RewardApiUrl.MEMBER_VERIFY_REFERRAL_CODE, referralDto);
        }

        public async Task<RewardMemberTempPointTransGetByIdOutput> GetTempPointTransByMemberId(RewardMemberTempPointTransGetByIdInput input)
        {
            var request = new RewardMemberTempPointTransGetByIdDto()
            {
                sorting = input.Sorting,
                maxResultCount = input.MaxResultCount,
                skipCount = input.SkipCount,
                MemberId = input.MemberId,
                NationalId = input.NationalId,
                MerchantIdFilter = input.MerchantIdFilter,
                StatusFilter = input.StatusFilter,
                FromDateFilter = input.FromDateFilter,
                ToDateFilter = input.ToDateFilter,
                OrderCodeFilter = input.OrderCodeFilter,
                ActionCodeFilter = input.ActionCodeFilter,
                ActionTypeFilter = input.ActionTypeFilter,
            };
            return await GetRewardAsync<RewardMemberTempPointTransGetByIdOutput>(RewardApiUrl.MEMBER_GET_TEMP_POINT_BY_ID, request, MerchantNameConfig.VPID);
        }

        public async Task<RewardGetAllMerchantExchangeOutput> GetAllMerchantExchange(RewardGetAllMerchantExchangeInput input)
        {
            var request = new RewardGetAllMerchantExchangeDto()
            {
                StatusFilter = "A",
                TypeFilter = "Exchange",
                skipCount = input.skipCount,
                maxResultCount = input.maxResultCount,
                sorting = input.sorting,
            };
            return await GetRewardAsync<RewardGetAllMerchantExchangeOutput>(RewardApiUrl.MEMBER_VIEW, request);
        }

        public async Task<LoyaltyResponse<GatewayMemberTokenTransGetByIdOutput>> GetTokenTransByMemberId(RewardMemberTokenTransGetByIdInput input)
        {
            //var getWalletAddressRequest = new RewardMemberViewPointInput()
            //{
            //    Id = input.MemberId,
            //    NationalId = input.NationalId
            //};

            //var walletAddress = await GetRewardAsync<RewardGetWalletAddressResponse>(RewardApiUrl.GET_WALLET_ADDRESS, getWalletAddressRequest);

            //var request = new RewardMemberTokenTransGetByIdDto()
            //{
            //    sorting = input.Sorting,
            //    maxResultCount = input.MaxResultCount,
            //    skipCount = input.SkipCount,
            //    WalletAddress = getWalletAddressRequest.NationalId,
            //    MerchantIdFilter = input.MerchantIdFilter,
            //    FromDateFilter = input.FromDateFilter,
            //    ToDateFilter = input.ToDateFilter,
            //    OrderCodeFilter = input.OrderCodeFilter,
            //    ActionCodeFilter = input.ActionCodeFilter,
            //    ActionTypeFilter = input.ActionTypeFilter,
            //};

            var result = await GetRewardAsync<RewardMemberTokenTransGetByIdOutput>(RewardApiUrl.MEMBER_GET_TOKEN_TRANS_BY_ID, input, MerchantNameConfig.VPBank);

            return new LoyaltyResponse<GatewayMemberTokenTransGetByIdOutput>()
            {
                Result = new GatewayMemberTokenTransGetByIdOutput()
                {
                    Items = result.Items,
                    TotalCount = result.TotalCount
                },
                Success = true
            };
        }

        public async Task<RewardAddPointUsingOrdinaryOutput> AddPointUsingOrdinary(RewardAddPointUsingOrdinaryInput input)
        {
            return await PostRewardAsync<RewardAddPointUsingOrdinaryOutput>(RewardApiUrl.MEMBER_ADD_POINT_USING_ORDINARY, input);
        }

        public async Task<RewardMemberVerifyIsIdCardVerifiedOutput> VerifyIsIdCardVerified(RewardMemberVerifyIsIdCardVerifiedInput input)
        {
            return await GetRewardAsync<RewardMemberVerifyIsIdCardVerifiedOutput>(RewardApiUrl.MEMBER_VERIFY_IDCARD, input);
        }

        private string GenReferralCode(string phoneNumber, string nationalId)
        {
            if (!string.IsNullOrEmpty(phoneNumber))
            {
                return phoneNumber.Replace("+84", "0");
            }
            else
            {
                return null;
                //return nationalId;
            }
        }

        private async Task<string> GetAvatarLink(string FromBase64String)
        {
            if (string.IsNullOrWhiteSpace(FromBase64String))
            {
                return null;
            }
            var link = ""; /*await _uploadImageSevice.UploadImage(FromBase64String)*/
            return link;
        }

        public async Task<RewardMemberVerifyPinCodeResponse> VerifyPinCode(RewardMemberVerifyPinCodeRequest input)
        {
            var auth = FirebaseAuth.DefaultInstance;
            var userRecord = await auth.GetUserByPhoneNumberAsync(input.PhoneNumber);
            var memberCode = userRecord.Uid;
            var nameToken = "Member_" + memberCode;

            var token = await _cache.GetStringAsync(nameToken);
            try
            {
                if (string.IsNullOrEmpty(token))
                {
                    var cacheAllowRenewTokenFlagCacheOptions = new DistributedCacheEntryOptions()
                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(60));
                    await _cache.SetStringAsync(nameToken, "1", cacheAllowRenewTokenFlagCacheOptions);
                }
                else
                {
                    return new RewardMemberVerifyPinCodeResponse()
                    {
                        Success = false
                    };
                }

                var res = await PostRewardAsync<RewardMemberVerifyPinCodeDto>(RewardApiUrl.MEMBER_VERIFY_PINCODE, input);

                if (input.IsRevoke)
                {
                    await auth.RevokeRefreshTokensAsync(userRecord.Uid);
                }

                var customToken = await auth.CreateCustomTokenAsync(userRecord.Uid);

                var cacheAllowRenewTokenFlagCacheOptions2 = new DistributedCacheEntryOptions()
                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(3));
                await _cache.SetStringAsync(nameToken, "1", cacheAllowRenewTokenFlagCacheOptions2);

                return new RewardMemberVerifyPinCodeResponse()
                {
                    MemberCode = res.MemberCode,
                    PhoneNumber = res.PhoneNumber,
                    SessionId = res.SessionId,
                    Success = true,
                    CustomToken = customToken,
                };
            }
            catch (Exception ex)
            {
                await _cache.RemoveAsync(nameToken);
                throw ex;
            }
        }

        public async Task<RewardMemberHasPinCodeResponse> HasPinCode(RewardMemberHasPinCodeRequest input)
        {
            return await GetRewardAsync<RewardMemberHasPinCodeResponse>(RewardApiUrl.MEMBER_HAS_PINCODE, input);
        }

        public async Task<RewardMemberCreateOrUpdatePinCodeResponse> CreateOrUpdatePinCode(MobileAPICreateOrUpdatePinCodeInput input)
        {
            var result = await PostRewardAsync<RewardMemberCreateOrUpdatePinCodeResponse>(RewardApiUrl.MEMBER_CREATE_OR_UPDATE_PINCODE,
            new RewardMemberCreateOrUpdatePinCodeRequest()
            {
                MemberCode = input.MemberCode,
                PinCode = input.PinCode
            });

            if (result.Result == 200 && input.Type == 3)
            {
                var auth = FirebaseAuth.DefaultInstance;
                //var userRecord = await auth.GetUserAsync(input.MemberCode);
                await auth.RevokeRefreshTokensAsync(input.MemberCode);

                var customToken = await auth.CreateCustomTokenAsync(input.MemberCode);

                result.CustomToken = customToken;
            }
            return result;
        }

        //Loyalty
        public async Task<LoyaltyResponse<string>> UpdateNotificationSetting(UpdateNotificationSettingInput input)
        {
            return await _customersService.UpdateNotificationSetting(input);
        }

        public async Task<VerifyProviderIdByPhoneNumberResponse> VerifyProviderIdByPhoneNumber(VerifyProviderIdByPhoneNumberRequest input)
        {
            return await PostRewardAsync<VerifyProviderIdByPhoneNumberResponse>(RewardApiUrl.VERIFY_PROVIDER_ID_BY_PHONE_NUMBER, input);
        }

        public async Task<UpdateProviderOutPut> UpdateProvider(VerifyProviderIdByPhoneNumberRequest input)
        {
            return await PostRewardAsync<UpdateProviderOutPut>(RewardApiUrl.UPDATE_PROVIDER, input);
        }

        public async Task<RewardMemberVerifyOrCreateOutput> VerifyOrCreate(RewardMemberVerifyOrCreateInput input, string authorization)
        {
            var nationalId = await getNational(authorization);
            var avatarLink = await GetAvatarLink(input.AvatarFromBase64String);
            var createMemberDto = new RewardMemberVerifyCreateMemberAndUpdateProviderInput()
            {
                NationalId = nationalId,
                IdCard = input.IdCard,
                PartnerPhoneNumber = input.PartnerPhoneNumber,
                Type = "Member",
                Phone = input.Phone,
                Gender = input.Gender,
                Status = "A",
                RankTypeCode = "Customer",
                IsDeleted = false,
                Address = input.Address,
                Dob = input.Dob,
                Name = input.Name,
                Email = input.Email,
                PointUsingOrdinary = input.PointUsingOrdinary,
                HashAddress = "",
                RegionCode = "",
                FullRegionCode = "",
                MemberTypeCode = "",
                FullMemberTypeCode = "",
                ChannelType = "",
                FullChannelTypeCode = "",
                StandardMemberCode = nationalId,
                ReferralCode = GenReferralCode(input.Phone, nationalId),
                Avatar = avatarLink,
                ProviderId = nationalId,
                ProviderName = "Firebase",
            };
            var result = await PostRewardAsync<RewardMemberVerifyCreateMemberAndUpdateProviderOutput>(RewardApiUrl.MEMBER_VERIFY_AND_UPDATE_PROVIDER, createMemberDto);
            return new RewardMemberVerifyOrCreateOutput()
            {
                Messages = "Success",
                Result = new RewardMemberVerifyOrCreateOutputItem()
                {
                    MemberCode = result.MemberCode,
                    PhoneNumber = input.Phone,
                },
                Status = 200,
            };
        }
		
        public async Task<RewardMemberGetRefreshTokenOutput> GetRefreshToken(RewardMemberGetRefreshTokenInput input)
        {
            return await PostRewardAsync<RewardMemberGetRefreshTokenOutput>(RewardApiUrl.MEMBER_GET_REFRESH_TOKEN, input);
        }

        public async Task<RewardMemberSaveRefreshTokenOutput> SaveRefreshToken(RewardMemberSaveRefreshTokenInput input)
        {
            return await PostRewardAsync<RewardMemberSaveRefreshTokenOutput>(RewardApiUrl.MEMBER_SAVE_REFRESH_TOKEN, input);
        }

        public async Task<RewardMemberCreateRegisterLogOutput> CreateRegisterLog(RewardMemberCreateRegisterLogInput input)
        {
            var request = new RewardMemberCreateRegisterLogDto()
            {
                Code = LoyaltyHelper.GenTransactionCode("CreateMemberLogs"),
                DeviceId = input.DeviceId,
                ErrorMessage = input.ErrorMessage,
                ExtraData = input.ExtraData,
                Localtion = input.Localtion,
                PhoneNumber = input.PhoneNumber,
                Status = input.Status,
                Time = input.Time,
            };
            return await PostRewardAsync<RewardMemberCreateRegisterLogOutput>(RewardApiUrl.MEMBER_CREATE_REGISTER_LOG, request);
        }

        public async Task<UpdatePhoneNumberOutput> UpdatePhoneNumber(MobileUpdatePhoneNumberInput input)
        {
            var auth = FirebaseAuth.DefaultInstance;

            var oldPhone = input.OldPhoneNumber;
            UserRecord user = null;


            //Update phone in firebase

            try
            {
                user = await auth.GetUserAsync(input.MemberCode);
                //oldPhone = user.PhoneNumber;
            }
            catch
            {
                return new UpdatePhoneNumberOutput()
                {
                    Status = UpdatePhoneNumberStatus.MemberNotExists,
                    Message = "Member does not exist"
                };
            }

            //try
            //{
            //    await auth.GetUserByPhoneNumberAsync(input.PhoneNumber);
            //    return new UpdatePhoneNumberOutput()
            //    {
            //        Status = UpdatePhoneNumberStatus.DuplicatePhoneNumber,
            //        Message = "Phone number already exist"
            //    };
            //}
            //catch { }

            //try
            //{
            //    await auth.UpdateUserAsync(new UserRecordArgs()
            //    {
            //        PhoneNumber = input.PhoneNumber,
            //        Uid = user.Uid
            //    });
            //}
            //catch
            //{
            //    return new UpdatePhoneNumberOutput()
            //    {
            //        Status = UpdatePhoneNumberStatus.SystemError,
            //        Message = "System error"
            //    };
            //}

            var inputBE = new UpdatePhoneNumberInput()
            {
                MemberCode = input.MemberCode,
                PhoneNumber = input.PhoneNumber
            };

            try
            {
                await PutRewardAsync<RewardMemberUpdateOutput>(RewardApiUrl.MEMBER_UPDATE_PHONE_NUMBER, inputBE);
            }
            catch (Exception ex)
            {
                var flag = false;
                for (int i = 0; i < 2 && !flag && !string.IsNullOrEmpty(oldPhone); i++)
                {
                    try
                    {
                        await auth.UpdateUserAsync(new UserRecordArgs()
                        {
                            PhoneNumber = oldPhone,
                            Uid = input.MemberCode
                        });
                        flag = true;
                    }
                    catch { }
                }

                var resultEx = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                var result = new UpdatePhoneNumberOutput();
                switch (resultEx.Code)
                {
                    case "MemberNotExits":
                        result.Status = UpdatePhoneNumberStatus.MemberNotExists;
                        result.Message = "Member does not exist";
                        break;
                    case "DuplicatePhoneNumber":
                        result.Status = UpdatePhoneNumberStatus.DuplicatePhoneNumber;
                        result.Message = "Phone number already exist";
                        break;
                    default:
                        result.Status = UpdatePhoneNumberStatus.SystemError;
                        result.Message = "System error";
                        break;
                }
                return result;
            }


            try
            {
                await _customersService.UpdatePhoneNumber(inputBE);
            }
            catch (Exception ex)
            {
                var flag = false;
                for (int i = 0; i < 2 && !flag && !string.IsNullOrEmpty(oldPhone); i++)
                {
                    try
                    {
                        await auth.UpdateUserAsync(new UserRecordArgs()
                        {
                            PhoneNumber = oldPhone,
                            Uid = input.MemberCode
                        });
                        flag = true;
                    }
                    catch { }
                }

                flag = false;
                inputBE.PhoneNumber = oldPhone;

                for (int i = 0; i < 2 && !flag; i++)
                {
                    try
                    {
                        await PutRewardAsync<RewardMemberUpdateOutput>(RewardApiUrl.MEMBER_UPDATE_PHONE_NUMBER, inputBE);
                        flag = true;
                    }
                    catch { }
                }

                var resultEx = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                var result = new UpdatePhoneNumberOutput();
                switch (resultEx.Code)
                {
                    case "101":
                        result.Status = UpdatePhoneNumberStatus.MemberNotExists;
                        result.Message = "Member does not exist";
                        break;
                    case "202":
                        result.Status = UpdatePhoneNumberStatus.DuplicatePhoneNumber;
                        result.Message = "Phone number already exist";
                        break;
                    default:
                        result.Status = UpdatePhoneNumberStatus.SystemError;
                        result.Message = "System error";
                        break;
                }
                return result;
            }

            return new UpdatePhoneNumberOutput()
            {
                Status = UpdatePhoneNumberStatus.Success,
                Message = "Success"
            };
            //}
            //catch (Exception ex)
            //{
            //    var errorResponse = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
            //    output.Status = StatusReturnConst.Error;
            //    output.Message = errorResponse.Message;
            //}
            //}
            //else
            //{
            //    output.Message = "Incorrect OTP Code!";
            //}
            //return output;
        }

        public async Task<RewardMemberAccountHavePhoneNumberOutput> AccountHavePhoneNumber(RewardMemberAccountHavePhoneNumberInput input)
        {
            return await PostRewardAsync<RewardMemberAccountHavePhoneNumberOutput>(RewardApiUrl.MEMBER_ACCOUNT_HAVE_PHONE_NUMBER, input);
        }

        public async Task<RewardMemberRevokeTokenResponse> RevokeToken(string authorization)
        {
            var nationalId = await getNational(authorization);
            var auth = FirebaseAuth.DefaultInstance;
            var nameToken = "Member_" + nationalId;

            var token = await _cache.GetStringAsync(nameToken);
            try
            {
                if (string.IsNullOrEmpty(token))
                {
                    var cacheAllowRenewTokenFlagCacheOptions = new DistributedCacheEntryOptions()
                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(60));
                    await _cache.SetStringAsync(nameToken, "1", cacheAllowRenewTokenFlagCacheOptions);
                }
                else
                {
                    return new RewardMemberRevokeTokenResponse()
                    {
                        Result = (int)RevokeTokenErrorCode.CanNotVerifyPinCodeNow,
                        Message = "Can Not Verify PinCode Now"
                    };
                }

                var userRecord = await auth.GetUserAsync(nationalId);
                var phoneNumber = userRecord.PhoneNumber;

                var res = new RewardMemberHasPinCodeResponse();

                try
                {
                    res = await GetRewardAsync<RewardMemberHasPinCodeResponse>(RewardApiUrl.MEMBER_HAS_PINCODE, new RewardMemberHasPinCodeRequest()
                    {
                        PhoneNumber = phoneNumber
                    });
                }
                catch (Exception ex) { }

                if (res != null && res.IsLocked)
                {
                    await _cache.RemoveAsync(nameToken);
                    return new RewardMemberRevokeTokenResponse()
                    {
                        Result = (int)RevokeTokenErrorCode.AccountIsLocked,
                        Message = "Account Is Locked"
                    };
                }

                //result.HasPinCode = resultReward.HasPinCode;
                //result.IsLocked = resultReward.IsLocked;

                await auth.RevokeRefreshTokensAsync(userRecord.Uid);

                var customToken = await auth.CreateCustomTokenAsync(userRecord.Uid);

                var cacheAllowRenewTokenFlagCacheOptions2 = new DistributedCacheEntryOptions()
                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(3));
                await _cache.SetStringAsync(nameToken, "1", cacheAllowRenewTokenFlagCacheOptions2);

                return new RewardMemberRevokeTokenResponse()
                {
                    Result = (int)RevokeTokenErrorCode.Success,
                    CustomToken = customToken,
                    Message = "Success"
                };
            }
            catch (Exception ex)
            {
                await _cache.RemoveAsync(nameToken);
                return new RewardMemberRevokeTokenResponse()
                {
                    Result = (int)RevokeTokenErrorCode.IntenalServerError,
                    CustomToken = null,
                    Message = ex.Message
                };
            }
        }

        public async Task<RewardMemberGetInfoOutput> GetInfo(RewardMemberGetInfoInput input)
        {
            var request = new RewardMemberGetInfoInput()
            {
                NationalId = input.NationalId,
                MemberId = input.MemberId,
                PhoneNumber = input.PhoneNumber,
                IdCard = input.IdCard,
            };
            return await GetRewardAsync<RewardMemberGetInfoOutput>(RewardApiUrl.MEMBER_GET_INFO, request);
        }

        public async Task<RewardMemberRedeemOutput> RedeemTransaction(RewardMemberRedeemInput input)
        {
            return await PostRewardAsync<RewardMemberRedeemOutput>(RewardApiUrl.REDEEM_GIFT_TRANSACTION_CREATE, input, MerchantNameConfig.VPID);
        }

        public async Task<RewardMemberRevertRedeemOutput> RevertRedeemTransaction(RewardMemberRevertRedeemInput input, string url = null)
        {
            var calledUrl = RewardApiUrl.REDEEM_GIFT_TRANSACTION_REVERT; // Default là URL redeem cũ đang dùng.
            if (!string.IsNullOrWhiteSpace(url))
            {
                calledUrl = url;
            }
            return await PostRewardAsync<RewardMemberRevertRedeemOutput>(calledUrl, input, MerchantNameConfig.VPID);
        }

        public async Task<RewardMemberFirstActionMemberOutput> FirstActionMember(RewardMemberFirstActionMemberInput input)
        {
            try
            {
                return await PostRewardAsync<RewardMemberFirstActionMemberOutput>(RewardApiUrl.MEMBER_FIRST_ACTION_MEMBER, input);
            } catch {
                return null;
            }
        }

        public async Task<RewardMemberGetMemberLoginByFirebaseIdOutput> GetMemberLoginByFirebaseId(string authorization)
        {
            var nationalId = await getNational(authorization);
            var request = new RewardMemberGetMemberLoginByFirebaseIdInput()
            {
                FirebaseId = nationalId,
            };
            return await GetRewardAsync<RewardMemberGetMemberLoginByFirebaseIdOutput>(RewardApiUrl.MEMBER_GET_LOGIN_BY_FIREBASE_ID, request);
        }

        public async Task<RewardMemberGetUsagePriorityOutput> GetUsagePriority(RewardMemberGetUsagePriorityInput input)
        {
            return await GetRewardAsync<RewardMemberGetUsagePriorityOutput>(RewardApiUrl.MEMBER_GET_USAGE_PRIORITY, input);
        }

        public async Task<RewardMemberGetCashoutAndTopupInfoOutput> GetCashoutAndTopupInfo(RewardMemberGetCashoutAndTopupInfoInput input)
        {
            return await GetRewardAsync<RewardMemberGetCashoutAndTopupInfoOutput>(RewardApiUrl.MEMBER_GET_CASHOUT_AND_TOPUP_INFO, input);
        }

        public async Task<RewardMemberUpdateUsagePriorityOutput> UpdateUsagePriority(RewardMemberUpdateUsagePriorityInput input)
        {
            return await PostRewardAsync<RewardMemberUpdateUsagePriorityOutput>(RewardApiUrl.MEMBER_UPDATE_USAGE_PRIORITY, input);
        }

        public async Task<RewardMemberUpdatePointUsageTypeOutput> UpdatePointUsageType(RewardMemberUpdatePointUsageTypeInput input)
        {
            return await PostRewardAsync<RewardMemberUpdatePointUsageTypeOutput>(RewardApiUrl.MEMBER_UPDATE_POINT_USAGE_TYPE, input);
        }

        public async Task<RewardMemberSendOtpForConnectMerchantOutput> AutoConnectMember(
            RewardMemberAutoConnectMemberInput input)
        {
            // Validation
            if (input.LoyaltyInfo == null)
            {
                CommonHelper.GetErrorValidation("0", "Invalid data request");
            }

            if (string.IsNullOrWhiteSpace(input.LinkID_PhoneNumber))
            {
                CommonHelper.GetErrorValidation("LinkIDPhoneRequired", "LinkID Phone Number is required");
            }

            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.IdCard))
            {
                CommonHelper.GetErrorValidation("IDCardRequired", "ID Card is required");
            }

            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.Phone))
            {
                CommonHelper.GetErrorValidation("VPBPhoneRequired", "VPBank PhoneNumber is required");
            }

            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.Cif))
            {
                CommonHelper.GetErrorValidation("CifCodeRequierd", "Cif code is required");
            }

            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.MemberLoyaltyCode))
            {
                CommonHelper.GetErrorValidation("LoyaltyMemberCodeRequired", "Loyalty member code is required");
            }

            if (!input.LoyaltyInfo.Dob.HasValue)
            {
                CommonHelper.GetErrorValidation("BirthdayRequired", "Date of birth is required");
            }

            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.Phone))
            {
                CommonHelper.GetErrorValidation("PhoneRequired", "Phone is required");
            }

            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.Segment))
            {
                CommonHelper.GetErrorValidation("SegmentRequired", "Segment is required");
            }

            if (!string.IsNullOrWhiteSpace(input.LoyaltyInfo.Gender))
            {
                if (!(new List<string> { "M", "F", "O" }).Contains(input.LoyaltyInfo.Gender))
                {
                    CommonHelper.GetErrorValidation("202", "Gender must be one of the following values: M,F,O");
                }
            }

            var __reqID = DateTime.UtcNow.Millisecond;
            _logger.LogInformation("[" + __reqID + "] >> Handle ConnectMerchant for this payload: " +
                                   JsonConvert.SerializeObject(input));
            // check connect history first
            await CheckHistoryConnect(new CheckConnectHistoryInput()
            {
                CifCode = input.LoyaltyInfo.Cif,
                LinkID_PhoneNumber = input.LinkID_PhoneNumber,
            });
            var merchantId = Convert.ToInt32(_configuration.GetSection("RewardVPBank:MerchantId").Value);
            var resultMemberId = 0;
            var walletAddress = "";
            var checkListHis = await _loyaltyUtilsService.GetMemberInfoByCif(new GetMemberInfoByCifInput()
            {
                Cif = input.LoyaltyInfo.Cif,
            });

            if (checkListHis != null && checkListHis.ConnectionHistory != null && checkListHis.ConnectionHistory.Count > 0)
            {
                var listPhoneConnected =
                    checkListHis.ConnectionHistory.Select(x => x.LinkIDPhoneNumber).ToList();
                if (listPhoneConnected.Contains(input.LinkID_PhoneNumber))
                {
                    // OK, vậy là đã từng có kết nối giữa CIF & SĐT LinkID. Cho phép tạo kết nối CONFIRMED.
                    // Đã tồn tại ở HISTORY có nghĩa là đã có account ở LinkID. Nhưng cứ check trên Operator cho chắc
                    try
                    {
                        var linkidMemInfo = await GetInfo(new RewardMemberGetInfoInput()
                        {
                            PhoneNumber = input.LinkID_PhoneNumber,
                        });
                        // Ko có exception nên OK, process tiếp
                        var tempReqConnectToRw = new RewardMemberSaveRefreshTokenInputV2()
                        {
                            MemberCode = linkidMemInfo.NationalId,
                            MerchantId = merchantId,
                            RefreshToken = "NA",
                            IsChangedLoyalty = true,
                            ConnectSource = "AutoConnect",
                            ReferenceData = JsonConvert.SerializeObject(new { Source = "AutoConnect" }),
                            MemberLoyaltyInfo = new MemberConnectLoyaltyInfoInput()
                            {
                                Cif = input.LoyaltyInfo.Cif,
                                MemberLoyaltyCode = input.LoyaltyInfo.Cif,
                                Name = input.LoyaltyInfo.Name,
                                Address = input.LoyaltyInfo.Address,
                                Avatar = "",
                                ChannelType = "",
                                Dob = input.LoyaltyInfo.Dob,
                                PartnerPhoneNumber = input.LoyaltyInfo.Phone,
                                Phone = input.LoyaltyInfo.Phone,
                                StandardMemberCode = input.LoyaltyInfo.Cif,
                            }
                        };
                        var rewardResult = await PostRewardAsync<RewardMemberSaveRefreshTokenV2Output>(
                            RewardApiUrl.SaveRefreshTokenV2,
                            tempReqConnectToRw);
                        var loyaltyResult = await _loyaltyMemberService.VerifyConfirmConnectForConnect(
                            new VerifyConfirmConnectForConnectInput()
                            {
                                CifCode = input.LoyaltyInfo.Cif,
                                LinkID_MemberID = linkidMemInfo.Id,
                                LinkID_PhoneNumber = linkidMemInfo.PhoneNumber,
                                LinkID_WalletAddress = linkidMemInfo.UserAddress,
                                ConnectSource = 1000
                            });
                        resultMemberId = linkidMemInfo.Id;
                        // Send ack
                        await RewardSendAckAfterConnected(new RewardSendAckAfterConnectedInput()
                        {
                            MemberId = linkidMemInfo.Id, MerchantId = merchantId,
                            NationalId = linkidMemInfo.NationalId
                        });
                        var innerRet = new RewardMemberSendOtpForConnectMerchantOutput()
                        {
                            Result = 200,
                            ExtraInfo = new Dictionary<string, int>()
                            {
                                { "AutoConnect", 1 }
                            }
                        };
                        if (resultMemberId > 0)
                        {
                            innerRet.ExtraInfo.Add("MemberId", resultMemberId);
                        }

                        return innerRet;
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(
                            "[" + __reqID + "] >> Create CONFIRMED connection, got error: " + e.Message);
                        _logger.LogError(e.StackTrace);
                        throw e;
                    }
                }
            }

            if (checkListHis is { ConnectionHistory: { Count: 0 } })
            {
                // Vậy là chưa từng có kết nối giữa CIF và LinkID bao giờ
                // Check sự tồn tại của sđt trên LinkID system
                RewardMemberGetInfoOutput linkidMemInfo = null;
                try
                {
                    linkidMemInfo = await GetInfo(new RewardMemberGetInfoInput()
                    {
                        PhoneNumber = input.LinkID_PhoneNumber,
                    });
                }
                catch (Exception e)
                {
                    _logger.LogError(
                        "[" + __reqID + "] >> Check member existence in the case that no history, got error: " +
                        e.Message);
                    _logger.LogError(e.StackTrace);
                    // Nếu mã lỗi từ operator bắn về là MemberNotExistsOrNotActive thì sẽ đi tiếp
                    if (e.GetType() == typeof(RewardException))
                    {
                        var res = await _exceptionReponseService.GetExceptionRewardReponse(e);
                        if (res.Code != "MemberNotExitsOrNotActive")
                        {
                            throw e;
                        }
                    }
                }

                // Nếu chưa tồn tại thì sẽ tạo ra account
                if (linkidMemInfo == null)
                {
                    var generatedMemCode = "AUTO";
                    var guid = Guid.NewGuid();
                    generatedMemCode += guid.ToString().Replace("-", "");
                    generatedMemCode = generatedMemCode.ToUpper();
                    var doneCreated = await CreateLinkIdMember(new CreateLinkIdMemberInput()
                    {
                        Address = input.LoyaltyInfo.Address,
                        StandardMemberCode = generatedMemCode,
                        IdCard = input.LoyaltyInfo.IdCard,
                        PartnerPhoneNumber = input.LoyaltyInfo.Phone,
                        Phone = input.LinkID_PhoneNumber,
                        Name = input.LoyaltyInfo.Name,
                        Email = input.LoyaltyInfo.Email,
                        Gender = input.LoyaltyInfo.Gender,
                        Dob = input.LoyaltyInfo.Dob
                    });
                    if (doneCreated is { Items: { } })
                    {
                        resultMemberId = doneCreated.Items.Id;
                        walletAddress = doneCreated.Items.UserAddress;
                    }

                    await CreateUnconfirmedConnection(new CreateUnconfirmedConnectionInputSendOperator()
                    {
                        ConnectSource = "AutoConnect",
                        MemberCode = generatedMemCode,
                        MerchantId = merchantId,
                        ReferenceData = "{}",
                        RefreshToken = "NA",
                        IsChangedLoyalty = true,
                        MemberLoyaltyInfo = new MemberLoyaltyInfoSendOperator()
                        {
                            Address = input.LoyaltyInfo.Address,
                            StandardMemberCode = input.LoyaltyInfo.Cif,
                            IdCard = input.LoyaltyInfo.IdCard,
                            Phone = input.LoyaltyInfo.Phone,
                            Name = input.LoyaltyInfo.Name,
                            Email = input.LoyaltyInfo.Email,
                            Gender = input.LoyaltyInfo.Gender,
                            Dob = input.LoyaltyInfo.Dob,
                            Cif = input.LoyaltyInfo.Cif,
                            MemberLoyaltyCode = input.LoyaltyInfo.Cif,
                            PartnerPhoneNumber = input.LoyaltyInfo.Phone,
                        }
                    });
                    _logger.LogInformation("[" + __reqID +
                                           "] Done create unconfirmed connection in operator. LinkID MemberCode: "
                                           + generatedMemCode + "; Cif: " + input.LoyaltyInfo.Cif);

                    // [Gọi sang vpb loyalty]
                    // Tạo connect dạng UnConfirmed ở bảng UserMapping (và UserMappingHistory)
                    await _loyaltyUtilsService.CreateUnConfirmConnection(new CreateUnConfirmConnectionInput()
                    {
                        LinkIdMemberId = resultMemberId, Cif = input.LoyaltyInfo.Cif, LinkIdWallet = walletAddress,
                        LinkIdPhoneNumber = input.LinkID_PhoneNumber, ConnectSource = 1000
                    });
                    _logger.LogInformation("[" + __reqID +
                                           "] Done create unconfirmed connection in vpbank loy. LinkID MemberCode: "
                                           + generatedMemCode + "; Cif: " + input.LoyaltyInfo.Cif);
                    var innerRet = new RewardMemberSendOtpForConnectMerchantOutput()
                    {
                        Result = 200,
                        ExtraInfo = new Dictionary<string, int>()
                        {
                            { "AutoConnect", 2 }
                        }
                    };
                    if (resultMemberId > 0)
                    {
                        innerRet.ExtraInfo.Add("MemberId", resultMemberId);
                    }

                    return innerRet;
                }
                else
                {
                    // Nếu CÓ TÀI KHOẢN VỚI SỐ NÀY RỒI, THÌ TẠO LUÔN KẾT NỐI Confirmed
                    var tempReqConnectToRw = new RewardMemberSaveRefreshTokenInputV2()
                    {
                        MemberCode = linkidMemInfo.NationalId,
                        MerchantId = merchantId,
                        RefreshToken = "NA",
                        IsChangedLoyalty = true,
                        ConnectSource = "AutoConnect",
                        ReferenceData = JsonConvert.SerializeObject(new { Source = "AutoConnect" }),
                        MemberLoyaltyInfo = new MemberConnectLoyaltyInfoInput()
                        {
                            Cif = input.LoyaltyInfo.Cif,
                            MemberLoyaltyCode = input.LoyaltyInfo.Cif,
                            Name = input.LoyaltyInfo.Name,
                            Address = input.LoyaltyInfo.Address,
                            Avatar = "",
                            ChannelType = "",
                            Dob = input.LoyaltyInfo.Dob,
                            PartnerPhoneNumber = input.LoyaltyInfo.Phone,
                            Phone = input.LoyaltyInfo.Phone,
                            StandardMemberCode = input.LoyaltyInfo.Cif,
                        }
                    };
                    var rewardResult = await PostRewardAsync<RewardMemberSaveRefreshTokenV2Output>(
                        RewardApiUrl.SaveRefreshTokenV2,
                        tempReqConnectToRw);
                    var loyaltyResult = await _loyaltyMemberService.VerifyConfirmConnectForConnect(
                        new VerifyConfirmConnectForConnectInput()
                        {
                            CifCode = input.LoyaltyInfo.Cif,
                            LinkID_MemberID = linkidMemInfo.Id,
                            LinkID_PhoneNumber = linkidMemInfo.PhoneNumber,
                            LinkID_WalletAddress = linkidMemInfo.UserAddress,
                            ConnectSource = 1000
                        });
                    resultMemberId = linkidMemInfo.Id;
                    // Send ack
                    await RewardSendAckAfterConnected(new RewardSendAckAfterConnectedInput()
                    {
                        MemberId = linkidMemInfo.Id, MerchantId = merchantId,
                        NationalId = linkidMemInfo.NationalId
                    });
                    var innerRet = new RewardMemberSendOtpForConnectMerchantOutput()
                    {
                        Result = 200,
                        ExtraInfo = new Dictionary<string, int>()
                        {
                            { "AutoConnect", 1 }
                        }
                    };
                    if (resultMemberId > 0)
                    {
                        innerRet.ExtraInfo.Add("MemberId", resultMemberId);
                    }

                    return innerRet;
                }
            }

            throw new Exception("Invalid data. End of flow! It must be one of cases above");
        }
        
        /*
         * Thực hiện tự động kết nối cho case Cif có UserMapping.Status = 0. Phiên bản adapt cho NEO - LINKID-794
         */
        public async Task<RewardMemberSendOtpForConnectMerchantOutput> AutoConnectMember2(
            RewardMemberAutoConnectMemberInput input)
        {
            // Validation
            if (input.LoyaltyInfo == null)
            {
                CommonHelper.GetErrorValidation("0", "Invalid data request");
            }

            if (string.IsNullOrWhiteSpace(input.LinkID_PhoneNumber))
            {
                CommonHelper.GetErrorValidation("LinkIDPhoneRequired", "LinkID Phone Number is required");
            }

            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.IdCard))
            {
                CommonHelper.GetErrorValidation("IDCardRequired", "ID Card is required");
            }

            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.Phone))
            {
                CommonHelper.GetErrorValidation("VPBPhoneRequired", "VPBank PhoneNumber is required");
            }

            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.Cif))
            {
                CommonHelper.GetErrorValidation("CifCodeRequierd", "Cif code is required");
            }

            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.MemberLoyaltyCode))
            {
                CommonHelper.GetErrorValidation("LoyaltyMemberCodeRequired", "Loyalty member code is required");
            }
            

            var __reqID = DateTime.UtcNow.Millisecond;
            _logger.LogInformation("[" + __reqID + "] >> Handle AutoConnectMember2 for this payload: " +
                                   JsonConvert.SerializeObject(input));
            // Make sure rằng Cif đang chưa có UserMapping.Status = 0
            var checkListHis = await _loyaltyUtilsService.GetMemberInfoByCif(new GetMemberInfoByCifInput()
            {
                Cif = input.LoyaltyInfo.Cif,
            });
            if (checkListHis != null && checkListHis.UserInfo != null &&
                checkListHis.UserInfo.IdCard != input.LoyaltyInfo.IdCard)
            {
                _logger.LogInformation("[" + __reqID + "] >> Handle AutoConnectMember2  - Input IDCard not same as IdCard in DB of the CIF - input = " +
                                       input.LoyaltyInfo.IdCard + " - Found In DB = " + checkListHis.UserInfo.IdCard);
                CommonHelper.GetErrorValidation("InputIdCardNotSameAsLoyalty", 
                    "Input's IDCard not same as IDCard of the CIF in DB");
            }

            if (checkListHis != null && checkListHis.IsSuccess
                                     && checkListHis.LinkIdConnectionStatus != 0
                                     && checkListHis.LinkIdConnectionStatus != 2)
            {
                _logger.LogInformation("[" + __reqID + "] >> Handle AutoConnectMember2 for this payload: " +
                                       JsonConvert.SerializeObject(input) + " -  CIF has usermapping.Status != 0 and 2 ");
                CommonHelper.GetErrorValidation("InvalidConnectionStatus", 
                    "AutoConnectV2 Only Accept UserMapping.Status = 0 Or 2");
            }
            // Truyền thông tin LinkID Phone, Cif, IdCard lên operator
            var merchantId = Convert.ToInt32(_configuration.GetSection("RewardVPBank:MerchantId").Value);
            // CASE 1: Cif mới nguyên, chưa có lịch sử kết nối ở onprem
            if (checkListHis != null && checkListHis.LinkIdConnectionStatus == 0 && (
                    checkListHis.ConnectionHistory == null || checkListHis.ConnectionHistory.Count == 0)) 
            {
                var operatorResponse = await PostRewardAsync<CheckExistenceAndConnectionWithMerchantOutput>(RewardApiUrl.CheckExistenceAndConnectionWithMerchant,
                    new CheckExistenceAndConnectionWithMerchantInput()
                    {
                        MerchantId = merchantId, IdCard = input.LoyaltyInfo.IdCard,
                        LinkIdPhoneNumber = input.LinkID_PhoneNumber,
                    });
                _logger.LogInformation("[" + __reqID + "] >> Handle AutoConnectMember2 - operatorResponse =  " +
                                       JsonConvert.SerializeObject(operatorResponse));
                if (operatorResponse != null && operatorResponse.RsCheckIdCard != null)
                {
                    if (operatorResponse.RsCheckIdCard.IsExist &&
                        operatorResponse.RsCheckIdCard.CorrespondingMemberPhone != input.LoyaltyInfo.Phone)
                    {
                        // Step 1: Nếu IdCard của NEO mà đang gắn với 1 TK linkid khác, và sđt đó != NEO phone:
                        // Thì BÁO LỖI
                        CommonHelper.GetErrorValidation("IdCardNotAvailable", 
                            "Input's IdCard already belong to another LinkID Member");
                    }
                }

                if (operatorResponse != null && operatorResponse.LinkIdMember != null)
                {
                    if (operatorResponse.LinkIdMember.IsExisting == false)
                    {
                        // Chưa có acc Linkid, thì tạo acc, và tạo connect CONFIRMED
                        var generatedMemCode = "AUTOV2";
                        var guid = Guid.NewGuid();
                        generatedMemCode += guid.ToString().Replace("-", "");
                        generatedMemCode = generatedMemCode.ToUpper();
                        var doneCreated = await CreateLinkIdMember(new CreateLinkIdMemberInput()
                        {
                            Address = input.LoyaltyInfo.Address,
                            StandardMemberCode = generatedMemCode,
                            IdCard = input.LoyaltyInfo.IdCard,
                            PartnerPhoneNumber = input.LoyaltyInfo.Phone,
                            Phone = input.LinkID_PhoneNumber,
                            Name = input.LoyaltyInfo.Name,
                            Email = input.LoyaltyInfo.Email,
                            Gender = input.LoyaltyInfo.Gender,
                            Dob = input.LoyaltyInfo.Dob
                        });
                        var resultMemberId = 0;
                        var walletAddress = "";
                        if (doneCreated is { Items: { } })
                        {
                            resultMemberId = doneCreated.Items.Id;
                            walletAddress = doneCreated.Items.UserAddress;
                        }

                        await PostRewardAsync<object>(RewardApiUrl.SaveRefreshTokenV2, new CreateUnconfirmedConnectionInputSendOperator()
                        {
                            ConnectSource = "AutoConnect2",
                            MemberCode = generatedMemCode,
                            MerchantId = merchantId,
                            ReferenceData = "{}",
                            RefreshToken = "NA",
                            IsChangedLoyalty = true,
                            MemberLoyaltyInfo = new MemberLoyaltyInfoSendOperator()
                            {
                                Address = input.LoyaltyInfo.Address,
                                StandardMemberCode = input.LoyaltyInfo.Cif,
                                IdCard = input.LoyaltyInfo.IdCard,
                                Phone = input.LoyaltyInfo.Phone,
                                Name = input.LoyaltyInfo.Name,
                                Email = input.LoyaltyInfo.Email,
                                Gender = input.LoyaltyInfo.Gender,
                                Dob = input.LoyaltyInfo.Dob,
                                Cif = input.LoyaltyInfo.Cif,
                                MemberLoyaltyCode = input.LoyaltyInfo.Cif,
                                PartnerPhoneNumber = input.LoyaltyInfo.Phone,
                            }
                        });
                        _logger.LogInformation("[" + __reqID +
                                               "] AutoConnectMember2: Done create Confirmed connection in operator (LID member is newly created). LinkID MemberCode: "
                                               + generatedMemCode + "; Cif: " + input.LoyaltyInfo.Cif);

                        // [Gọi sang vpb loyalty]
                        // Tạo connect dạng UnConfirmed ở bảng UserMapping (và UserMappingHistory)
                        await _loyaltyUtilsService.CreateConfirmedConnection(new CreateConfirmedConnectionInput()
                        {
                            LinkIdMemberId = resultMemberId, Cif = input.LoyaltyInfo.Cif, LinkIdWallet = walletAddress,
                            LinkIdPhoneNumber = input.LinkID_PhoneNumber, ConnectSource = 1001
                        });
                        _logger.LogInformation("[" + __reqID +
                                               "]AutoConnectMember2 Done create Confirmed connection in vpbank loy (LID member is newly created). LinkID MemberCode: "
                                               + generatedMemCode + "; Cif: " + input.LoyaltyInfo.Cif);
                        var innerRet = new RewardMemberSendOtpForConnectMerchantOutput()
                        {
                            Result = 200, ExtraInfo = new Dictionary<string, int>()
                        };
                        if (resultMemberId > 0)
                        {
                            innerRet.ExtraInfo.Add("MemberId", resultMemberId);
                        }
                        // Send ack
                        await RewardSendAckAfterConnected(new RewardSendAckAfterConnectedInput()
                        {
                            MemberId = resultMemberId, MerchantId = merchantId, NationalId = generatedMemCode
                        });
                        _logger.LogInformation(" AutoConnectMember2 >> Done sent ack to operator >> " + input.LoyaltyInfo.Cif);
                        // end
                        return innerRet;
                    }
                    else
                    {
                        // Đã có acc LinkID.  Trong trường hợp này chỉ có success khi tk này ko có liên kết nào
                        // với merchant VPB, và đồng thời IDCard của membernafy phải là Null hoặc trùng input's IDcard 
                        if (operatorResponse.LinkIdMember.HasConnection)
                        {
                            // Đã có liên kết. Dù lket với same cif hoặc khác cif đều bắn lỗi
                            if (operatorResponse.LinkIdMember.ConnectedCif == input.LoyaltyInfo.Cif)
                            {
                                CommonHelper.GetErrorValidation("AlreadyConnect", 
                                    "Trên Operator đang có kết nối giữa same cif & Neo Phone ");
                            }
                            else
                            {
                                CommonHelper.GetErrorValidation("NotAllowConnect", 
                                    "Member LinkID tương ứng với NEO phone đang có kết nối với Cif khác");
                            }
                        }
                        else
                        {
                            // Chưa có liên kết
                            if (string.IsNullOrWhiteSpace(operatorResponse.LinkIdMember.MemberIdCard) ||
                                operatorResponse.LinkIdMember.MemberIdCard == input.LoyaltyInfo.IdCard)
                            {
                                // Nếu IdCard của member linkid null hoặc same với input's idcard thì tạo lket confirmed 
                                
                                await CheckHistoryConnect(new CheckConnectHistoryInput()
                                {
                                    CifCode = input.LoyaltyInfo.Cif,
                                    LinkID_PhoneNumber = input.LinkID_PhoneNumber,
                                });
                                await PostRewardAsync<object>(RewardApiUrl.SaveRefreshTokenV2, new CreateUnconfirmedConnectionInputSendOperator()
                                {
                                    ConnectSource = "AutoConnect2",
                                    MemberCode = operatorResponse.LinkIdMember.LinkIdMemberCode,
                                    MerchantId = merchantId,
                                    ReferenceData = "{}",
                                    RefreshToken = "NA",
                                    IsChangedLoyalty = true,
                                    MemberLoyaltyInfo = new MemberLoyaltyInfoSendOperator()
                                    {
                                        Address = input.LoyaltyInfo.Address,
                                        StandardMemberCode = input.LoyaltyInfo.Cif,
                                        IdCard = input.LoyaltyInfo.IdCard,
                                        Phone = input.LoyaltyInfo.Phone,
                                        Name = input.LoyaltyInfo.Name,
                                        Email = input.LoyaltyInfo.Email,
                                        Gender = input.LoyaltyInfo.Gender,
                                        Dob = input.LoyaltyInfo.Dob,
                                        Cif = input.LoyaltyInfo.Cif,
                                        MemberLoyaltyCode = input.LoyaltyInfo.Cif,
                                        PartnerPhoneNumber = input.LoyaltyInfo.Phone,
                                    }
                                });
                                _logger.LogInformation("[" + __reqID +
                                                       "] AutoConnectMember2: Done create Confirmed connection in operator (existing LID member). LinkID MemberCode: "
                                                       + operatorResponse.LinkIdMember.LinkIdMemberCode + "; Cif: " + input.LoyaltyInfo.Cif);

                                // [Gọi sang vpb loyalty]
                                // Tạo connect dạng UnConfirmed ở bảng UserMapping (và UserMappingHistory)
                                await _loyaltyUtilsService.CreateConfirmedConnection(new CreateConfirmedConnectionInput()
                                {
                                    LinkIdMemberId = operatorResponse.LinkIdMember.LinkIdMemberId, Cif = input.LoyaltyInfo.Cif,
                                    LinkIdWallet = operatorResponse.LinkIdMember.LinkIdMemberWallet,
                                    LinkIdPhoneNumber = input.LinkID_PhoneNumber, ConnectSource = 1001
                                });
                                _logger.LogInformation("[" + __reqID +
                                                       "]AutoConnectMember2 Done create Confirmed connection in vpbank loy (LID member is newly created). LinkID MemberCode: "
                                                       + operatorResponse.LinkIdMember.LinkIdMemberCode + "; Cif: " + input.LoyaltyInfo.Cif);
                                var innerRet = new RewardMemberSendOtpForConnectMerchantOutput()
                                {
                                    Result = 200, ExtraInfo = new Dictionary<string, int>()
                                };
                                innerRet.ExtraInfo.Add("MemberId", operatorResponse.LinkIdMember.LinkIdMemberId);
                                // Send ack
                                await RewardSendAckAfterConnected(new RewardSendAckAfterConnectedInput()
                                {
                                    MemberId = operatorResponse.LinkIdMember.LinkIdMemberId, MerchantId = merchantId,
                                    NationalId = operatorResponse.LinkIdMember.LinkIdMemberCode
                                });
                                _logger.LogInformation(" AutoConnectMember2 (2) >> Done sent ack to operator >> " + input.LoyaltyInfo.Cif);
                                return innerRet;
                            }
                            else
                            {
                                CommonHelper.GetErrorValidation("LinkIdHasDiffIdcard",
                                    "LinkID Member đã gắn với IDCard khác NEO IDcard");
                            }
                        }
                    }
                }
            }
            
            // CASE 2: Cif đã từng kết nối với LinkID Acc nào đó. Case này, chỉ khi NEO Phone same as linkidphone trong history
            // thì mới đi tiếp
            if (checkListHis != null && checkListHis.LinkIdConnectionStatus == 0 && checkListHis.ConnectionHistory != null
                && checkListHis.ConnectionHistory.Count > 0) 
            {
                var historyPhone = checkListHis.ConnectionHistory[0].LinkIDPhoneNumber;
                if (historyPhone == input.LinkID_PhoneNumber)
                {
                    // Lịch sử liên kết vs sđt Neo rồi nên cần make sure là sđt neo có acc LinkID
                    RewardMemberGetInfoOutput linkidMemInfo = null;
                    try
                    {
                        linkidMemInfo = await GetInfo(new RewardMemberGetInfoInput()
                        {
                            PhoneNumber = input.LinkID_PhoneNumber,
                        });
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(
                            "[" + __reqID + "] >> AutoConnectV2 Check member existence, got error: "
                            + input.LinkID_PhoneNumber + " - " +
                            e.Message + " - " + e.StackTrace);
                        // Nếu mã lỗi từ operator bắn về là MemberNotExistsOrNotActive thì sẽ đi tiếp
                        if (e.GetType() == typeof(RewardException))
                        {
                            var res = await _exceptionReponseService.GetExceptionRewardReponse(e);
                            if (res.Code == "MemberNotExitsOrNotActive")
                            {
                                CommonHelper.GetErrorValidation("MemberNotExitsOrNotActive", "LinkID Member từng tồn tại nhưng giờ không tồn tại");
                            }
                            else
                            {
                                CommonHelper.GetErrorValidation("ErrorGetLinkIDMemInfo", "Lỗi khi lấy thông tin LinkID Member");
                            }
                        }
                    }
                    // OK keets nois
                    await PostRewardAsync<object>(RewardApiUrl.SaveRefreshTokenV2, new CreateUnconfirmedConnectionInputSendOperator()
                    {
                        ConnectSource = "AutoConnect2",
                        MemberCode = linkidMemInfo.NationalId,
                        MerchantId = merchantId,
                        ReferenceData = "{}",
                        RefreshToken = "NA",
                        IsChangedLoyalty = true,
                        MemberLoyaltyInfo = new MemberLoyaltyInfoSendOperator()
                        {
                            Address = input.LoyaltyInfo.Address,
                            StandardMemberCode = input.LoyaltyInfo.Cif,
                            IdCard = input.LoyaltyInfo.IdCard,
                            Phone = input.LoyaltyInfo.Phone,
                            Name = input.LoyaltyInfo.Name,
                            Email = input.LoyaltyInfo.Email,
                            Gender = input.LoyaltyInfo.Gender,
                            Dob = input.LoyaltyInfo.Dob,
                            Cif = input.LoyaltyInfo.Cif,
                            MemberLoyaltyCode = input.LoyaltyInfo.Cif,
                            PartnerPhoneNumber = input.LoyaltyInfo.Phone,
                        }
                    });
                    _logger.LogInformation("[" + __reqID +
                                           "] AutoConnectMember2: Done create Confirmed connection in operator (existing LID member). LinkID MemberCode: "
                                           + linkidMemInfo.NationalId + "; Cif: " + input.LoyaltyInfo.Cif);

                    // [Gọi sang vpb loyalty]
                    // Tạo connect dạng UnConfirmed ở bảng UserMapping (và UserMappingHistory)
                    await _loyaltyUtilsService.CreateConfirmedConnection(new CreateConfirmedConnectionInput()
                    {
                        LinkIdMemberId = linkidMemInfo.Id, Cif = input.LoyaltyInfo.Cif,
                        LinkIdWallet = linkidMemInfo.UserAddress,
                        LinkIdPhoneNumber = input.LinkID_PhoneNumber, ConnectSource = 1001
                    });
                    _logger.LogInformation("[" + __reqID +
                                           "]AutoConnectMember2 Done create Confirmed connection in vpbank loy (LID member is newly created). LinkID MemberCode: "
                                           + linkidMemInfo.NationalId + "; Cif: " + input.LoyaltyInfo.Cif);
                    var innerRet = new RewardMemberSendOtpForConnectMerchantOutput()
                    {
                        Result = 200, ExtraInfo = new Dictionary<string, int>()
                    };
                    innerRet.ExtraInfo.Add("MemberId", linkidMemInfo.Id);
                    return innerRet;
                }
                else
                {
                    CommonHelper.GetErrorValidation("InvalidPhoneHistory",
                        "Cif từng liên kết với SĐT khác. Không cho phép đi tiếp.");
                }
            }

            if (checkListHis != null && checkListHis.LinkIdConnectionStatus == 2)
            {
                var operatorResponse = await PostRewardAsync<CheckExistenceAndConnectionWithMerchantOutput>(RewardApiUrl.CheckExistenceAndConnectionWithMerchant,
                    new CheckExistenceAndConnectionWithMerchantInput()
                    {
                        MerchantId = merchantId, IdCard = input.LoyaltyInfo.IdCard,
                        LinkIdPhoneNumber = checkListHis.CurrentLinkIdPhoneNumber, // Get info of số đt đang liên kêt unconfirmed với CIF
                    });
                if (operatorResponse == null || operatorResponse.LinkIdMember == null ||
                    operatorResponse.LinkIdMember.IsExisting == false)
                {
                    _logger.LogInformation(" >> AutoConnect2, Status = 2,  but related LinkIDPhone not exist in LinkID >> " + checkListHis.CurrentLinkIdPhoneNumber);
                    CommonHelper.GetErrorValidation("UnconfirmedPhoneNumberInvalidInLinkID", 
                        "Cif has unconfirmed connection with a phone number that not exist in LinkID");
                }
                else
                {
                    if (checkListHis.CurrentLinkIdPhoneNumber != input.LinkID_PhoneNumber || checkListHis.ConnectionHistory?.Count == 0
                        || operatorResponse.LinkIdMember.MemberIdCard != input.LoyaltyInfo.IdCard)
                    {
                        _logger.LogInformation(" >> AutoConnect2, Status = 2,  LinkID Phone not match, or IDCard Not match >> " + checkListHis.CurrentLinkIdPhoneNumber);
                        CommonHelper.GetErrorValidation("UnconfirmedInfoNotCorrect", 
                            "Cif has unconfirmed connection with different phone number to input phone number and/or IdCard also not the same");
                    }
                    else
                    {
                        // Tự động confirm kết nối trên operator & loyalty
                        _logger.LogInformation(" >> AutoConnect2 - Unconfirmed Connection - Confirm now ..." + input.LinkID_PhoneNumber);
                        var requestReward = new RewardMemberConfirmConnectDto()
                        {
                            MerchantId = merchantId,
                            Action = "Confirmed",
                            PhoneNumber = input.LinkID_PhoneNumber,
                            ConnectSource = "AutoConnect2"
                        };
                        var rewardConfirm = await PostRewardAsync<RewardMemberConfirmConnectOutputDto>(RewardApiUrl.MEMBER_CONFIRM_CONNECT, requestReward, MerchantNameConfig.VPBank);
                        var linkIdMemberId = rewardConfirm.Item.MemberId;
                        _logger.LogInformation("Reward confirm result: " + JsonConvert.SerializeObject(rewardConfirm));
                        // ConnectSource: { 1: LinkID, 2: VPO, 247: Portal247 }
                        var connectSource = 1001;
                        await RetryConfirmConnect(linkIdMemberId, connectSource);
                        var innerRet = new RewardMemberSendOtpForConnectMerchantOutput()
                        {
                            Result = 200, ExtraInfo = new Dictionary<string, int>()
                        };
                        innerRet.ExtraInfo.Add("MemberId", checkListHis.ConnectionHistory[0].LinkIDMemberId);
                        return innerRet;
                    }
                }
            }
            throw new Exception("Invalid data. End of flow! It must be one of cases above");
        }
        /**
         * API kết nối cho phần tích hợp MasterCard, do Adapter gọi sang.
         * Khác với AutoConnectMember2, thì api này nhận thêm PREFIX (để adapter có thể truyền lên), và trả về LinkID Member Code
         */
        public async Task<RewardMemberAutoConnectMember3Output> AutoConnectMember3MasterCard(
            RewardMemberAutoConnectMember3Input input)
        {
            // Validation
            if (input.LoyaltyInfo == null)
            {
                CommonHelper.GetErrorValidation("0", "Invalid data request");
            }

            if (string.IsNullOrWhiteSpace(input.LinkID_PhoneNumber))
            {
                CommonHelper.GetErrorValidation("LinkIDPhoneRequired", "LinkID Phone Number is required");
            }

            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.IdCard))
            {
                CommonHelper.GetErrorValidation("IDCardRequired", "ID Card is required");
            }

            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.Phone))
            {
                CommonHelper.GetErrorValidation("VPBPhoneRequired", "VPBank PhoneNumber is required");
            }

            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.Cif))
            {
                CommonHelper.GetErrorValidation("CifCodeRequierd", "Cif code is required");
            }

            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.MemberLoyaltyCode))
            {
                CommonHelper.GetErrorValidation("LoyaltyMemberCodeRequired", "Loyalty member code is required");
            }
            

            var __reqID = DateTime.UtcNow.Millisecond;
            _logger.LogInformation("[" + __reqID + "] >> Handle AutoConnectMember3MasterCard for this payload: " +
                                   JsonConvert.SerializeObject(input));
            // Make sure rằng Cif đang chưa có UserMapping.Status = 0
            var checkListHis = await _loyaltyUtilsService.GetMemberInfoByCif(new GetMemberInfoByCifInput()
            {
                Cif = input.LoyaltyInfo.Cif,
            });
            if (checkListHis != null && checkListHis.UserInfo != null &&
                checkListHis.UserInfo.IdCard != input.LoyaltyInfo.IdCard)
            {
                _logger.LogInformation("[" + __reqID + "] >> Handle AutoConnectMember3MasterCard  - Input IDCard not same as IdCard in DB of the CIF - input = " +
                                       input.LoyaltyInfo.IdCard + " - Found In DB = " + checkListHis.UserInfo.IdCard);
                CommonHelper.GetErrorValidation("InputIdCardNotSameAsLoyalty", 
                    "Input's IDCard not same as IDCard of the CIF in DB");
            }

            if (checkListHis != null && checkListHis.IsSuccess
                                     && checkListHis.LinkIdConnectionStatus != 0
                                     && checkListHis.LinkIdConnectionStatus != 2)
            {
                _logger.LogInformation("[" + __reqID + "] >> Handle AutoConnectMember3MasterCard for this payload: " +
                                       JsonConvert.SerializeObject(input) + " -  CIF has usermapping.Status != 0 and 2 ");
                CommonHelper.GetErrorValidation("InvalidConnectionStatus", 
                    "AutoConnectMember3MasterCard Only Accept UserMapping.Status = 0 Or 2");
            }
            // Truyền thông tin LinkID Phone, Cif, IdCard lên operator
            var merchantId = Convert.ToInt32(_configuration.GetSection("RewardVPBank:MerchantId").Value);
            // CASE 1: Cif mới nguyên, chưa có lịch sử kết nối ở onprem
            if (checkListHis != null && checkListHis.LinkIdConnectionStatus == 0 && (
                    checkListHis.ConnectionHistory == null || checkListHis.ConnectionHistory.Count == 0)) 
            {
                var operatorResponse = await PostRewardAsync<CheckExistenceAndConnectionWithMerchantOutput>(RewardApiUrl.CheckExistenceAndConnectionWithMerchant,
                    new CheckExistenceAndConnectionWithMerchantInput()
                    {
                        MerchantId = merchantId, IdCard = input.LoyaltyInfo.IdCard,
                        LinkIdPhoneNumber = input.LinkID_PhoneNumber,
                    });
                _logger.LogInformation("[" + __reqID + "] >> Handle AutoConnectMember3MasterCard - operatorResponse =  " +
                                       JsonConvert.SerializeObject(operatorResponse));
                if (operatorResponse != null && operatorResponse.RsCheckIdCard != null)
                {
                    if (operatorResponse.RsCheckIdCard.IsExist &&
                        operatorResponse.RsCheckIdCard.CorrespondingMemberPhone != input.LoyaltyInfo.Phone)
                    {
                        // Step 1: Nếu IdCard của NEO mà đang gắn với 1 TK linkid khác, và sđt đó != NEO phone:
                        // Thì BÁO LỖI
                        CommonHelper.GetErrorValidation("IdCardNotAvailable", 
                            "Input's IdCard already belong to another LinkID Member");
                    }
                }

                if (operatorResponse != null && operatorResponse.LinkIdMember != null)
                {
                    if (operatorResponse.LinkIdMember.IsExisting == false)
                    {
                        // Chưa có acc Linkid, thì tạo acc, và tạo connect CONFIRMED
                        var generatedMemCode = CommonHelper.GetPrefixCreateMemberCode(input.Prefix, "AUTOV3MC");
                        var guid = Guid.NewGuid();
                        generatedMemCode += guid.ToString().Replace("-", "");
                        generatedMemCode = generatedMemCode.ToUpper();
                        var doneCreated = await CreateLinkIdMember(new CreateLinkIdMemberInput()
                        {
                            Address = input.LoyaltyInfo.Address,
                            StandardMemberCode = generatedMemCode,
                            IdCard = input.LoyaltyInfo.IdCard,
                            PartnerPhoneNumber = input.LoyaltyInfo.Phone,
                            Phone = input.LinkID_PhoneNumber,
                            Name = input.LoyaltyInfo.Name,
                            Email = input.LoyaltyInfo.Email,
                            Gender = input.LoyaltyInfo.Gender,
                            Dob = input.LoyaltyInfo.Dob
                        });
                        var resultMemberId = 0;
                        var walletAddress = "";
                        if (doneCreated is { Items: { } })
                        {
                            resultMemberId = doneCreated.Items.Id;
                            walletAddress = doneCreated.Items.UserAddress;
                        }

                        await PostRewardAsync<object>(RewardApiUrl.SaveRefreshTokenV2, new CreateUnconfirmedConnectionInputSendOperator()
                        {
                            ConnectSource = "AutoConnect3",
                            MemberCode = generatedMemCode,
                            MerchantId = merchantId,
                            ReferenceData = "{}",
                            RefreshToken = "NA",
                            IsChangedLoyalty = true,
                            MemberLoyaltyInfo = new MemberLoyaltyInfoSendOperator()
                            {
                                Address = input.LoyaltyInfo.Address,
                                StandardMemberCode = input.LoyaltyInfo.Cif,
                                IdCard = input.LoyaltyInfo.IdCard,
                                Phone = input.LoyaltyInfo.Phone,
                                Name = input.LoyaltyInfo.Name,
                                Email = input.LoyaltyInfo.Email,
                                Gender = input.LoyaltyInfo.Gender,
                                Dob = input.LoyaltyInfo.Dob,
                                Cif = input.LoyaltyInfo.Cif,
                                MemberLoyaltyCode = input.LoyaltyInfo.Cif,
                                PartnerPhoneNumber = input.LoyaltyInfo.Phone,
                            }
                        });
                        _logger.LogInformation("[" + __reqID +
                                               "] AutoConnectMember3MasterCard: Done create Confirmed connection in operator (LID member is newly created). LinkID MemberCode: "
                                               + generatedMemCode + "; Cif: " + input.LoyaltyInfo.Cif);

                        // [Gọi sang vpb loyalty]
                        // Tạo connect dạng UnConfirmed ở bảng UserMapping (và UserMappingHistory)
                        await _loyaltyUtilsService.CreateConfirmedConnection(new CreateConfirmedConnectionInput()
                        {
                            LinkIdMemberId = resultMemberId, Cif = input.LoyaltyInfo.Cif, LinkIdWallet = walletAddress,
                            LinkIdPhoneNumber = input.LinkID_PhoneNumber, ConnectSource = 1002
                        });
                        _logger.LogInformation("[" + __reqID +
                                               "]AutoConnectMember3MasterCard Done create Confirmed connection in vpbank loy (LID member is newly created). LinkID MemberCode: "
                                               + generatedMemCode + "; Cif: " + input.LoyaltyInfo.Cif);
                        var innerRet = new RewardMemberAutoConnectMember3Output()
                        {
                            Result = 200, ExtraInfo = new Dictionary<string, int>(), LinkiDMemberCode = generatedMemCode
                        };
                        if (resultMemberId > 0)
                        {
                            innerRet.ExtraInfo.Add("MemberId", resultMemberId);
                        }
                        // Send ack
                        await RewardSendAckAfterConnected(new RewardSendAckAfterConnectedInput()
                        {
                            MemberId = resultMemberId, MerchantId = merchantId, NationalId = generatedMemCode
                        });
                        _logger.LogInformation(" AutoConnectMember3MasterCard >> Done sent ack to operator >> " + input.LoyaltyInfo.Cif);
                        // end
                        return innerRet;
                    }
                    else
                    {
                        // Đã có acc LinkID.  Trong trường hợp này chỉ có success khi tk này ko có liên kết nào
                        // với merchant VPB, và đồng thời IDCard của membernafy phải là Null hoặc trùng input's IDcard 
                        if (operatorResponse.LinkIdMember.HasConnection)
                        {
                            // Đã có liên kết. Dù lket với same cif hoặc khác cif đều bắn lỗi
                            if (operatorResponse.LinkIdMember.ConnectedCif == input.LoyaltyInfo.Cif)
                            {
                                CommonHelper.GetErrorValidation("AlreadyConnect", 
                                    "Trên Operator đang có kết nối giữa same cif & Neo Phone ");
                            }
                            else
                            {
                                CommonHelper.GetErrorValidation("NotAllowConnect", 
                                    "Member LinkID tương ứng với NEO phone đang có kết nối với Cif khác");
                            }
                        }
                        else
                        {
                            // Chưa có liên kết
                            if (string.IsNullOrWhiteSpace(operatorResponse.LinkIdMember.MemberIdCard) ||
                                operatorResponse.LinkIdMember.MemberIdCard == input.LoyaltyInfo.IdCard)
                            {
                                // Nếu IdCard của member linkid null hoặc same với input's idcard thì tạo lket confirmed 
                                
                                await CheckHistoryConnect(new CheckConnectHistoryInput()
                                {
                                    CifCode = input.LoyaltyInfo.Cif,
                                    LinkID_PhoneNumber = input.LinkID_PhoneNumber,
                                });
                                await PostRewardAsync<object>(RewardApiUrl.SaveRefreshTokenV2, new CreateUnconfirmedConnectionInputSendOperator()
                                {
                                    ConnectSource = "AutoConnect3",
                                    MemberCode = operatorResponse.LinkIdMember.LinkIdMemberCode,
                                    MerchantId = merchantId,
                                    ReferenceData = "{}",
                                    RefreshToken = "NA",
                                    IsChangedLoyalty = true,
                                    MemberLoyaltyInfo = new MemberLoyaltyInfoSendOperator()
                                    {
                                        Address = input.LoyaltyInfo.Address,
                                        StandardMemberCode = input.LoyaltyInfo.Cif,
                                        IdCard = input.LoyaltyInfo.IdCard,
                                        Phone = input.LoyaltyInfo.Phone,
                                        Name = input.LoyaltyInfo.Name,
                                        Email = input.LoyaltyInfo.Email,
                                        Gender = input.LoyaltyInfo.Gender,
                                        Dob = input.LoyaltyInfo.Dob,
                                        Cif = input.LoyaltyInfo.Cif,
                                        MemberLoyaltyCode = input.LoyaltyInfo.Cif,
                                        PartnerPhoneNumber = input.LoyaltyInfo.Phone,
                                    }
                                });
                                _logger.LogInformation("[" + __reqID +
                                                       "] AutoConnectMember3MasterCard: Done create Confirmed connection in operator (existing LID member). LinkID MemberCode: "
                                                       + operatorResponse.LinkIdMember.LinkIdMemberCode + "; Cif: " + input.LoyaltyInfo.Cif);

                                // [Gọi sang vpb loyalty]
                                // Tạo connect dạng UnConfirmed ở bảng UserMapping (và UserMappingHistory)
                                await _loyaltyUtilsService.CreateConfirmedConnection(new CreateConfirmedConnectionInput()
                                {
                                    LinkIdMemberId = operatorResponse.LinkIdMember.LinkIdMemberId, Cif = input.LoyaltyInfo.Cif,
                                    LinkIdWallet = operatorResponse.LinkIdMember.LinkIdMemberWallet,
                                    LinkIdPhoneNumber = input.LinkID_PhoneNumber, ConnectSource = 1002
                                });
                                _logger.LogInformation("[" + __reqID +
                                                       "]AutoConnectMember3MasterCard Done create Confirmed connection in vpbank loy (LID member is newly created). LinkID MemberCode: "
                                                       + operatorResponse.LinkIdMember.LinkIdMemberCode + "; Cif: " + input.LoyaltyInfo.Cif);
                                var innerRet = new RewardMemberAutoConnectMember3Output()
                                {
                                    Result = 200, ExtraInfo = new Dictionary<string, int>(), LinkiDMemberCode = operatorResponse.LinkIdMember.LinkIdMemberCode
                                };
                                innerRet.ExtraInfo.Add("MemberId", operatorResponse.LinkIdMember.LinkIdMemberId);
                                // Send ack
                                await RewardSendAckAfterConnected(new RewardSendAckAfterConnectedInput()
                                {
                                    MemberId = operatorResponse.LinkIdMember.LinkIdMemberId, MerchantId = merchantId,
                                    NationalId = operatorResponse.LinkIdMember.LinkIdMemberCode
                                });
                                _logger.LogInformation(" AutoConnectMember3MasterCard (2) >> Done sent ack to operator >> " + input.LoyaltyInfo.Cif);
                                return innerRet;
                            }
                            else
                            {
                                CommonHelper.GetErrorValidation("LinkIdHasDiffIdcard",
                                    "LinkID Member đã gắn với IDCard khác NEO IDcard");
                            }
                        }
                    }
                }
            }
            
            // CASE 2: Cif đã từng kết nối với LinkID Acc nào đó. Case này, chỉ khi NEO Phone same as linkidphone trong history
            // thì mới đi tiếp
            if (checkListHis != null && checkListHis.LinkIdConnectionStatus == 0 && checkListHis.ConnectionHistory != null
                && checkListHis.ConnectionHistory.Count > 0) 
            {
                var historyPhone = checkListHis.ConnectionHistory[0].LinkIDPhoneNumber;
                if (historyPhone == input.LinkID_PhoneNumber)
                {
                    // Lịch sử liên kết vs sđt Neo rồi nên cần make sure là sđt neo có acc LinkID
                    RewardMemberGetInfoOutput linkidMemInfo = null;
                    try
                    {
                        linkidMemInfo = await GetInfo(new RewardMemberGetInfoInput()
                        {
                            PhoneNumber = input.LinkID_PhoneNumber,
                        });
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(
                            "[" + __reqID + "] >> AutoConnectV3MasterCard Check member existence, got error: "
                            + input.LinkID_PhoneNumber + " - " +
                            e.Message + " - " + e.StackTrace);
                        // Nếu mã lỗi từ operator bắn về là MemberNotExistsOrNotActive thì sẽ đi tiếp
                        if (e.GetType() == typeof(RewardException))
                        {
                            var res = await _exceptionReponseService.GetExceptionRewardReponse(e);
                            if (res.Code == "MemberNotExitsOrNotActive")
                            {
                                CommonHelper.GetErrorValidation("MemberNotExitsOrNotActive", "LinkID Member từng tồn tại nhưng giờ không tồn tại");
                            }
                            else
                            {
                                CommonHelper.GetErrorValidation("ErrorGetLinkIDMemInfo", "Lỗi khi lấy thông tin LinkID Member");
                            }
                        }
                    }
                    // OK keets nois
                    await PostRewardAsync<object>(RewardApiUrl.SaveRefreshTokenV2, new CreateUnconfirmedConnectionInputSendOperator()
                    {
                        ConnectSource = "AutoConnect3",
                        MemberCode = linkidMemInfo.NationalId,
                        MerchantId = merchantId,
                        ReferenceData = "{}",
                        RefreshToken = "NA",
                        IsChangedLoyalty = true,
                        MemberLoyaltyInfo = new MemberLoyaltyInfoSendOperator()
                        {
                            Address = input.LoyaltyInfo.Address,
                            StandardMemberCode = input.LoyaltyInfo.Cif,
                            IdCard = input.LoyaltyInfo.IdCard,
                            Phone = input.LoyaltyInfo.Phone,
                            Name = input.LoyaltyInfo.Name,
                            Email = input.LoyaltyInfo.Email,
                            Gender = input.LoyaltyInfo.Gender,
                            Dob = input.LoyaltyInfo.Dob,
                            Cif = input.LoyaltyInfo.Cif,
                            MemberLoyaltyCode = input.LoyaltyInfo.Cif,
                            PartnerPhoneNumber = input.LoyaltyInfo.Phone,
                        }
                    });
                    _logger.LogInformation("[" + __reqID +
                                           "] AutoConnectMember3MasterCard: Done create Confirmed connection in operator (existing LID member). LinkID MemberCode: "
                                           + linkidMemInfo.NationalId + "; Cif: " + input.LoyaltyInfo.Cif);

                    // [Gọi sang vpb loyalty]
                    // Tạo connect dạng UnConfirmed ở bảng UserMapping (và UserMappingHistory)
                    await _loyaltyUtilsService.CreateConfirmedConnection(new CreateConfirmedConnectionInput()
                    {
                        LinkIdMemberId = linkidMemInfo.Id, Cif = input.LoyaltyInfo.Cif,
                        LinkIdWallet = linkidMemInfo.UserAddress,
                        LinkIdPhoneNumber = input.LinkID_PhoneNumber, ConnectSource = 1002
                    });
                    _logger.LogInformation("[" + __reqID +
                                           "]AutoConnectMember3MasterCard Done create Confirmed connection in vpbank loy (LID member is newly created). LinkID MemberCode: "
                                           + linkidMemInfo.NationalId + "; Cif: " + input.LoyaltyInfo.Cif);
                    var innerRet = new RewardMemberAutoConnectMember3Output()
                    {
                        Result = 200, ExtraInfo = new Dictionary<string, int>(), LinkiDMemberCode = linkidMemInfo.NationalId,
                    };
                    innerRet.ExtraInfo.Add("MemberId", linkidMemInfo.Id);
                    return innerRet;
                }
                else
                {
                    CommonHelper.GetErrorValidation("InvalidPhoneHistory",
                        "Cif từng liên kết với SĐT khác. Không cho phép đi tiếp.");
                }
            }

            if (checkListHis != null && checkListHis.LinkIdConnectionStatus == 2)
            {
                var operatorResponse = await PostRewardAsync<CheckExistenceAndConnectionWithMerchantOutput>(RewardApiUrl.CheckExistenceAndConnectionWithMerchant,
                    new CheckExistenceAndConnectionWithMerchantInput()
                    {
                        MerchantId = merchantId, IdCard = input.LoyaltyInfo.IdCard,
                        LinkIdPhoneNumber = checkListHis.CurrentLinkIdPhoneNumber, // Get info of số đt đang liên kêt unconfirmed với CIF
                    });
                if (operatorResponse == null || operatorResponse.LinkIdMember == null ||
                    operatorResponse.LinkIdMember.IsExisting == false)
                {
                    _logger.LogInformation(" >> AutoConnect3MasterCard, Status = 2,  but related LinkIDPhone not exist in LinkID >> " + checkListHis.CurrentLinkIdPhoneNumber);
                    CommonHelper.GetErrorValidation("UnconfirmedPhoneNumberInvalidInLinkID", 
                        "Cif has unconfirmed connection with a phone number that not exist in LinkID");
                }
                else
                {
                    if (checkListHis.CurrentLinkIdPhoneNumber != input.LinkID_PhoneNumber || checkListHis.ConnectionHistory?.Count == 0
                        || operatorResponse.LinkIdMember.MemberIdCard != input.LoyaltyInfo.IdCard)
                    {
                        _logger.LogInformation(" >> AutoConnect3MasterCard, Status = 2,  LinkID Phone not match, or IDCard Not match >> " + checkListHis.CurrentLinkIdPhoneNumber);
                        CommonHelper.GetErrorValidation("UnconfirmedInfoNotCorrect", 
                            "Cif has unconfirmed connection with different phone number to input phone number and/or IdCard also not the same");
                    }
                    else
                    {
                        // Tự động confirm kết nối trên operator & loyalty
                        _logger.LogInformation(" >> AutoConnect3MasterCard - Unconfirmed Connection - Confirm now ..." + input.LinkID_PhoneNumber);
                        var requestReward = new RewardMemberConfirmConnectDto()
                        {
                            MerchantId = merchantId,
                            Action = "Confirmed",
                            PhoneNumber = input.LinkID_PhoneNumber,
                            ConnectSource = "AutoConnect3"
                        };
                        var rewardConfirm = await PostRewardAsync<RewardMemberConfirmConnectOutputDto>(RewardApiUrl.MEMBER_CONFIRM_CONNECT, requestReward, MerchantNameConfig.VPBank);
                        var linkIdMemberId = rewardConfirm.Item.MemberId;
                        _logger.LogInformation(">> AutoConnect3MasterCard >> Reward confirm result: " + JsonConvert.SerializeObject(rewardConfirm));
                        // ConnectSource: { 1: LinkID, 2: VPO, 247: Portal247 }
                        var connectSource = 1002;
                        await RetryConfirmConnect(linkIdMemberId, connectSource);
                        // get membercode from wallet
                        var innerRet = new RewardMemberAutoConnectMember3Output()
                        {
                            Result = 200, ExtraInfo = new Dictionary<string, int>(), LinkiDMemberCode = rewardConfirm.Item.MemberCode
                        };
                        innerRet.ExtraInfo.Add("MemberId", checkListHis.ConnectionHistory[0].LinkIDMemberId);
                        return innerRet;
                    }
                }
            }
            throw new Exception("Invalid data. End of flow! It must be one of cases above");
        }
        public async Task<RewardMemberSendOtpForConnectMerchantOutput> SendOtpForConnectMerchant(RewardMemberSendOtpForConnectMerchantInput input)
        {
            // Validation
            if (input.LoyaltyInfo == null)
            {
                CommonHelper.GetErrorValidation("0", "Invalid data request");
            }
            if (string.IsNullOrWhiteSpace(input.SessionId))
            {
                CommonHelper.GetErrorValidation("SessionIdRequired", "SessionId is required");
            }
            if (string.IsNullOrWhiteSpace(input.LinkID_PhoneNumber))
            {
                CommonHelper.GetErrorValidation("LinkIDPhoneRequired", "LinkID Phone Number is required");
            }
            if (!input.RequiredOtp.HasValue)
            {
                CommonHelper.GetErrorValidation("RequiredOTPRequired", "RequiredOTP field is required");
            }
            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.IdCard))
            {
                CommonHelper.GetErrorValidation("IDCardRequired", "ID Card is required");
            }
            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.Phone))
            {
                CommonHelper.GetErrorValidation("VPBPhoneRequired", "VPBank PhoneNumber is required");
            }
            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.Cif))
            {
                CommonHelper.GetErrorValidation("CifCodeRequierd", "Cif code is required");
            }
            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.MemberLoyaltyCode))
            {
                CommonHelper.GetErrorValidation("LoyaltyMemberCodeRequired", "Loyalty member code is required");
            }
            if (!input.LoyaltyInfo.Dob.HasValue)
            {
                CommonHelper.GetErrorValidation("BirthdayRequired", "Date of birth is required");
            }
            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.Phone))
            {
                CommonHelper.GetErrorValidation("PhoneRequired", "Phone is required");
            }
            if (string.IsNullOrWhiteSpace(input.LoyaltyInfo.Segment))
            {
                CommonHelper.GetErrorValidation("SegmentRequired", "Segment is required");
            }
            if (!string.IsNullOrWhiteSpace(input.LoyaltyInfo.Gender))
            {
                if (!(new List<string> { "M", "F", "O" }).Contains(input.LoyaltyInfo.Gender))
                {
                    CommonHelper.GetErrorValidation("202", "Gender must be one of the following values: M,F,O");
                }
            }
            // check connect history first
            await CheckHistoryConnect(new CheckConnectHistoryInput()
            {
                CifCode = input.LoyaltyInfo.Cif,
                LinkID_PhoneNumber = input.LinkID_PhoneNumber,
            });
            // End validation

            // [VL-373] https://linkid.atlassian.net/browse/VL-373 Ở OCB có yêu cầu mới nếu sessionId có prefix VERIFY
            // + Trường hợp có tiền tố cố định VERIFY, phía LynkID sẽ thực hiện verify only(ko gửi OTP, ko tạo account…)
            // + Trường hợp tiền tố không có VERIFY thì API Init xử lý như hiện tại: gửi OTP, tạo yêu cầu liên kết
            var isValidation = false;
            var prefixVerify = @"VERIFY";
            var resultMemberId = 0;
            if (input.SessionId.StartsWith(prefixVerify, StringComparison.OrdinalIgnoreCase))
            {
                isValidation = true;
            };
            _logger.LogInformation("Send otp connect merchant request: " + JsonConvert.SerializeObject(input));
            var merchantId = Convert.ToInt32(_configuration.GetSection("RewardVPBank:MerchantId").Value);
            var loyaltyInfo = input.LoyaltyInfo;
            // Create request for send otp to LinkID
            var requestRequest = new RewardMemberSendOtpForConnectMerchantInputDto()
            {
                RequiredOtp = input.RequiredOtp.Value,
                IdCard = input.LoyaltyInfo.IdCard,
                Cif = input.LoyaltyInfo.Cif,
                LoyaltyInfo = new RewardMemberSendOtpLoyaltyInfoDto()
                {
                    Address = loyaltyInfo.Address,
                    Avatar = "",
                    ChannelType = "",
                    Cif = loyaltyInfo.Cif,
                    Dob = loyaltyInfo.Dob,
                    Email = loyaltyInfo.Email,
                    FullChannelTypeCode = "",
                    FullMemberTypeCode = "Customer",
                    FullRegionCode = "VN",
                    Gender = !(new List<string> { "M", "F", "O" }).Contains(loyaltyInfo.Gender) ? "O" : loyaltyInfo.Gender,
                    IdCard = loyaltyInfo.IdCard,
                    MemberLoyaltyCode = loyaltyInfo.MemberLoyaltyCode,
                    MemberTypeCode = "Customer",
                    Name = loyaltyInfo.Name,
                    PartnerPhoneNumber = loyaltyInfo.Phone,
                    Phone = loyaltyInfo.Phone,
                    RankTypeCode = "Customer",
                    RegionCode = "VN",
                    Segment = loyaltyInfo.Segment,
                    StandardMemberCode = loyaltyInfo.Cif,
                    Type = "Customer",
                    VipType = loyaltyInfo.VipType,
                },
                MerchantId = merchantId,
                PartnerPhoneNumber = loyaltyInfo.Phone,
                PhoneNumber = input.LinkID_PhoneNumber,
                SessionId = input.SessionId,
                IsValidation = isValidation,
                ConnectSource = "VPBank"
            };
            // [VL-373] https://linkid.atlassian.net/browse/VL-373 Ở OCB có yêu cầu mới nếu sessionId có prefix VERIFY
            // + Trường hợp có tiền tố cố định VERIFY, phía LynkID sẽ thực hiện verify only(ko gửi OTP, ko tạo account…)
            // + Trường hợp tiền tố không có VERIFY thì API Init xử lý như hiện tại: gửi OTP, tạo yêu cầu liên kết
            // DO đã check history ở VPBANK phía trên nên khi có isValidation thì trả ra response luôn không cần call sang VPbank
            if (isValidation)
            {
                await PostRewardAsync<RewardMemberSendOtpForConnectMerchantDto>(RewardApiUrl.MEMBER_SEND_OTP_CONNECT, requestRequest, MerchantNameConfig.VPBank);
                return new RewardMemberSendOtpForConnectMerchantOutput()
                {
                    Result = 200,
                    ExtraInfo = null
                };
            }
            if (input.RequiredOtp.Value)
            {
                var requestInfo = JsonConvert.SerializeObject(input);
                var cacheConnectMerchant = new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromSeconds(450));
                await _cache.SetStringAsync("ConnectMerchant" + input.SessionId + input.LinkID_PhoneNumber, requestInfo, cacheConnectMerchant);
            }
            try
            {
                var rewardResult = await PostRewardAsync<RewardMemberSendOtpForConnectMerchantDto>(RewardApiUrl.MEMBER_SEND_OTP_CONNECT, requestRequest, MerchantNameConfig.VPBank);
                _logger.LogInformation("Send otp connect merchant reward result: " + JsonConvert.SerializeObject(rewardResult));
                var connectSource = input.ConnectSource ?? 2;
                var listAllowedConnectSource = new List<int>() { 1, 2, 247 };
                if (!listAllowedConnectSource.Contains(connectSource))
                {
                    // Nếu truyền gì đó khác 3 giá trị trên thì default = 2
                    connectSource = 2;
                }
                // Nếu không yêu cầu otp thì tạo member và link connect luôn
                if (!input.RequiredOtp.Value)
                {
                    var memberInfo = rewardResult.Item;
                    var loyaltyResult = await _loyaltyMemberService.VerifyConfirmConnectForConnect(new VerifyConfirmConnectForConnectInput()
                    {
                        CifCode = input.LoyaltyInfo.Cif,
                        LinkID_MemberID = memberInfo.MemberId,
                        LinkID_PhoneNumber = memberInfo.PhoneNumber,
                        LinkID_WalletAddress = memberInfo.MemberWalletAddress,
                        ConnectSource = connectSource
                    });
                    resultMemberId = memberInfo.MemberId;
                    _logger.LogInformation("Verify otp connect merchant loyalty result: " + JsonConvert.SerializeObject(loyaltyResult));
                }
            } catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    if (res.Code == "ConnectInvalidHistoryVPBank")
                    {
                        CommonHelper.GetErrorValidation("222", "VPB member used to connect with other LinkID member. Cannot connect");
                    }
                }
                throw ex;
            }
            var ret = new RewardMemberSendOtpForConnectMerchantOutput()
            {
                Result = 200,
                ExtraInfo = null
            };
            if (resultMemberId > 0)
            {
                ret.ExtraInfo = new Dictionary<string, int>()
                {
                    { "MemberId", resultMemberId }
                };
            }
            return ret;
        }

        public async Task<RewardMemberVerifyOtpForConnectMerchantOutput> VerifyOtpForConnectMerchant(RewardMemberVerifyOtpForConnectMerchantInput input)
        {
            // Validation
            if (string.IsNullOrWhiteSpace(input.OtpCode))
            {
                CommonHelper.GetErrorValidation("OTPRequired", "OtpCode is required");
            }
            if (string.IsNullOrWhiteSpace(input.LinkID_PhoneNumber))
            {
                CommonHelper.GetErrorValidation("LinkIDPhoneRequired", "LinkID Phone Number is required");
            }
            if (string.IsNullOrWhiteSpace(input.SessionId))
            {
                CommonHelper.GetErrorValidation("SessionIdRequired", "SessionId is required");
            }
            var connectSource = input.ConnectSource ?? 2;
            var listAllowedConnectSource = new List<int>() { 1, 2, 247 };
            if (!listAllowedConnectSource.Contains(connectSource))
            {
                // Nếu truyền gì đó khác 3 giá trị trên thì default = 2
                connectSource = 2;
            }
            // Dummy for test, remove after golive
            // if (input.LinkID_PhoneNumber == "+84975764966")
            // {
            //     System.Threading.Thread.Sleep(180000);
            // }
            // End validation
            _logger.LogInformation("Verify otp connect merchant request: " + JsonConvert.SerializeObject(input.LinkID_PhoneNumber));
            var cacheRequest = await _cache.GetStringAsync("ConnectMerchant" + input.SessionId + input.LinkID_PhoneNumber);
            if (string.IsNullOrWhiteSpace(cacheRequest))
            {
                await VerifyOtpExpired(new RewardMemberVerifyOtpExpiredInput()
                {
                    OtpCode = input.OtpCode,
                    PhoneNumber = input.LinkID_PhoneNumber,
                    SessionId = input.SessionId,
                    SmsType = "ConnectLinkId",
                });
                var ex = new RewardException();
                var error = new RewardDataExceptionResponse()
                {
                    result = new RewardDataExceptionResultItem()
                    {
                        code = "InvalidOtp",
                        message = "Invalid otp"
                    },
                    status = 500
                };
                ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
                ex.Data.Add("StatusCode", 400);
                throw ex;
            }
            var requestInfo = JsonConvert.DeserializeObject<RewardMemberSendOtpForConnectMerchantInput>(cacheRequest);
            var loyaltyInfo = requestInfo.LoyaltyInfo;
            var merchantId = Convert.ToInt32(_configuration.GetSection("RewardVPBank:MerchantId").Value);
            if (loyaltyInfo == null)
            {
                var ex = new RewardException();
                var error = new RewardDataExceptionResponse()
                {
                    result = new RewardDataExceptionResultItem()
                    {
                        code = "InvalidLoyaltyInfoData",
                        message = "Loyalty info cannot not null"
                    },
                    status = 500
                };
                ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
                ex.Data.Add("StatusCode", 400);
                throw ex;
            }
            // check connect history first
            await CheckHistoryConnect(new CheckConnectHistoryInput()
            {
                CifCode = loyaltyInfo.Cif,
                LinkID_PhoneNumber = input.LinkID_PhoneNumber,
            });
            var request = new RewardMemberVerifyOtpForConnectMerchantInputDto()
            {
                MerchantId = merchantId,
                IdCard = loyaltyInfo.IdCard,
                PartnerPhoneNumber = loyaltyInfo.Phone,
                PhoneNumber = input.LinkID_PhoneNumber,
                Cif = loyaltyInfo.Cif,
                LoyaltyInfo = new RewardMemberVerifyOtpLoyaltyInfoDto()
                {
                    Address = loyaltyInfo.Address,
                    Avatar = "",
                    ChannelType = "",
                    Cif = loyaltyInfo.Cif,
                    Dob = loyaltyInfo.Dob,
                    Email = loyaltyInfo.Email,
                    FullChannelTypeCode = "",
                    FullMemberTypeCode = "Customer",
                    FullRegionCode = "VN",
                    Gender = !(new List<string> { "M", "F", "O" }).Contains(loyaltyInfo.Gender) ? "O" : loyaltyInfo.Gender,
                    IdCard = loyaltyInfo.IdCard,
                    MemberLoyaltyCode = loyaltyInfo.MemberLoyaltyCode,
                    MemberTypeCode = "Customer",
                    Name = loyaltyInfo.Name,
                    PartnerPhoneNumber = loyaltyInfo.Phone,
                    Phone = loyaltyInfo.Phone,
                    RankTypeCode = "Customer",
                    RegionCode = "VN",
                    Segment = loyaltyInfo.Segment,
                    StandardMemberCode = loyaltyInfo.Cif,
                    Type = "Customer",
                    VipType = loyaltyInfo.VipType,
                },
                OtpCode = input.OtpCode,
                SessionId = input.SessionId,
                ConnectSource = "VPBank"
            };
            var resultMemberId = 0;
            try
            {
                var rewardResult = await PostRewardAsync<RewardMemberVerifyOtpForConnectMerchantDto>(RewardApiUrl.MEMBER_VERIFY_OTP_CONNECT, request, MerchantNameConfig.VPBank);
                _logger.LogInformation("Verify otp connect merchant reward result: " + JsonConvert.SerializeObject(rewardResult));
                var memberInfo = rewardResult.Item;
                resultMemberId = memberInfo.MemberId;
                var loyaltyResult = await _loyaltyMemberService.VerifyConfirmConnectForConnect(new VerifyConfirmConnectForConnectInput()
                {
                    CifCode = loyaltyInfo.Cif,
                    LinkID_MemberID = memberInfo.MemberId,
                    LinkID_PhoneNumber = memberInfo.PhoneNumber,
                    LinkID_WalletAddress = memberInfo.MemberWalletAddress,
                    ConnectSource = connectSource
                });
                _logger.LogInformation("Verify otp connect merchant loyalty result: " + JsonConvert.SerializeObject(loyaltyResult));
                await _cache.RemoveAsync("ConnectMerchant" + input.SessionId + input.LinkID_PhoneNumber);
            } catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    if (res.Code == "ConnectInvalidHistoryVPBank")
                    {
                        CommonHelper.GetErrorValidation("222", "VPB member used to connect with other LinkID member. Cannot connect");
                    }
                }
                throw ex;
            }
            return new RewardMemberVerifyOtpForConnectMerchantOutput()
            {
                Result = 200,
                ExtraInfo = new Dictionary<string, int>()
                {
                    {"MemberId", resultMemberId}
                }
            };
        }

        public async Task<RewardMemberConfirmConnectOutput> ConfirmConnect(RewardMemberConfirmConnectInput input)
        {
            // Validation
            if (string.IsNullOrWhiteSpace(input.LinkID_PhoneNumber))
            {
                CommonHelper.GetErrorValidation("LinkIDPhoneRequired", "LinkID Phone Number is required");
            }
            if (string.IsNullOrWhiteSpace(input.Action))
            {
                CommonHelper.GetErrorValidation("ActionRequired", "Action is required");
            }
            if (!(new List<string> { "Confirmed", "Removed" }).Contains(input.Action))
            {
                CommonHelper.GetErrorValidation("ActionInvalidData", "Action must be Confirmed/Removed");
            }
            // End validation
            var merchantId = Convert.ToInt32(_configuration.GetSection("RewardVPBank:MerchantId").Value);
            var requestReward = new RewardMemberConfirmConnectDto()
            {
                MerchantId = merchantId,
                Action = input.Action,
                PhoneNumber = input.LinkID_PhoneNumber,
                ConnectSource = "VPBank"
            };
            var rewardConfirm = await PostRewardAsync<RewardMemberConfirmConnectOutputDto>(RewardApiUrl.MEMBER_CONFIRM_CONNECT, requestReward, MerchantNameConfig.VPBank);
            var linkIdMemberId = rewardConfirm.Item.MemberId;
            _logger.LogInformation("Reward confirm result: " + JsonConvert.SerializeObject(rewardConfirm));
            // ConnectSource: { 1: LinkID, 2: VPO, 247: Portal247 }
            var connectSource = input.ConnectSource ?? 2;
            var listAllowedConnectSource = new List<int>() { 1, 2, 247 };
            if (!listAllowedConnectSource.Contains(connectSource))
            {
                // Nếu truyền gì đó khác 3 giá trị trên thì default = 2
                connectSource = 2;
            }
            if (input.Action == StatusConnectMerchant.Confirmed)
            {
                await RetryConfirmConnect(linkIdMemberId, connectSource);
            }
            else
            {
                await RetryUnConfirmConnect(linkIdMemberId);
            }
            return new RewardMemberConfirmConnectOutput()
            {
                Result = 200, ExtraInfo = new Dictionary<string, int>()
                {
                    {"MemberId", linkIdMemberId}
                }
            };
        }

        private async Task CheckHistoryConnect(CheckConnectHistoryInput input)
        {
            try
            {
                await _loyaltyMemberService.CheckConnectHistory(input);
            } catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    if (res.Code == "213" || res.Code == "223")
                    {
                        CommonHelper.GetErrorValidation("ConnectInvalidHistory", "LinkID member used to connect with other CIF code. Cannot connect");
                    } else if (res.Code == "752")
                    {
                        CommonHelper.GetErrorValidation("222", "VPB member used to connect with other LinkID member. Cannot connect");
                    }
                }
                throw ex;
            }
        }

        // For retry service
        private async Task RetryConfirmConnect(int LinkIdMemberId, int connectSource)
        {
            var retryNum = 3;
            while (retryNum != 0)
            {
                var result = await ConfirmConnect(LinkIdMemberId, connectSource);
                if (result)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        private async Task<bool> ConfirmConnect(int LinkIdMemberId, int connectSource)
        {
            try
            {
                var result = await _loyaltyMemberService.ConfirmConnect(new LoyaltyMemberConfirmConnectMerchantInput()
                {
                    LinkID_MemberID = LinkIdMemberId,
                    ConnectSource = connectSource,
                });
                _logger.LogInformation("Loyalty confirm result: " + JsonConvert.SerializeObject(result));
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Confirm connect member loyalty error " + ex.Message);
                return false;
            }
        }

        private async Task RetryUnConfirmConnect(int LinkIdMemberId)
        {
            var retryNum = 3;
            while (retryNum != 0)
            {
                var result = await UnConfirmConnect(LinkIdMemberId);
                if (result)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        private async Task<bool> UnConfirmConnect(int LinkIdMemberId)
        {
            try
            {
                var result = await _loyaltyMemberService.RemoveConnect(new LoyaltyMemberRemoveConnectMerchantInput()
                {
                    LinkID_MemberID = LinkIdMemberId
                    
                });
                _logger.LogInformation("Loyalty Unconfirm result: " + JsonConvert.SerializeObject(result));
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Confirm connect member loyalty error " + ex.Message);
                return false;
            }
        }

        public async Task<RewardGetMemberInfoResponse> GetMemberInfo(int MemberId)
        {
            return await GetRewardAsync<RewardGetMemberInfoResponse>
                (RewardApiUrl.GET_MEMBER_INFO, new RewardGetMemberInfoRequest
                {
                    MemberId = MemberId
                }, MerchantNameConfig.VPBank);
        }

        public async Task<RewardMemberSendOtpConfirmOutput> SendOtpConfirm(RewardMemberSendOtpConfirmInput input)
        {
            return await PostRewardAsync<RewardMemberSendOtpConfirmOutput>
                (RewardApiUrl.SEND_OTP_CONFIRM, input, MerchantNameConfig.VPBank);
        }

        public async Task<RewardMemberVerifyOtpConfirmOutput> VerifyOtpConfirm(RewardMemberVerifyOtpConfirmInput input)
        {
            return await PostRewardAsync<RewardMemberVerifyOtpConfirmOutput>
                (RewardApiUrl.VERIFY_OTP_CONFIRM, input, MerchantNameConfig.VPBank);
        }

        public async Task<RewardMemberVerifyCreateRedeemOutput> VerifyCreateRedem(RewardMemberVerifyCreateRedeemInput input)
        {
            return await PostRewardAsync<RewardMemberVerifyCreateRedeemOutput>
               (RewardApiUrl.VERIFY_CREATE_REDDEM, input, MerchantNameConfig.VPID);
        }

        public async Task<RewardMemberVerifyOtpExpiredOutput> VerifyOtpExpired(RewardMemberVerifyOtpExpiredInput input)
        {
            return await PostRewardAsync<RewardMemberVerifyOtpExpiredOutput>
                (RewardApiUrl.VERIFY_EXPIRED_OTP, input, MerchantNameConfig.VPBank);
        }

        public async Task<RewardMemberGetCifByPhoneNumberOutput> GetCifByPhoneNumber(RewardMemberGetCifByPhoneNumberInput input)
        {
            return await PostRewardAsync<RewardMemberGetCifByPhoneNumberOutput>
                (RewardApiUrl.GET_CIF_BY_PHONE_NUMBER, input);
        }

        public async Task<RewardMemberVerifyOtpMerchantViewOutput> sendOtpForConnectMerchantView(RewardMemberSendOtpForConnectMerchantViewInput input)
        {
            return await PostRewardAsync<RewardMemberVerifyOtpMerchantViewOutput>
                (RewardApiUrl.SEND_OTP_MERCHANT_VIEW, input);
        }

        public async Task<RewardMemberVerifyOtpMerchantViewOutput> verifyOtpForConnectMerchantView(RewardMemberVerifyOtpMerchantViewInput input)
        {
            return await PostRewardAsync<RewardMemberVerifyOtpMerchantViewOutput>
                (RewardApiUrl.VERIFY_OTP_MERCHANT_VIEW, input);
        }

        public async Task<CreateUnconfirmedConnectionOutput> CreateUnconfirmedConnection(
            CreateUnconfirmedConnectionInputSendOperator input)
        {
            return await PostRewardAsync<CreateUnconfirmedConnectionOutput>(RewardApiUrl.CreateUnconfirmedConnection, input);
        }
        
        /**
         * Gọi đến API Member/Create của opeartor
         */
        public async Task<RewardMemberCreateOutput> CreateLinkIdMember(CreateLinkIdMemberInput input)
        {
            var gender = input.Gender;
            if (gender != "M" && gender != "F" && gender != "O")
            {
                gender = "O";
            }
            var createMemberDto = new RewardMemberCreateDto()
            {
                NationalId = input.StandardMemberCode,
                IdCard = input.IdCard,
                PartnerPhoneNumber = input.PartnerPhoneNumber,
                Type = "Member",
                Phone = input.Phone,
                Gender = gender,
                Status = "A",
                RankTypeCode = "Customer",
                IsDeleted = false,
                Address = input.Address,
                Dob = input.Dob,
                Name = input.Name,
                Email = input.Email,
                PointUsingOrdinary = "",
                HashAddress = "",
                D365Id = input.StandardMemberCode,
                MbcId = input.StandardMemberCode,
                MbcCardId = input.StandardMemberCode,
                RegionCode = "",
                FullRegionCode = "",
                MemberTypeCode = "",
                FullMemberTypeCode = "",
                ChannelType = "",
                FullChannelTypeCode = "",
                StandardMemberCode = input.StandardMemberCode,
                ReferralCode = GenReferralCode(input.Phone, input.StandardMemberCode),
                Avatar = "",
            };

            return await PostRewardAsync<RewardMemberCreateOutput>(RewardApiUrl.MEMBER_CREATE, createMemberDto);
        }

        public async Task<RewardSendAckAfterConnectedOutput> RewardSendAckAfterConnected(
            RewardSendAckAfterConnectedInput input)
        {
            return await PostRewardAsync<RewardSendAckAfterConnectedOutput>(RewardApiUrl.RewardSendAckAfterConnected, input);
        }

        public async Task<GetListMemberCodeByListPhoneNumberOutput> GetListMemberCodeByListPhoneNumber(GetListMemberCodeByListPhoneNumberInput input)
        {
            return await PostRewardAsync<GetListMemberCodeByListPhoneNumberOutput>(RewardApiUrl.GetMemberCodeByPhoneNumber, input);
        }

        public async Task<GetListConnectedMerchantSimplifiedByMemberIdOutput> GetListConnectedMerchantSimplifiedByMemberId(
            GetListConnectedMerchantSimplifiedByMemberIdInput input)
        {
            return await GetRewardAsync<GetListConnectedMerchantSimplifiedByMemberIdOutput>(RewardApiUrl.GetListConnectedMerchantSimplifiedByMemberId, input);
        }

        public async Task<LoyaltyResponse<LoyaltyRewardMemberViewPointV2>> ViewBalanceWithExpiringCoinsByMemberCode(RewardMemberRequestInput input, string authorization)
        {
            if (string.IsNullOrWhiteSpace(input.MemberCode))
            {
                CommonHelper.GetErrorValidation("911", "Member code is required");
            }

            var request = new RewardMemberViewPointInput()
            {
                NationalId = input.MemberCode
            };

            var exception = new Exception();

            var loyaltyViewPointResult = new LoyaltyResponse<LoyaltyViewPointWithGrantTypeOutput>();
            try
            {
                loyaltyViewPointResult = await _customersService.ViewPointWithGrantType(input.MemberCode, false);
            }
            catch (Exception ex)
            {
                exception = ex;
                loyaltyViewPointResult = null;
            }
            var rewardViewPointResult = new RewardViewPointWithGrantTypeOutput();
            // View reward token when member has connect LinkID
            if (loyaltyViewPointResult != null && !string.IsNullOrWhiteSpace(loyaltyViewPointResult.Result.LinkID_WalletAddress))
            {
                try
                {
                    rewardViewPointResult = await GetRewardAsync<RewardViewPointWithGrantTypeOutput>(RewardApiUrl.VIEW_POINT_WITH_GRANTTYPE,
                        new { WalletAddress = loyaltyViewPointResult.Result.LinkID_WalletAddress }, MerchantNameConfig.VPBank);
                }
                catch (Exception ex)
                {
                    exception = ex;
                    rewardViewPointResult = null;
                }
            }

            if (rewardViewPointResult == null && loyaltyViewPointResult == null)
            {
                throw exception;
            }
            else
            {
                var result = new LoyaltyResponse<LoyaltyRewardMemberViewPointV2>()
                {
                    Result = new LoyaltyRewardMemberViewPointV2()
                    {
                        MemberCode = input.MemberCode
                    },
                    Success = loyaltyViewPointResult != null ? loyaltyViewPointResult.Success : false,
                };

                if (rewardViewPointResult != null && rewardViewPointResult.Items != null)
                {
                    result.Result.TotalToken = rewardViewPointResult.Items.TotalToken;
                    result.Result.Reward = rewardViewPointResult.Items.GrantTypeBalance;
                }

                if (loyaltyViewPointResult != null)
                {
                    result.Result.TotalCoin = loyaltyViewPointResult.Result.TotalCoin;
                    result.Result.TotalAutoExchangeCoin = loyaltyViewPointResult.Result.TotalAutoExchangeCoin;
                    result.Result.Loyalty = new LoyaltyViewPointResponse()
                    {
                        Point = loyaltyViewPointResult.Result.Point,
                        Coin = loyaltyViewPointResult.Result.Coin
                    };

                    var requestViewTokenExpiring = new ViewBalanceWithExpiringCoinInput()
                    {
                        UserAddress = loyaltyViewPointResult.Result.LinkID_WalletAddress
                    };

                    var resultViewTokenExpiring = ViewBalanceWithExpiringCoins(requestViewTokenExpiring, authorization);
                    if (resultViewTokenExpiring != null)
                    {
                        result.Result.ExpiringDate = resultViewTokenExpiring.Result?.Items?.ExpiringDate;
                        result.Result.ExpiringTokenAmount = resultViewTokenExpiring.Result?.Items?.ExpiringTokenAmount;
                    }
                }

                return result;
            }
        }

        public async Task<RewardCreateSmeConnectionOutput> RewardCreateSmeConnection(
            RewardCreateSmeConnectionInput input)
        {
            var res = await PostRewardAsync<RewardCreateSmeConnectionOutputWrapper>(RewardApiUrl.SME_CREATECONNECTION, input);
            return res.Item;
        }

        public  async Task<RewardAckSmeConnectionOutput> RewardAckSmeConnection(RewardAckSmeConnectionInput input)
        {
            return await PostRewardAsync<RewardAckSmeConnectionOutput>(RewardApiUrl.SME_ACK_CONNECTION, input);
        }

        public async Task<ViewSmePointResponse> ViewSmePoint(ViewSmePointRequest request)
        {
            return await GetRewardAsync<ViewSmePointResponse>(RewardApiUrl.SME_VIEWPOINT, request);
        }
        private enum ErrorCode
        {
            MemberNotExists = 101,
            PhoneNumberAlreadyExists = 201
        }

        private enum RevokeTokenErrorCode
        {
            Success = 1,
            IntenalServerError = 2,
            AccountIsLocked = 3,
            CanNotVerifyPinCodeNow = 4,
            MemberNotExists = 5
        }
    }
}
