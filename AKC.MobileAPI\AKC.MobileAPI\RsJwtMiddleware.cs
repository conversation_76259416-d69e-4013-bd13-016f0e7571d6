﻿using AKC.MobileAPI.Service;
using JwtManager;
using JwtManager.Helpers;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI
{
    public class Payload
    {
        public string Token { get; set; }
    }

    public class RSJwtConfig
    {
        public string Key { get; set; }
    }

    public static class RsJwtMiddleware
    {
        public static void AddValidateRS256TokenMiddleware(this IApplicationBuilder app)
        {
            app.Use(async (context, next) =>
            {
                context.Request.Headers.TryGetValue("Authorization", out var value);

                if (!string.IsNullOrEmpty(value))
                {
                    var splitList = value.ToString().Split(" ");
                    var token = splitList.Last();
                    var partnerCode = context.Request.Headers["X-Partner"].ToString();
                    var configuration = AccessConfigurationService.Instance.GetConfiguration();
                    string key = string.Empty;
                    string keyValue = string.Empty;
                    if (string.IsNullOrEmpty(partnerCode))
                    {
                        key = configuration.GetValue<string>("AppSettings:EncryptionKey");
                        keyValue = configuration.GetValue<string>("AppSettings:Value");
                    }
                    else
                    {
                        key = configuration.GetValue<string>(partnerCode + ":AppSettings:EncryptionKey");
                        keyValue = configuration.GetValue<string>(partnerCode + ":AppSettings:Value");
                        if (string.IsNullOrEmpty(key) || string.IsNullOrEmpty(keyValue))
                        {
                            context.Response.StatusCode = 400;
                            context.Response.ContentType = "application/json";
                            var error = new
                            {
                                Code = "BadRequest",
                                Message = "Partner not exits",
                                Result = StatusCodes.Status400BadRequest,
                            };
                            string jsonString = JsonConvert.SerializeObject(error);
                            await context.Response.WriteAsync(jsonString, Encoding.UTF8);
                            return;
                        }
                    }
                    try
                    {
                        RsJwt.Instance.Validate(token, key, keyValue);
                    }
                    catch (Exception ex)
                    {
                        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                        context.Response.ContentType = "application/json";
                        var error = new
                        {
                            Code = "Unauthorized",
                            Message = "Unauthorized access data",
                            Result = StatusCodes.Status401Unauthorized,
                        };
                        string jsonString = JsonConvert.SerializeObject(error);
                        await context.Response.WriteAsync(jsonString, Encoding.UTF8);
                        return;
                    }
                    if (partnerCode == "NEOBiz" && !context.Request.Path.StartsWithSegments("/api/Sme"))
                    {
                        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                        context.Response.ContentType = "application/json";
                        var error = new
                        {
                            Code = "Unauthorized",
                            Message = "Unauthorized access data",
                            Result = StatusCodes.Status401Unauthorized,
                        };
                        string jsonString = JsonConvert.SerializeObject(error);
                        await context.Response.WriteAsync(jsonString, Encoding.UTF8);
                        return;
                    }
                    if ((string.IsNullOrEmpty(partnerCode) || partnerCode != "NEOBiz") && context.Request.Path.StartsWithSegments("/api/Sme"))
                    {
                        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                        context.Response.ContentType = "application/json";
                        var error = new
                        {
                            Code = "Unauthorized",
                            Message = "Unauthorized access data",
                            Result = StatusCodes.Status401Unauthorized,
                        };
                        string jsonString = JsonConvert.SerializeObject(error);
                        await context.Response.WriteAsync(jsonString, Encoding.UTF8);
                        return;
                    }
                    var userRule = "Test";
                    var userName = "Test";
                    var claims = new[]
                    {
                       new Claim("name", userName),
                       new Claim(ClaimTypes.Role, userRule),
                   };
                    var identity = new ClaimsIdentity(claims, "basic");
                    context.User = new ClaimsPrincipal(identity);
                }
                await next();
            });
        }
    }
}