﻿using AKC.MobileAPI.Service;
using Gelf.Extensions.Logging;
using log4net;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.Logging;
using Microsoft.IO;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web.Http;

namespace AKC.MobileAPI.AuditLog
{
    public class RequestLoginMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly RecyclableMemoryStreamManager _recyclableMemoryStreamManager;


        public RequestLoginMiddleware(RequestDelegate next, ILoggerFactory loggerFactory)
        {
            _next = next;
            _recyclableMemoryStreamManager = new RecyclableMemoryStreamManager();
        }

        public async Task Invoke(HttpContext context)
        {
            var endpoint = context.GetEndpoint();

            if (endpoint != null)
            {
                var logData = await GetRequestLogData(context, endpoint);

                LogRequest(logData);
            }

            await _next(context);
        }

        private void LogRequest(RequestLogData logData)
        {
            AuditLoggerProvider.Instance.LogAudit(logData);
        }

        private async Task<RequestLogData> GetRequestLogData(HttpContext context, Endpoint endpoint)
        {
            context.Request.EnableBuffering();
            var requestStream = _recyclableMemoryStreamManager.GetStream();
            await context.Request.Body.CopyToAsync(requestStream);

            var result = new RequestLogData()
            {
                Browser = context.Request.Headers["User-Agent"],
                Ip = context.Connection.RemoteIpAddress.ToString(),
                Scheme = context.Request.Scheme,
                Method = context.Request.Method,
                ServerHost = context.Request.Host.Value,
                Path = context.Request.Path.Value,
                QueryString = JsonConvert.SerializeObject(context.Request.Query.ToDictionary(k => k.Key, v => v.Value)),
                Body = ReadStreamInChunks(requestStream)
            };

            // Reset stream Position request body.
            context.Request.Body.Position = 0;

            var controllerActionDescriptor = endpoint.Metadata.GetMetadata<ControllerActionDescriptor>();
            if (controllerActionDescriptor != null)
            {
                result.ControllerName = controllerActionDescriptor.ControllerName;
                result.ActionName = controllerActionDescriptor.ActionName;
            }

            return result;
        }

        private static string ReadStreamInChunks(Stream stream)
        {
            const int readChunkBufferLength = 4096;
            stream.Seek(0, SeekOrigin.Begin);
            var textWriter = new StringWriter();
            var reader = new StreamReader(stream);
            var readChunk = new char[readChunkBufferLength];

            int readChunkLength;
            do
            {
                readChunkLength = reader.ReadBlock(readChunk, 0, readChunkBufferLength);
                textWriter.Write(readChunk, 0, readChunkLength);
            } while (readChunkLength > 0);

            return textWriter.ToString();
        }
    }
}
