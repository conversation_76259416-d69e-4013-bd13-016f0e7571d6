﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.GiftTransactions
{
    public class CheckVoucherOutput
    {
        public CheckVoucherOutputResult Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class CheckVoucherOutputResult
    {
        public string ErrorCode { get; set; }
        public string ErrorMessage { get; set; }
        public string VoucherCode { get; set; }
        public decimal? TopupAmount { get; set; }
        public string VendorName { get; set; }
        public string ExpiredDate { get; set; }
        public string VendorPhoto { get; set; }
        public int? Count { get; set; }
        public int? RetryMax { get; set; }
        public string GiftRedeemTransactionCode { get; set; }
    }
}
