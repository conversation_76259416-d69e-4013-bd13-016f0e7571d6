﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <UserSecretsId>3e6d0b83-8039-4f2f-96d5-3a32a2f30670</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="log4net-audit.config" />
    <Content Remove="log4net.config" />
  </ItemGroup>

  <ItemGroup>
    <None Include="log4net-audit.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="log4net.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Cronos" Version="0.7.0" />
    <PackageReference Include="FirebaseAdmin" Version="1.14.0" />
    <PackageReference Include="Gelf.Extensions.Logging" Version="2.0.0" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Core" Version="5.2.7" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="3.1.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="3.1.0-preview1.19506.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="3.1.0-preview1.19506.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.6" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="3.1.0-preview1.19506.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Log4Net.AspNetCore" Version="3.1.0" />
    <PackageReference Include="Microsoft.IO.RecyclableMemoryStream" Version="1.3.4" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.9.5" />
    <PackageReference Include="Portable.BouncyCastle" Version="1.8.10" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="5.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AKC.MobileAPI.DTO\AKC.MobileAPI.DTO.csproj" />
    <ProjectReference Include="..\AKC.MobileAPI.Service\AKC.MobileAPI.Service.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="App_Data\Logs\" />
  </ItemGroup>


</Project>
