﻿using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Const;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Loyalty.LocationManagement;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.Loyalty.Transaction;
using AKC.MobileAPI.DTO.MerchantGift;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.TopUpTransaction;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.LoyaltyVendorGift;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class MerchantGiftService : IMerchantGiftService
    {
        private readonly ILoyaltyMemberService _loyaltyMemberService;
        private readonly IRewardMemberService _rewardMemberService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILogger<MerchantGiftService> _logger;
        private readonly ILinkIdLoyaltyVendorGiftService _linkIdLoyaltyService;
        private readonly IConfiguration _configuration;
        private readonly IDistributedCache _cache;
        private readonly IRewardGiftRedeemTransactionService _rewardGiftRedeemTransactionService;
        private int GIFT_PRICE_THAT_NEEDS_SMS_VERIFICATION = 2_000_000;
        public MerchantGiftService(
            IExceptionReponseService exceptionReponseService,
            ILogger<MerchantGiftService> logger,
            ILinkIdLoyaltyVendorGiftService linkIdLoyaltyService,
            IRewardMemberService rewardMemberService,
            ILoyaltyMemberService loyaltyMemberService,
            IConfiguration configuration,
            IDistributedCache c,
            IRewardGiftRedeemTransactionService rewardGiftRedeemTransactionService
            )
        {
            _exceptionReponseService = exceptionReponseService;
            _logger = logger;
            _linkIdLoyaltyService = linkIdLoyaltyService;
            _rewardMemberService = rewardMemberService;
            _loyaltyMemberService = loyaltyMemberService;
            _configuration = configuration;
            _cache = c;
            var threshold = _configuration.GetSection("GIFT_PRICE_THAT_NEEDS_SMS_VERIFICATION").Value;
            if (string.IsNullOrWhiteSpace(threshold)) return;
            try
            {
                GIFT_PRICE_THAT_NEEDS_SMS_VERIFICATION = int.Parse(threshold);
                if (GIFT_PRICE_THAT_NEEDS_SMS_VERIFICATION < 0)
                {
                    GIFT_PRICE_THAT_NEEDS_SMS_VERIFICATION = 2_000_000;
                }
            }
            catch (Exception)
            {
                GIFT_PRICE_THAT_NEEDS_SMS_VERIFICATION = 2_000_000;
            }
            _rewardGiftRedeemTransactionService = rewardGiftRedeemTransactionService;
        }

        /**
         * Khi số tiền 2tr trở lên thì sẽ theo flow cũ
         * Khi dưới 2tr, thì bỏ qua bước gửi OTP và thực hiện gọi sang LinkidLoyalty luôn
         */
        public async Task<MerchantGiftSendOtpCreateRedeemOutput> SendOtpCreateRedeem(MerchantGiftSendOtpCreateRedeemInput input)
        {
            if (string.IsNullOrWhiteSpace(input.CifCode))
            {
                CommonHelper.GetErrorValidation("902", "Cif code is required");
            }
            if (!input.Quantity.HasValue || input.Quantity <= 0)
            {
                CommonHelper.GetErrorValidation("922", "Quantity must be a positive integer");
            }
            if (!input.TotalAmount.HasValue || input.TotalAmount < 0 || (input.TotalAmount % 1) != 0)
            {
                CommonHelper.GetErrorValidation("923", "Total amount must be a positive integer");
            }
            if (string.IsNullOrWhiteSpace(input.GiftCode))
            {
                CommonHelper.GetErrorValidation("921", "Gift code is required");
            }
            //var merchantId = Convert.ToInt32(_configuration.GetSection("LoyaltyLinkID:MerchantIdRedeem").Value);
            var member = await CheckAndGetMemberInfo(new GetLinkIdMemberByCifCodeInput
            {
                CifCode = input.CifCode
            });

            var merchantIdFromGiftCode = await _linkIdLoyaltyService.GetMerchantIdFromGiftCode(new LoyaltyGiftGetMerchantIdFromGiftCodeInput()
            {
                GiftCode = input.GiftCode
            });

            if (merchantIdFromGiftCode == null || !merchantIdFromGiftCode.Success || merchantIdFromGiftCode.Result == null || !merchantIdFromGiftCode.Result.MerchantId.HasValue)
            {
                CommonHelper.GetErrorValidation("924", "Cannot find merchant");
            }
            var merchantId = merchantIdFromGiftCode.Result.MerchantId.Value;

            var orderCode = genOrderCode(input.GiftCode, member.NationalId);
            // Dù luồng cũ hay mới thì vẫn cần check số dư trước khi thực hiện đổi
            await _rewardMemberService.VerifyCreateRedem(new RewardMemberVerifyCreateRedeemInput()
            {
                MemberId = member.LinkID_MemberID,
                MerchantId = merchantId,
                TotalRequestedAmount = input.TotalAmount.Value,
            });

            var request = new MerchantGiftVerifyCreateRedeemInputDto()
            {
                Description = input.Description,
                GiftCode = input.GiftCode,
                MemberCode = member.NationalId,
                Quantity = input.Quantity.Value,
                TotalAmount = input.TotalAmount.Value,
                Date = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                TransactionCode = orderCode,
                OtpSession = input.SessionId,
                VpoCifCode = input.CifCode,
            };
            try
            {
                await _linkIdLoyaltyService.VerifyCreateRedeem(request);
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    CommonHelper.GetErrorValidation(CommonHelper.GetCodeRedeemFromCode(res.Code), CommonHelper.GetMessageRedeemFromCode(res.Code));
                    throw ex;
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    _logger.LogInformation($"Error message from code {res.Message}");
                    if (res.Code == "-1")
                    {
                        CommonHelper.GetErrorValidation(CommonHelper.GetCodeRedeemFromCode(res.Message), CommonHelper.GetMessageRedeemFromCode(res.Message));
                    }
                    else
                    {
                        CommonHelper.GetErrorValidation(CommonHelper.GetCodeRedeemFromCode(res.Code), CommonHelper.GetMessageRedeemFromCode(res.Code));
                    }
                    throw ex;
                }
            }
            // Với số tiền lớn hơn mốc config thì mới bắn OTP
            var isOtpSent = false;
            if (input.TotalAmount.Value >= GIFT_PRICE_THAT_NEEDS_SMS_VERIFICATION)
            {
                await _rewardMemberService.SendOtpConfirm(new RewardMemberSendOtpConfirmInput()
                {
                    PhoneNumber = member.LinkID_PhoneNumber,
                    SessionId = input.SessionId,
                    SmsType = "RedeemGift"
                });
                isOtpSent = true;
            }
            else
            {
                var cacheAllowRenewTokenFlagCacheOptions = new DistributedCacheEntryOptions()
                    .SetAbsoluteExpiration(TimeSpan.FromMinutes(10));
                await _cache.SetStringAsync("REDEEM_" + input.SessionId, "CALLER_VERIFIED", cacheAllowRenewTokenFlagCacheOptions);
            }

            return new MerchantGiftSendOtpCreateRedeemOutput()
            {
                Result = 200,
                IsOtpSent = isOtpSent,
            };
        }

        public async Task<MerchantGiftCreateRedeemOutput> CreateRedeem(MerchantGiftCreateRedeemInput input)
        {
            if (string.IsNullOrWhiteSpace(input.CifCode))
            {
                CommonHelper.GetErrorValidation("902", "Cif code is required");
            }
            var member = await CheckAndGetMemberInfo(new GetLinkIdMemberByCifCodeInput
            {
                CifCode = input.CifCode
            });
            if (!string.IsNullOrWhiteSpace(input.OtpCode) && input.OtpCode.Equals("CALLER_VERIFIED"))
            {
                // NO OTP VERIFICATION IN LINKID SIDE
                var cachedValue = await _cache.GetStringAsync("REDEEM_" + input.SessionId);
                // Nếu CALLER truyền giá trị CALLER_VERIFIED nhưng trong cache ko lưu như vậy, thì báo lỗi
                if (cachedValue != "CALLER_VERIFIED")
                {
                    var ex = new RewardException();
                    var error = new RewardDataExceptionResponse()
                    {
                        result = new RewardDataExceptionResultItem()
                        {
                            code = "ActionNotAllowed",
                            message = "You cannot perform this action. Please try to redeem the gift again",
                        },
                        status = 500
                    };
                    ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
                    ex.Data.Add("StatusCode", 400);
                    throw ex;
                }
            }
            else
            {
                await _rewardMemberService.VerifyOtpExpired(new RewardMemberVerifyOtpExpiredInput()
                {
                    OtpCode = input.OtpCode,
                    PhoneNumber = member.LinkID_PhoneNumber,
                    SessionId = input.SessionId,
                    SmsType = "RedeemGift",
                });
            }

            var cacheRedeemRequest = new MerchantGiftGetCreateRedeemOutputDto();
            try
            {
                var cacheRedem = await _linkIdLoyaltyService.GetCreateRedeemCache(new MerchantGiftGetCreateRedeemInputDto()
                {
                    LinkIdMemberCode = member.NationalId,
                    OtpSession = input.SessionId,
                    VpoCifCode = input.CifCode,
                });
                cacheRedeemRequest = cacheRedem.Result;
                if (cacheRedeemRequest == null)
                {
                    CommonHelper.GetErrorValidation("SessionInvalid", "Session invalid");
                }
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogInformation($"Error message from code {res.Message}");
                if (res.Code == "404")
                {
                    CommonHelper.GetErrorValidation("SessionInvalid", "Session invalid");
                }
                throw ex;
            }
            var request = new MerchantGiftCreateRedeemInputDto()
            {
                Description = cacheRedeemRequest.Description,
                GiftCode = cacheRedeemRequest.GiftCode,
                MemberCode = member.NationalId,
                Quantity = cacheRedeemRequest.Quantity.Value,
                TotalAmount = cacheRedeemRequest.TotalAmount,
                Date = cacheRedeemRequest.Date,
                TransactionCode = cacheRedeemRequest.TransactionCode,
                OtpSession = input.SessionId,
                VpoCifCode = input.CifCode,
                RedeemSource = "Vpbank"
            };
            if (!string.IsNullOrWhiteSpace(input.OtpCode) && input.OtpCode.StartsWith("CALLER_VERIFIED"))
            {
                // NO OTP VERIFICATION IN LINKID SIDE
            }
            else
            {
                await _rewardMemberService.VerifyOtpConfirm(new RewardMemberVerifyOtpConfirmInput()
                {
                    OtpCode = input.OtpCode,
                    SessionId = input.SessionId,
                    SmsType = "RedeemGift",
                    PhoneNumber = member.LinkID_PhoneNumber,
                });
            }

            try
            {
                var result = await _linkIdLoyaltyService.CreateRedeem(member.LinkID_MemberID, request);
                var data = result.Result;
                // Gỡ bỏ cache
                try
                {
                    await _cache.RemoveAsync("REDEEM_" + input.SessionId);
                }
                catch (Exception)
                {
                }
                return new MerchantGiftCreateRedeemOutput()
                {
                    Error = result.Error,
                    Result = new MerchantGiftCreateRedeemTransaction()
                    {
                        Items = data.Items,
                        TotalCount = data.TotalCount,
                    },
                    Success = result.Success,
                };
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    CommonHelper.GetErrorValidation(CommonHelper.GetCodeRedeemFromCode(res.Code), CommonHelper.GetMessageRedeemFromCode(res.Code));
                    throw ex;
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    _logger.LogInformation($"Error message from code {res.Message}");
                    if (res.Code == "-1")
                    {
                        CommonHelper.GetErrorValidation(CommonHelper.GetCodeRedeemFromCode(res.Message), CommonHelper.GetMessageRedeemFromCode(res.Message));
                    }
                    else
                    {
                        CommonHelper.GetErrorValidation(CommonHelper.GetCodeRedeemFromCode(res.Code), CommonHelper.GetMessageRedeemFromCode(res.Code));
                    }
                    throw ex;
                }
            }
        }

        public async Task<AdminBuyGiftForMemberOutput> AdminBuyGiftForMember(AdminBuyGiftForMemberInput input)
        {
            if (string.IsNullOrWhiteSpace(input.CifCode))
            {
                CommonHelper.GetErrorValidation("902", "Cif code is required");
            }
            var member = await CheckAndGetMemberInfo(new GetLinkIdMemberByCifCodeInput
            {
                CifCode = input.CifCode
            });

            var orderCode = genOrderCode(input.GiftCode, member.NationalId);
            var request = new MerchantGiftCreateRedeemInputDto()
            {
                Description = input.Description,
                GiftCode = input.GiftCode,
                MemberCode = member.NationalId,
                Quantity = input.Quantity.Value,
                TotalAmount = input.TotalAmount ?? 0,
                Date = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                TransactionCode = orderCode,
                OtpSession = "",
                VpoCifCode = input.CifCode,
                RedeemSource = "247",
            };
            try
            {
                var result = await _linkIdLoyaltyService.CreateRedeem(member.LinkID_MemberID, request);
                var data = result.Result;

                return new AdminBuyGiftForMemberOutput()
                {
                    Error = result.Error,
                    Result = new MerchantGiftCreateRedeemTransaction()
                    {
                        Items = data.Items,
                        TotalCount = data.TotalCount,
                    },
                    Success = result.Success,
                };
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    CommonHelper.GetErrorValidation(CommonHelper.GetCodeRedeemFromCode(res.Code), CommonHelper.GetMessageRedeemFromCode(res.Code));
                    throw ex;
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    _logger.LogInformation($"Error message from code {res.Message}");
                    if (res.Code == "-1")
                    {
                        CommonHelper.GetErrorValidation(CommonHelper.GetCodeRedeemFromCode(res.Message), CommonHelper.GetMessageRedeemFromCode(res.Message));
                    }
                    else
                    {
                        CommonHelper.GetErrorValidation(CommonHelper.GetCodeRedeemFromCode(res.Code), CommonHelper.GetMessageRedeemFromCode(res.Code));
                    }
                    throw ex;
                }
            }
        }
        public async Task<LoyaltyResponseList<MerchantGiftGetAllCategoryOutput>> GetAllCategory(MerchantGiftGetAllCategoryInput input)
        {
            if (string.IsNullOrWhiteSpace(input.CifCode))
            {
                CommonHelper.GetErrorValidation("902", "Cif code is required");
            }
            if (input.MaxResultCount <= 0)
            {
                input.MaxResultCount = 10;
            }
            if (input.SkipCount <= 0)
            {
                input.SkipCount = 0;
            }

            var member = await CheckAndGetMemberInfo(new GetLinkIdMemberByCifCodeInput
            {
                CifCode = input.CifCode
            });

            var request = new MerchantGiftGetAllCategoryInputDto()
            {
                MaxResultCount = input.MaxResultCount ?? 10,
                SkipCount = input.SkipCount ?? 0,
                MemberCode = member.NationalId,
                Is3rdPartyGiftCategory = false,
                StatusFilter = "A",
                Channel = "VPBank"
            };
            try
            {
                var result = await _linkIdLoyaltyService.GetAllCategory_v1(request);
                var items = result.Result.Items.Select(x => new MerchantGiftGetAllCategoryOutput()
                {
                    Id = x.GiftCategory?.Id,
                    Code = x.GiftCategory?.Code,
                    Description = x.GiftCategory?.Description,
                    FullLink = x.GiftCategory?.ImageLink?.FullLink,
                    Link = x.GiftCategory?.ImageLink?.Link,
                    Name = x.GiftCategory?.Name,
                    ParentCode = x.GiftCategory.ParentCode,
                }).ToList();

                return new LoyaltyResponseList<MerchantGiftGetAllCategoryOutput>()
                {
                    Error = result.Error,
                    Result = new LoyaltyResponsResultDto<MerchantGiftGetAllCategoryOutput>()
                    {
                        Items = items,
                        TotalCount = result.Result.TotalCount
                    },
                    Success = result.Success,
                };
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                if (res.Code == "-1")
                {
                    CommonHelper.GetErrorValidation(GetCodeFromCode(res.Message), GetMessageFromCode(res.Message));
                }
                throw ex;
            }
        }

        public async Task<LoyaltyResponseList<MerchantGiftGetAllCategoryOutput>> GetAllCategoryFor247(MerchantGiftGetAllCategoryInput input)
        {
            if (string.IsNullOrWhiteSpace(input.CifCode))
            {
                CommonHelper.GetErrorValidation("902", "Cif code is required");
            }
            if (input.MaxResultCount <= 0)
            {
                input.MaxResultCount = 10;
            }
            if (input.SkipCount <= 0)
            {
                input.SkipCount = 0;
            }

            var member = await CheckAndGetMemberInfo(new GetLinkIdMemberByCifCodeInput
            {
                CifCode = input.CifCode
            });

            var request = new MerchantGiftGetAllCategoryInputDto()
            {
                MaxResultCount = input.MaxResultCount ?? 10,
                SkipCount = input.SkipCount ?? 0,
                MemberCode = member.NationalId,
                Is3rdPartyGiftCategory = false,
                StatusFilter = "A",
                Channel = input.GiftCategoryChannelCode
            };
            try
            {
                var result = await _linkIdLoyaltyService.GetAllCategory(request);
                var listExclude = new List<string>()
                {
                    "ab978ff5-427f-42cc-958a-8c8241870164", "c3f8b851-2fc0-418a-bf22-3234ce4cdbd7",
                    // IMEDIA TOPUP: Thay giá trị trên production khi golive
                    "81044caf-eddb-40ee-a7c7-b862be9818e0","84afa499-7e53-4cbc-b442-2286ee7ff545"
                };
                var containIt = result.Result.Items.Any(x => listExclude.Contains(x.GiftCategory.Code));
                var items = result.Result.Items.Where(x => !listExclude.Contains(x.GiftCategory.Code))
                    .Select(x => new MerchantGiftGetAllCategoryOutput()
                    {
                        Id = x.GiftCategory?.Id,
                        Code = x.GiftCategory?.Code,
                        Description = x.GiftCategory?.Description,
                        FullLink = x.GiftCategory?.ImageLink?.FullLink,
                        Link = x.GiftCategory?.ImageLink?.Link,
                        Name = x.GiftCategory?.Name,
                        ParentCode = x.GiftCategory.ParentCode,
                    }).ToList();
                var totalCount = result.Result.TotalCount;
                if (containIt && totalCount > 0)
                {
                    totalCount--;
                }

                return new LoyaltyResponseList<MerchantGiftGetAllCategoryOutput>()
                {
                    Error = result.Error,
                    Result = new LoyaltyResponsResultDto<MerchantGiftGetAllCategoryOutput>()
                    {
                        Items = items,
                        TotalCount = totalCount
                    },
                    Success = result.Success,
                };
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                if (res.Code == "-1")
                {
                    CommonHelper.GetErrorValidation(GetCodeFromCode(res.Message), GetMessageFromCode(res.Message));
                }
                throw ex;
            }
        }
        public async Task<LoyaltyResponseList<MerchantGiftLocationOutput>> GetAllLocation(MerchantGiftLocationInput input)
        {
            if (input.MaxResultCount <= 0)
            {
                input.MaxResultCount = 10;
            }
            if (input.SkipCount <= 0)
            {
                input.SkipCount = 0;
            }
            var request = new MerchantGiftLocationInputDto()
            {
                MaxResultCount = input.MaxResultCount ?? 10,
                SkipCount = input.SkipCount ?? 0,
            };
            try
            {
                var result = await _linkIdLoyaltyService.GetAllLocation(request);
                var items = result.Result.Items.Select(x => new MerchantGiftLocationOutput()
                {
                    Code = x.Region?.Code,
                    Id = x.Region?.Id,
                    Name = x.Region?.Name,
                }).ToList();

                return new LoyaltyResponseList<MerchantGiftLocationOutput>()
                {
                    Error = result.Error,
                    Result = new LoyaltyResponsResultDto<MerchantGiftLocationOutput>()
                    {
                        Items = items,
                        TotalCount = result.Result.TotalCount
                    },
                    Success = result.Success,
                };
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                if (res.Code == "-1")
                {
                    CommonHelper.GetErrorValidation(GetCodeFromCode(res.Message), GetMessageFromCode(res.Message));
                }
                throw ex;
            }
        }

        public async Task<LoyaltyResponse<MerchantGiftGetDetailOutput>> GiftDetail(MerchantGiftGetDetailInput input)
        {
            if (string.IsNullOrWhiteSpace(input.CifCode))
            {
                CommonHelper.GetErrorValidation("902", "Cif code is required");
            }
            if (!input.GiftId.HasValue || input.GiftId <= 0)
            {
                CommonHelper.GetErrorValidation("925", "GiftId must be greater than 0");
            }

            var member = await CheckAndGetMemberInfo(new GetLinkIdMemberByCifCodeInput
            {
                CifCode = input.CifCode
            });

            var request = new MerchantGiftGetDetailInputDto()
            {
                GiftId = input.GiftId.Value,
                MaxItem = 10,
                MemberCode = member.NationalId,
            };
            try
            {
                var result = await _linkIdLoyaltyService.GiftDetail(request);
                var item = result.Result;
                if (!string.IsNullOrWhiteSpace(item.ErrorCode))
                {
                    CommonHelper.GetErrorValidation(item.ErrorCode, GetMessageFromCode(item.ErrorCode));
                }
                var images = (item.ImageLink == null || item.ImageLink.Count == 0) ? new List<MerchantGiftImageLinkShortDto>() : item.ImageLink.Select(x => new MerchantGiftImageLinkShortDto()
                {
                    FullLink = x.FullLink,
                    Link = x.Link,
                }).ToList();

                return new LoyaltyResponse<MerchantGiftGetDetailOutput>()
                {
                    Error = result.Error,
                    Result = new MerchantGiftGetDetailOutput()
                    {
                        Id = item.GiftInfor?.Id,
                        Code = item.GiftInfor?.Code,
                        CategoryCode = item.GiftInfor?.FullGiftCategoryCode,
                        Description = item.GiftInfor?.Introduce,
                        ImageLinks = images,
                        InStock = item.GiftInfor?.RemainingQuantity,
                        Name = item.GiftInfor?.Name,
                        RequiredCoin = item.GiftInfor?.RequiredCoin,
                        ShortDescription = item.GiftInfor?.Description,
                        IsEgift = item.GiftInfor?.IsEGift,
                        //Office = item.GiftInfor?.Office,
                        VendorHotline = item.GiftInfor?.VendorHotline,
                        Vendor = item.GiftInfor?.Vendor,
                        BrandAddress = item.GiftInfor?.BrandAddress,
                        BrandDescription = item.GiftInfor?.BrandDescription,
                        BrandName = item.GiftInfor?.BrandName,
                        BrandLinkLogo = item.GiftInfor?.BrandLinkLogo,
                        VendorDescription = item.GiftInfor?.VendorDescription,
                        VendorImage = item.GiftInfor?.VendorImage
                    },
                    Success = result.Success,
                };
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                if (res.Code == "-1")
                {
                    CommonHelper.GetErrorValidation(GetCodeFromCode(res.Message), GetMessageFromCode(res.Message));
                }
                throw ex;
            }
        }

        public async Task<LoyaltyResponseList<MerchantGiftGetAllGiftOutput>> GiftList(MerchantGiftGetAllGiftInput input)
        {
            if (string.IsNullOrWhiteSpace(input.CifCode))
            {
                CommonHelper.GetErrorValidation("902", "Cif code is required");
            }
            if (input.MaxResultCount <= 0)
            {
                input.MaxResultCount = 10;
            }
            if (input.SkipCount <= 0)
            {
                input.SkipCount = 0;
            }

            if (string.IsNullOrWhiteSpace(input.Sorting) || !(new List<string>() { "POPULAR", "NEW" }.Contains(input.Sorting.Trim().ToUpper())))
            {
                // Sorting ko được chỉ rõ, hoặc chứa giá trị sai mô tả, thì sẽ mặc định về POPULAR
                input.Sorting = "POPULAR";
            }
            // Đến đây sorting không bị null nên toupper now:
            input.Sorting = input.Sorting.ToUpper();

            var member = await CheckAndGetMemberInfo(new GetLinkIdMemberByCifCodeInput
            {
                CifCode = input.CifCode
            });

            var request = new MerchantGiftGetAllGiftInputDto()
            {
                FullGiftCategoryCodeFilter = input.CategoryCodeFilter,
                MemberCode = member.NationalId,
                MaxItem = 10,
                FromCointFilter = input.FromCointFilter,
                ToCoinFilter = input.ToCoinFilter,
                RegionCodeFilter = input.LocationCodeFilter,
                MaxResultCount = input.MaxResultCount ?? 10,
                SkipCount = input.SkipCount ?? 0,
                Filter = input.Keyword,
                BrandIdFilter = input.BrandIdFilter,
                Sorting = input.Sorting == "POPULAR" ? " UsedQuantity DESC " : " CreationTime DESC ",
                GiftCategoryChannelCode = input.GiftCategoryChannelCode
            };
            try
            {
                var result = await _linkIdLoyaltyService.GiftList(request);
                var items = result.Result.Items.Select(item => new MerchantGiftGetAllGiftOutput()
                {
                    Id = item.GiftInfor?.Id,
                    Code = item.GiftInfor?.Code,
                    CategoryCode = item.GiftInfor?.FullGiftCategoryCode,
                    Description = item.GiftInfor?.Introduce,
                    ImageLinks = (item.ImageLink == null || item.ImageLink.Count == 0) ? new List<MerchantGiftImageLinkShortDto>()
                        : item.ImageLink.Select(x => new MerchantGiftImageLinkShortDto()
                        {
                            FullLink = x.FullLink,
                            Link = x.Link,
                        }).ToList(),
                    InStock = item.GiftInfor?.RemainingQuantity,
                    Name = item.GiftInfor?.Name,
                    RequiredCoin = item.GiftInfor?.RequiredCoin,
                    ShortDescription = item.GiftInfor?.Introduce,
                    IsEgift = item.GiftInfor?.IsEGift,
                    //Office = item.GiftInfor?.Office,
                    //VendorHotline = item.GiftInfor?.VendorHotline,
                    Vendor = item.GiftInfor?.Vendor,
                    TotalWish = item.GiftInfor?.TotalWish ?? 0,
                    BrandName = item.GiftInfor?.BrandName,
                    ExpireDuration = item.GiftInfor?.ExpireDuration,
                }).ToList();

                return new LoyaltyResponseList<MerchantGiftGetAllGiftOutput>()
                {
                    Error = result.Error,
                    Result = new LoyaltyResponsResultDto<MerchantGiftGetAllGiftOutput>()
                    {
                        Items = items,
                        TotalCount = result.Result.TotalCount,
                    },
                    Success = result.Success,
                };
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                if (res.Code == "-1")
                {
                    CommonHelper.GetErrorValidation(GetCodeFromCode(res.Message), GetMessageFromCode(res.Message));
                }
                throw ex;
            }
        }

        public async Task<LoyaltyResponse<MerchantGiftTransactionDetailOutput>> TransactionDetail(MerchantGiftTransactionDetailInput input)
        {
            if (string.IsNullOrWhiteSpace(input.CifCode))
            {
                CommonHelper.GetErrorValidation("902", "Cif code is required");
            }
            if (string.IsNullOrWhiteSpace(input.TransactionCode))
            {
                CommonHelper.GetErrorValidation("941", "Transaction code is required");
            }

            var member = await CheckAndGetMemberInfo(new GetLinkIdMemberByCifCodeInput
            {
                CifCode = input.CifCode
            });

            var request = new MerchantGiftTransactionDetailInputDto()
            {
                OwnerCodeFilter = member.NationalId,
                GiftTransactionCode = input.TransactionCode,
            };
            try
            {
                var result = await _linkIdLoyaltyService.TransactionDetail(request);
                if (result.Result.Items == null || result.Result.Items.Count == 0)
                {
                    CommonHelper.GetErrorValidation("940", "Transaction code not exist");
                }
                var data = result.Result.Items[0];

                return new LoyaltyResponse<MerchantGiftTransactionDetailOutput>()
                {
                    Error = result.Error,
                    Result = new MerchantGiftTransactionDetailOutput()
                    {
                        EGift = data.EGift,
                        GiftTransaction = data.GiftTransaction,
                        ImageLinks = data.ImageLinks.Count == 0 ? new List<MerchantGiftImageLinkShortDto>()
                        : data.ImageLinks.Select(x => new MerchantGiftImageLinkShortDto()
                        {
                            FullLink = x.FullLink,
                            Link = x.Link,
                        }).ToList(),
                        VendorInfo = data.VendorInfo,
                    },
                    Success = result.Success,
                };
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                if (res.Code == "-1")
                {
                    CommonHelper.GetErrorValidation(GetCodeFromCode(res.Message), GetMessageFromCode(res.Message));
                }
                throw ex;
            }
        }

        public async Task<LoyaltyResponseList<MerchantGiftTransactionHistoryOutput>> TransactionHistory(MerchantGiftTransactionHistoryInput input)
        {
            if (input.MaxResultCount <= 0)
            {
                input.MaxResultCount = 10;
            }
            if (input.SkipCount <= 0)
            {
                input.SkipCount = 0;
            }
            if (string.IsNullOrWhiteSpace(input.CifCode))
            {
                CommonHelper.GetErrorValidation("902", "Cif code is required");
            }
            var member = await CheckAndGetMemberInfo(new GetLinkIdMemberByCifCodeInput
            {
                CifCode = input.CifCode
            });
            var request = new MerchantGiftTransactionDetailInputDto()
            {
                OwnerCodeFilter = member.NationalId,
                FromDateFilter = input.FromDateFilter,
                ToDateFilter = input.ToDateFilter,
                StatusFilter = input.StatusFilter,
                SkipCount = input.SkipCount ?? 0,
                MaxResultCount = input.MaxResultCount ?? 10
            };
            try
            {
                var result = await _linkIdLoyaltyService.TransactionHistory(request);
                var items = result.Result.Items.Select(data => new MerchantGiftTransactionHistoryOutput()
                {
                    EGift = data.EGift,
                    GiftTransaction = data.GiftTransaction,
                    ImageLinks = (data.ImageLinks == null || data.ImageLinks.Count == 0) ? new List<MerchantGiftImageLinkShortDto>()
                        : data.ImageLinks.Select(x => new MerchantGiftImageLinkShortDto()
                        {
                            FullLink = x.FullLink,
                            Link = x.Link,
                        }).ToList(),
                    VendorInfo = data.VendorInfo,
                }).ToList();

                return new LoyaltyResponseList<MerchantGiftTransactionHistoryOutput>()
                {
                    Error = result.Error,
                    Result = new LoyaltyResponsResultDto<MerchantGiftTransactionHistoryOutput>()
                    {
                        Items = items,
                        TotalCount = result.Result.TotalCount,
                    },
                    Success = result.Success,
                };
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                if (res.Code == "-1")
                {
                    CommonHelper.GetErrorValidation(GetCodeFromCode(res.Message), GetMessageFromCode(res.Message));
                }
                throw ex;
            }
        }

        private string GetCodeFromCode(string code)
        {
            switch (code)
            {
                case "MemberNotExitsOrNotActive":
                    return "MemberNotExist";
                default:
                    return code;
            }
        }

        private string GetMessageFromCode(string code)
        {
            _logger.LogInformation($"Message from code {code}");
            var codeCheck = GetCodeFromCode(code);
            switch (codeCheck)
            {
                case "MemberNotEnoughToken":
                    return "Member not enough token";
                case "MemberNotExist":
                    return "LinkID member not exist";
                case "OriginalOrderNotExist":
                    return "Original order code not exist";
                case "OriginalOrderHasBeenReverted":
                    return "Original order code has been reverted";
                case "DuplicateOrderCode":
                    return "Duplicate order code";
                case "DuplicateRevertCode":
                    return "Duplicate revert code";
                case "1023":
                    return "Gift not exist";
                case "1025":
                    return "Out of gift";
                default:
                    return "System error";
            }
        }

        private async Task<CheckAndGetMemberInfoOutput> CheckAndGetMemberInfo(GetLinkIdMemberByCifCodeInput input)
        {
            LoyaltyResponse<GetLinkIdMemberByCifCodeOutput> vpbMember;
            try
            {
                vpbMember = await _loyaltyMemberService.GetLinkIdMemberByCifCode(input);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                CommonHelper.GetErrorValidation(res.Code, res.Message);
                throw ex;
            }

            RewardGetMemberInfoResponse linkIdMember;
            try
            {
                linkIdMember = await _rewardMemberService.GetMemberInfo(vpbMember.Result.LinkID_MemberID);
                if (linkIdMember == null)
                {
                    CommonHelper.GetErrorValidation("750", "Member with this CIF code not exist");
                }
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    CommonHelper.GetErrorValidation(GetCodeFromCode(res.Code), GetMessageFromCode(res.Code));
                }
                throw ex;
            }

            return new CheckAndGetMemberInfoOutput
            {
                LinkID_MemberID = linkIdMember.Id,
                NationalId = linkIdMember.NationalId,
                LinkID_PhoneNumber = linkIdMember.PhoneNumber,
            };
        }

        public async Task<GetAllLocationManagementDto> GetAllLocationShip(MerchantGiftLocationShipInput input)
        {
            if (input.MaxResultCount <= 0)
            {
                input.MaxResultCount = 10;
            }
            if (input.SkipCount <= 0)
            {
                input.SkipCount = 0;
            }
            // LinkID đang xài location ship từ vendor urbox
            var vendorType = "Urbox";
            var req = new GetAllLocationInput()
            {
                CodeFilter = input.CodeFilter,
                IdFilter = input.IdFilter,
                LevelFilter = input.LevelFilter,
                MaxResultCount = input.MaxResultCount,
                NameFilter = input.NameFilter,
                ParentCodeFilter = input.ParentCodeFilter,
                SkipCount = input.SkipCount,
                Sorting = input.Sorting,
                VendorType = vendorType,
            };
            return await _linkIdLoyaltyService.GetAllLocationShip(req);
        }

        public async Task<ViewLocationByIdsOutput> ViewLocationShipByIds(MerchantGiftLocationShipByIdInput input)
        {
            var defaultValue = new List<int>() { 0 };
            var req = new ViewLocationByIds()
            {
                IdList = (input.IdList == null || input.IdList.Count == 0) ? defaultValue : input.IdList,
            };
            return await _linkIdLoyaltyService.ViewLocationShipByIds(req);
        }

        // For redeem
        private string genOrderCode(string orderCode, string memberCode)
        {
            return LoyaltyHelper.GenTransactionCode(orderCode + memberCode + DateTime.Now.Ticks);
        }

        public async Task<LoyaltyResponseList<MerchantGiftTransactionHistoryOutput>> TransactionHistoryForCardZone247(MerchantGiftTransactionHistoryInput input)
        {
            if (input.MaxResultCount <= 0)
            {
                input.MaxResultCount = 10;
            }
            if (input.SkipCount <= 0)
            {
                input.SkipCount = 0;
            }
            if (string.IsNullOrWhiteSpace(input.CifCode))
            {
                CommonHelper.GetErrorValidation("902", "Cif code is required");
            }
            var member = await CheckAndGetMemberInfo(new GetLinkIdMemberByCifCodeInput
            {
                CifCode = input.CifCode
            });
            var request = new MerchantGiftTransactionDetailInputDto()
            {
                OwnerCodeFilter = member.NationalId,
                FromDateFilter = input.FromDateFilter,
                ToDateFilter = input.ToDateFilter,
                StatusFilter = input.StatusFilter,
                SkipCount = input.SkipCount ?? 0,
                MaxResultCount = input.MaxResultCount ?? 10,
                Language = input.Language
            };
            try
            {
                var result = await _linkIdLoyaltyService.TransactionHistoryForCardZone247(request);
                var items = result.Result.Items.Select(data => new MerchantGiftTransactionHistoryOutput()
                {
                    Partner = "LynkiD",
                    EGift = data.EGift,
                    GiftTransaction = data.GiftTransaction,
                    ImageLinks = (data.ImageLinks == null || data.ImageLinks.Count == 0) ? new List<MerchantGiftImageLinkShortDto>()
                        : data.ImageLinks.Select(x => new MerchantGiftImageLinkShortDto()
                        {
                            FullLink = x.FullLink,
                            Link = x.Link,
                        }).ToList(),
                    VendorInfo = data.VendorInfo,
                    GiftUsageAddress = data.GiftUsageAddress,
                    BrandInfo = data.BrandInfo

                }).ToList();

                return new LoyaltyResponseList<MerchantGiftTransactionHistoryOutput>()
                {
                    Error = result.Error,
                    Result = new LoyaltyResponsResultDto<MerchantGiftTransactionHistoryOutput>()
                    {
                        Items = items,
                        TotalCount = result.Result.TotalCount,
                    },
                    Success = result.Success,
                };
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                if (res.Code == "-1")
                {
                    CommonHelper.GetErrorValidation(GetCodeFromCode(res.Message), GetMessageFromCode(res.Message));
                }
                throw ex;
            }
        }

        public async Task<LoyaltyResponseList<MerchantGiftTransactionHistoryOutput>> GetVouchersCardZone247(MerchantGiftTransactionHistoryInput input)
        {
            var result = new LoyaltyResponseList<MerchantGiftTransactionHistoryOutput>()
            {
                Error = null,
                Result = new LoyaltyResponsResultDto<MerchantGiftTransactionHistoryOutput>()
                {
                    Items = new List<MerchantGiftTransactionHistoryOutput>(),
                    TotalCount = 0,
                },
                Success = true
            };

            try
            {
                var resultLoyalty = await TransactionHistoryForCardZone247(input);
                if (resultLoyalty != null && resultLoyalty.Result != null && resultLoyalty.Result.TotalCount > 0)
                {
                    result.Result.Items.AddRange(resultLoyalty.Result.Items);
                    result.Result.TotalCount += resultLoyalty.Result.TotalCount;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"ERROR_GetVouchersCardZone247_LynkiD:{ex.Message}-{ex.StackTrace}");
            }
            return result;
        }

        public async Task<RedeemVoucherOutput> MarkUseEGift(MarkUseEGiftInput input)
        {
            decimal? topupAmount = null;
            var merchantId = Convert.ToInt32(_configuration.GetSection("RewardVPID" + ":MerchantId").Value);
            var isRedeemed = false;
            if (string.IsNullOrWhiteSpace(input.CifCode))
            {
                CommonHelper.GetErrorValidation("902", "Cif code is required");
            }
            var member = await CheckAndGetMemberInfo(new GetLinkIdMemberByCifCodeInput
            {
                CifCode = input.CifCode
            });

            if (member == null)
            {
                CommonHelper.GetErrorValidation("902", "Cannot find member");
            }

            var orderCode = LoyaltyHelper.GenTransactionCode(input.EGiftCode + member.NationalId + DateTime.Now.Ticks);
            try
            {
                var resultCheckEvoucher = await _linkIdLoyaltyService.CheckEVoucherTopup(new CheckVoucherInput()
                {
                    EGiftCode = input.EGiftCode,
                    MemberCode = member.NationalId
                });

                if (resultCheckEvoucher.Result != null && resultCheckEvoucher.Success && resultCheckEvoucher.Result.ErrorCode == "0")
                {
                    orderCode = resultCheckEvoucher.Result.GiftRedeemTransactionCode;
                    isRedeemed = true;
                }
                else
                {
                    _logger.LogInformation($"MarkUseEGift___CheckEVoucherTopup:{JsonConvert.SerializeObject(resultCheckEvoucher)}");
                    throw new Exception("Lỗi Check EGift");
                }

                var useEVoucherInput = new UseEVoucherTopupInput()
                {
                    EGiftCode = input.EGiftCode,
                    MemberCode = member.NationalId,
                    TransactionCode = orderCode
                };
                var result = await _linkIdLoyaltyService.UseEVoucherTopup(useEVoucherInput);

                if (result.Success && result.Result.ErrorCode == "0")
                {
                    topupAmount = result.Result.TopupAmount ?? 0;
                    try
                    {
                        var resultRedeemReward = await _rewardGiftRedeemTransactionService.CreateTransForTopupVoucher(new RewardNapEvoucherInput()
                        {
                            ECode = input.EGiftCode,
                            OrderCode = orderCode,
                            MerchantId = merchantId,
                            NationalId = useEVoucherInput.MemberCode,
                            RequestDate = DateTime.UtcNow,
                            TokenAmount = topupAmount.Value
                        });
                        if (resultRedeemReward == null || resultRedeemReward.Result != 200)
                        {
                            _logger.LogError(" >> Error when call to Rewardnetwork to topup evoucher >> " + useEVoucherInput.MemberCode + " - " + input.EGiftCode + "; Error  = " + JsonConvert.SerializeObject(resultRedeemReward));
                            await _linkIdLoyaltyService.RevertEVoucherCode(new RevertEVoucherInput()
                            {
                                MemberCode = useEVoucherInput.MemberCode,
                                TransactionCode = "REVERT_" + orderCode,
                                EGiftCode = input.EGiftCode,
                                OriginalTransactionCode = orderCode,
                                IsRedeemed = isRedeemed
                            });

                            result = new RedeemVoucherOutput()
                            {
                                Success = false,
                                Result = new RedeemVoucherOutputResult()
                                {
                                    Count = 0,
                                    ErrorCode = "13",
                                    ErrorMessage = "Lỗi xảy ra ở tổng đài nạp điểm, vui lòng thử lại sau nhé"
                                }
                            };
                            return result;
                        }
                        else
                        {
                            var successRnRes = resultRedeemReward;
                            result.Result.ExpireDate = successRnRes.Items.ExpiryDate;

                            // Khi thành công thì sẽ call api bắn noti cho các app đối tác ở đây
                        }
                    }
                    catch (Exception e)
                    {
                        if (e.GetType() == typeof(RewardException))
                        {
                            var res = await _exceptionReponseService.GetExceptionRewardReponse(e);
                            _logger.LogError("Exception occurs when call to RN >> " + JsonConvert.SerializeObject(res));
                        }

                        throw e;
                    }
                }
                else
                {
                    await _linkIdLoyaltyService.RevertEVoucherCode(new RevertEVoucherInput()
                    {
                        MemberCode = useEVoucherInput.MemberCode,
                        TransactionCode = "REVERT_" + orderCode,
                        EGiftCode = input.EGiftCode,
                        OriginalTransactionCode = orderCode,
                        IsRedeemed = isRedeemed
                    });
                    _logger.LogError(">> Issue calling Loyalty API: Error When Mark EVoucher as USED >> " + useEVoucherInput.MemberCode + " - " + input.EGiftCode);
                }

                result.Result.TransactionCode = orderCode;
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(" Error when doing topup for evoucher - >> " + member.NationalId + " - " + input.EGiftCode);
                await _linkIdLoyaltyService.RevertEVoucherCode(new RevertEVoucherInput()
                {
                    MemberCode = member.NationalId,
                    TransactionCode = "REVERT_" + orderCode,
                    EGiftCode = input.EGiftCode,
                    OriginalTransactionCode = orderCode,
                    IsRedeemed = isRedeemed
                });

                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    _logger.LogError("Exception occurs when call to RN >> " + JsonConvert.SerializeObject(res));
                    throw ex;
                }
                var result = new RedeemVoucherOutput()
                {
                    Success = false,
                    Result = new RedeemVoucherOutputResult()
                    {
                        Count = 0,
                        ErrorCode = "13",
                        ErrorMessage = "Lỗi xảy ra ở tổng đài nạp điểm, vui lòng thử lại sau nhé"
                    }
                };
                return result;
            }
        }

        public async Task<AdminCreateRedeemTransactionResponse> AdminCreateRedeemTransaction(AdminCreateRedeemTransactionRequest input)
        {
            if (string.IsNullOrWhiteSpace(input.Cif))
            {
                CommonHelper.GetErrorValidation("902", "Cif code is required");
            }
            if (string.IsNullOrEmpty(input.GiftCode))
            {
                CommonHelper.GetErrorValidation("902", "GiftCode is required");
            }

            if (string.IsNullOrEmpty(input.GiftName))
            {
                CommonHelper.GetErrorValidation("902", "GiftName is required");
            }

            if (string.IsNullOrEmpty(input.OfferType))
            {
                CommonHelper.GetErrorValidation("902", "OfferType is required");
            }

            var member = await CheckAndGetMemberInfo(new GetLinkIdMemberByCifCodeInput
            {
                CifCode = input.Cif
            });

            var orderCode = genOrderCode(input.GiftCode, member.NationalId);
            var request = new AdminCreateRedeemTransactionRequest()
            {
                GiftCode = input.GiftCode,
                MemberCode = member.NationalId,
                Quantity = input.Quantity.Value,
                TotalAmount = input.TotalAmount,
                Date = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                TransactionCode = orderCode,
                Cif = input.Cif,
                GiftName = input.GiftName,
                OfferType = input.OfferType
            };
            try
            {
                var result = await _linkIdLoyaltyService.AdminCreateRedeemTransaction(request);
                return result;
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    CommonHelper.GetErrorValidation(CommonHelper.GetCodeRedeemFromCode(res.Code), CommonHelper.GetMessageRedeemFromCode(res.Code));
                    throw ex;
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    _logger.LogInformation($"Error message from code {res.Message}");
                    if (res.Code == "-1")
                    {
                        CommonHelper.GetErrorValidation(CommonHelper.GetCodeRedeemFromCode(res.Message), CommonHelper.GetMessageRedeemFromCode(res.Message));
                    }
                    else
                    {
                        CommonHelper.GetErrorValidation(CommonHelper.GetCodeRedeemFromCode(res.Code), CommonHelper.GetMessageRedeemFromCode(res.Code));
                    }
                    throw ex;
                }
            }
        }

        public async Task<AdminGiveGiftToMemberResponse> AdminGiveGiftToMember(AdminGiveGiftToMemberRequest input)
        {
            var member = new CheckAndGetMemberInfoOutput();
            try
            {
                member = await CheckAndGetMemberInfo(new GetLinkIdMemberByCifCodeInput
                {
                    CifCode = input.Cif
                });
            }
            catch (Exception ex)
            {
                return new AdminGiveGiftToMemberResponse
                { IsSuccess = false, Exception = "NotLinkedYet" };
            }
            // Get GiftDetail by GiftId

            var giftDetailRequest = new MerchantGiftGetDetailInput()
            {
                GiftId = input.GiftId,
                CifCode = input.Cif
            };
            var giftDetail = await this.GiftDetail(giftDetailRequest);
            var giftInfo = giftDetail.Result;

            var orderCode = genOrderCode(giftInfo.Code, member.NationalId);
            var request = new AdminCreateRedeemTransactionRequest()
            {
                GiftCode = giftInfo.Code,
                MemberCode = member.NationalId,
                Quantity = input.Quantity,
                TotalAmount = giftInfo.RequiredCoin.Value * input.Quantity,
                Date = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                TransactionCode = orderCode,
                Cif = input.Cif,
                RedeemSource = input.RedeemSource
            };
            try
            {
                var result = await _linkIdLoyaltyService.AdminGiveGiftToMember(request);
                return new AdminGiveGiftToMemberResponse { IsSuccess = result.IsSuccess, TransactionCode = result.TransactionCode, Exception = result.Exception };
            }
            catch (Exception ex)
            {
                return new AdminGiveGiftToMemberResponse
                { IsSuccess = false, Exception = ex.Message };
            }
        }
    }
}
