﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.Service.Constants
{
    public class LoyaltyApiUrl
    {
        public const string CREATE_SECONDARY_CUSTOMERS = "services/app/SecondaryCustomers/CreateOrEdit";
        public const string CHECK_CIF_CODE = "services/app/SecondaryCustomers/CheckCifCode";
        public const string GET_CONTACT_LIST_FROM_ID_CARD = "services/app/SecondaryCustomers/GetContactListFromIdCard";
        public const string CHECK_CIF_CODE_PHASE1 = "services/app/SecondaryCustomers/CheckCifCode_PHASE1";
        public const string RECEIVED_CIF_CODE = "services/app/SecondaryCustomers/ReceivedCifCode";
        public const string GET_TRANSACTION_HISTORY = "services/app/Transactions/GetTransactionHistoryForVPO";
        public const string GET_LINID_MEMBER_ID_BY_CIF_CODE = "services/app/SecondaryCustomers/GetLinkIdMemberIdByCifCode";

        public const string GETALL_GIFT_CATEGORY = "services/app/GiftCategory/GetAll";
        public const string GetAllGiftCategoryWithoutMemberCode = "services/app/GiftCategory/GetAllWithoutMemberCode";
        public const string GIFT_INFORS_GETAll_FOR_CATEGORY = "services/app/GiftInfors/GetAllForCategory";
        public const string GIFT_GROUPS_GETAll_WITH_IMAGE = "services/app/GiftGroups/GetAllWithImage";
        public const string GIFT_GROUPS_GETAll_WITH_IMAGE_BY_MEMBER = "services/app/GiftGroups/GetAllWithImageByMemberCode";
        public const string GIFT_INFORS_GETAll_MEMBER_CODE = "services/app/GiftInfors/GetAllByMemberCode";
        public const string GIFT_INFORS_GET_GIFT_MEMBER_CODE = "services/app/GiftInfors/GetGiftByMemberCode";
        public const string GIFT_INFORS_GET_GIFT_CATEGORIES_MEMBER_CODE = "services/app/GiftInfors/GetAllGiftCategoriesAndInfo";
        public const string GIFT_INFORS_GET_GIFT_CATEGORIES_MEMBER_CODE_V1 = "services/app/GiftInfors/GetAllGiftCategoriesAndInfo_V1";
        public const string GIFT_INFORS_GET_GIFT_ALL_INFORS_MEMBER_CODE = "services/app/GiftInfors/GetAllInfors";
        public const string GIFT_INFORS_GET_GIFT_BY_ID_AND_RELATED_GIFT_MEMBER_CODE = "services/app/GiftInfors/GetByIdAndRelatedGift";

        public const string GIFT_INFORS_GET_ALL_EFFECTIVE_CATEGORY = "services/app/GiftInfors/GetAllEffectiveCategory";
        public const string GIFT_INFORS_GET_ALL_EFFECTIVE_CATEGORY_V1 = "services/app/GiftInfors/GetAllEffectiveCategory_v1";
        public const string GIFT_INFORS_WISH_LIST_BY_MEMBER = "services/app/GiftInfors/GetWishlistByMember";
        public const string GIFT_INFORS_UPDATE_WISH_LIST = "services/app/GiftInfors/UpdateWishlist";

        public const string NOTIFICATION_HISTORY_GET_ALL_BY_MEMBER_CODE = "services/app/NotificationHistory/GetAll";
        public const string NOTIFICATION_HISTORY_CHANGE_STATUS = "services/app/NotificationHistory/ChangeStatus";
        public const string NOTIFICATION_HISTORY_CHANGE_NOTIFICATION_COUNT = "services/app/NotificationHistory/ChangeNotificationCount";
        public const string NOTIFICATION_HISTORY_DELETE = "services/app/NotificationHistory/Delete";
        public const string NOTIFICATION_HISTORY_HARD_DELETE = "services/app/NotificationHistory/HardDelete";
        public const string MEMBER_UPDATE_PHONE_NUMBER = "services/app/SecondaryCustomers/UpdatePhoneNumber";



        public const string USEPOINT_PRECALCULATEPOINT = "services/app/UsePoint/PreCalculatePoint";

        public const string ORDER_PURCHASEAGENT = "services/app/Order/PurchaseAgent";
        public const string ORDER_USEPOINT = "services/app/Order/UsePoint";

        public const string REWARDS_INPUTACTION = "services/app/Rewards/InputAction";

        public const string GIFTTRANSACTION_CREATEREDEEMTRANSACTION = "services/app/GiftTransactions/CreateRedeemTransaction";
        public const string GIFTTRANSACTION_GETALL_WITH_EGIFT = "services/app/GiftTransactions/GetAllWithEGift";

        public const string GIFTTRANSACTION_USER_VERIFYING = "services/app/GiftTransactions/UserVerifying";

        public const string GIFTTRANSACTION_GIFT_TRANSFER = "services/app/GiftTransactions/GiftTransfer";

        public const string ARTICLE_GETALL = "services/app/Article/GetAll";

        public const string GET_ARTICLE_BY_ID = "services/app/Article/GetArticleByIdAndRelatedNews";
        public const string GET_ALL_ARTICLE_BY_MEMBERCODE = "services/app/Article/GetAllArticleByMemberCode";

        public const string GET_ALL_ARTICLE_AND_RELATED_NEWS = "services/app/Article/GetAllArticleAndRelatedNews";

        public const string GET_ARTICLE_BY_ID_BY_MEMBERCODE = "services/app/Article/GetArticleByIdByMemberCode";
        
        public const string GET_LIST_BY_GROUP = "services/app/Quest/GetListByGroup";

        public const string GET_DETAIL = "services/app/Quest/GetDetail";

        public const string GET_LIST_QUEST_BY_ACTIVITY = "services/app/Quest/GetListQuestByActivity";

        public const string QUEST_JOIN = "services/app/Quest/Join";

        public const string QUEST_VIEW_ACTUAL = "services/app/Quest/ViewActual";

        public const string QUEST_CLAIM = "services/app/Quest/Claim";

        public const string GET_PROFILE_BY_CODE = "services/app/SecondaryCustomers/GetProfileByCode";

        public const string GET_MEMBER_INFO_BY_CODE = "services/app/SecondaryCustomers/GetMemberInfoByCode";

        public const string VERIFY_REFERRAL_CODE = "services/app/ReferralReward/VerifyReferralCode";

        public const string QUEST_GET_ALL = "services/app/Quest/GetAll";

        public const string QUEST_GET_LIST_BY_MEMBERCODE = "services/app/Quest/GetListByMemberCode";


        public const string QUEST_STAGE_JOIN = "services/app/QuestStage/Join";
        public const string QUEST_STAGE_CLAIM = "services/app/QuestStage/Claim";
        public const string QUEST_STAGE_OR_LEVEL_CLAIM = "services/app/QuestStage/ClaimQuestStageOrLevel";
        public const string QUEST_STAGE_GET_ALL = "services/app/QuestStage/GetAll";
        public const string QUEST_STAGE_VIEW_ACTUAL = "services/app/QuestStage/ViewActual";
        public const string QUEST_STAGE_GET_LIST_BY_MEMBERCODE = "services/app/QuestStage/GetListQuestAndQuestStageByMemberCode";
        public const string GET_QUEST_STAGE_DETAIL = "services/app/QuestStage/GetQuestStageDetail";
        public const string GET_LIST_QUEST_STAGE_JOINED_BY_MEMBER = "services/app/QuestStage/GetListQuestStageJoinedByMember";
        public const string QUEST_OR_QUEST_STAGE_GET_LIST_BY_MEMBERCODE = "services/app/QuestStage/GetListQuestAndQuestStageByMemberCode";
        public const string QUEST_STAGE_GET_DETAIL = "services/app/QuestStage/GetDetail";


        public const string FAQ_GET_ALL = "services/app/FAQ/GetAll";

        public const string THIRD_PARTY_POINT_VIEW = "services/app/ThirdPartyPoints/View";

        public const string THIRD_PARTY_VERIFY_NATIONAL_ID = "services/app/ThirdPartyVerification/VerifyNationalId";

        public const string THIRD_PARTY_VERIFY_OTP = "services/app/ThirdPartyVerification/VerifyOTP";

        public const string THIRD_PARTY_POINT_EXCHANGE = "services/app/ThirdPartyPoints/Exchange";

        public const string THIRD_PARTY_REVERT_POINT = "services/app/ThirdPartyPoints/Revert";
        public const string CHECK_REFERRAL_CODE_EXISTANCE = "services/app/ReferralReward/CheckReferralCodeExistance";

        public const string GIFT_INFORS_GET_ALL_BY_MEMBER_CODE = "services/app/GiftCategory/GetAllByMemberCode";

        public const string GIFT_INFORS_GET_ALL_FOR_CATEGORY_BY_MEMBER_CODE = "services/app/GiftInfors/GetAllForCategoryByMemberCode";

        public const string UPDATE_MEMBER_BALANCE = "services/app/Adjust/UpdateMemberBalance";

        public const string CREATE_OPERATION_LOG = "services/app/AuditLog/CreateOperationLog";

        public const string UPDATE_NOTIFICATION_SETTING = "services/app/SecondaryCustomers/UpdateNotificationSetting";
        
        public const string EGIFT_INFORS_UPDATE_GIFT_STATUS = "services/app/EGiftInfors/UpdateGiftStatus";

        public const string GET_LANGUAGES_TEXT = "services/app/Language/GetListLanguageTexts";
        public const string LOCATION_GET_ALL = "services/app/LocationManagement/GetAllLocation";
        public const string LOCATION_GET_ALL_NO_PAGING = "services/app/LocationManagement/GetAllLocationNoPaging";
        public const string LOCATION_VIEW_BY_IDS = "services/app/LocationManagement/ViewLocationByIds";
        public const string UPDATE_EGIFT_CODE = "services/app/EGiftInfors/UpdateEgiftCode";
        public const string VERIFY_CREATE_REDEEM = "services/app/GiftTransactions/VerifyCreateRedeemForVbo";

        //New
        public const string VIEW_POINT_WITH_GRANTTYPE = "services/app/SecondaryCustomers/ViewPointWithGrantType";
        public const string MEMBER_VERIFY_CONNECT_MERCHANT = "services/app/SecondaryCustomers/VerifyConfirmConnectForConnect";
        public const string MEMBER_CONFIRM_CONNECT_MERCHANT = "services/app/SecondaryCustomers/ConfirmConnect";
        public const string MEMBER_REMOVE_CONNECT_MERCHANT = "services/app/SecondaryCustomers/RemoveConnect";
        public const string MEMBER_CHECK_CONNECT_HISTORY = "services/app/SecondaryCustomers/CheckConnectHistory";
        public const string CreateUnConfirmConnection = "member/CreateUnConfirmConnection";
        public const string CreateConfirmedConnection = "member/CreateConfirmedConnection";
        public const string GetMemberInfoByCif = "member/GetMemberInfoByCif";
        public const string UpdateMemberFromAdapter = "member/UpdateMemberData";
        public const string AdminCreateRedeemTransaction = "GiftTransaction/AdminCreateRedeemTransaction";
        public const string GetAllBirthDayTransaction = "Transaction/GetAllBirthDayTransaction";
        // Bill payment
        public const string BILL_PAYMENT_CHECK_BALANCE = "services/app/BillPayment/CheckBalance";
        public const string BILL_PAYMENT_CONFIRM_USING_TOKEN = "services/app/BillPayment/ConfirmUsingToken";
        public const string BILL_PAYMENT_REVERT_TOKEN = "services/app/BillPayment/RevertToken";
        // Merchant gift
        public const string MERCHANT_GIFT_CREATE_REDEEM = "services/app/GiftTransactions/CreateRedeemTransactionForVpo";
        public const string MERCHANT_GIFT_GET_ALL_CATEGORY = "services/app/GiftCategory/GetAllByMemberCode";
        public const string MERCHANT_GIFT_GET_ALL_CATEGORY_V1 = "services/app/GiftCategory/GetAllByMemberCode_v1";
        public const string MERCHANT_GIFT_GET_ALL_LOCATION = "services/app/Regions/GetAll";
        public const string MERCHANT_GIFT_GET_GIFT_DETAIL = "services/app/GiftInfors/GetByIdAndRelatedGift";
        public const string MERCHANT_GIFT_GET_GIFT_LIST = "services/app/GiftInfors/GetAllEffectiveCategory_v1";
        public const string MERCHANT_GIFT_GET_ORDER_DETAIL = "services/app/GiftTransactions/GetAllWithEGiftForVpo";
        public const string MERCHANT_GIFT_GET_ORDER_LIST = "services/app/GiftTransactions/GetAllWithEGiftForVpo";
        public const string MERCHANT_GIFT_GET_REDEEM_CACHE = "services/app/GiftTransactions/GetRedeemTransactionForVpo";

        public const string VERIFY_OR_CREATE_REDEEM_ORDER = "services/app/GiftTransactions/VerifyAndCreateRedeemOrder";
        public const string UPDATE_ERROR_WHEN_CREATE_REDEEM = "services/app/GiftTransactions/HandleErrorWhenCreateRedeemPayment";
        public const string GET_MERCHANT_ID_FROM_GIFT_CODE = "services/app/GiftInfors/GetMerchantIdFromGiftCode";
        public const string GetAllWithEGift = "services/app/GiftTransactions/GetAllWithEGift";
        public const string GetGiftTransForNEOBiz = "services/app/GiftTransactions/GetGiftTransForNEOBiz";
        public const string GetAllWithEGiftForCardZone247 = "services/app/GiftTransactions/GetAllWithEGiftForCardZone247";
        public const string AdminGiveGiftToMember = "services/app/GiftTransactions/AdminGiveGiftToMember";

        // For loyalty InputReference
        public const string CREATE_INPUT_REFERENCE = "services/app/InputReference/CreateOrEdit";
        // GiftGroup
        public const string GET_GIFT_BY_GROUP_CHANNEL = "services/app/GiftInfors/GetGiftByGroupChannel";
        public const string REDEEM_GIFT_BY_GROUP_CODE = "services/app/GiftTransactions/CreateRedeemTxWithGroupCode";
        public const string REDEEM_MULTI_GIFT_BY_GROUP_CODE = "services/app/GiftTransactions/CreateRedeemMultiTxWithGroupCode";
        public const string VERIFY_AND_CREATE_REDEEM_ORDER = "services/app/GiftTransactions/VerifyAndCreateRedeemOrder";
        public const string REDEEM_GET_GIFT_REDEEMED = "services/app/GiftTransactions/GetGiftRedeemTransWithGiftGroup";
        public const string REDEEM_CHECK_GIFT_BRAND = "services/app/GiftInfors/ValidGiftByBrand";

        public const string GET_GIFT_WITHOUT_MEMBER_CODE = "services/app/GiftInfors/GetQuaWithoutMemberCode";
        public const string GET_GIFT_DETAIL_WITHOUT_MEMBER_CODE = "services/app/GiftInfors/GetByIdAndRelatedGiftWithoutMemberCode";

        public const string EGIFT_INFORS_USE_VOUCHER_TOPUP = "services/app/EGiftInfors/UseEGift";
        public const string EGIFT_INFORS_REVERT_VOUCHER_TOPUP = "services/app/EGiftInfors/RevertEGift";
        public const string EGIFT_INFORS_CHECK_VOUCHER_TOPUP = "services/app/EGiftInfors/CheckEGift";
    }
}
