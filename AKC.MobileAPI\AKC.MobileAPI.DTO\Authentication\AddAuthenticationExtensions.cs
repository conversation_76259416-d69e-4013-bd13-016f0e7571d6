﻿//using System;
//using System.Collections.Generic;
//using System.Text;

//namespace AKC.MobileAPI.DTO.Common
//{
//    public static IServiceCollection AddAuthentication(this IServiceCollection services,
//    byte[] signingKey)
//    {
//        services.AddAuthentication(authOptions =>
//        {
//            authOptions.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
//            authOptions.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
//        })
//            .AddJwtBearer(jwtOptions =>
//            {
//                jwtOptions.SaveToken = true;
//                jwtOptions.TokenValidationParameters = new TokenValidationParameters
//                {
//                    ValidateAudience = false,
//                    ValidateIssuer = false,
//                    ValidateIssuerSigningKey = true,
//                    IssuerSigningKey = new SymmetricSecurityKey(signingKey),
//                    ValidateLifetime = true,
//                    LifetimeValidator = LifetimeValidator
//                };
//            });

//        return services;
//    }
//}
