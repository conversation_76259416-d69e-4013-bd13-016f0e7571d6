﻿using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Reward;
using AKC.RabbitMQ;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyGiftTransactionsService : BaseLoyaltyService, ILoyaltyGiftTransactionsService
    {
        private IRewardMemberService _rewardMemberService;
        private IRewardGiftRedeemTransactionService _rewardGiftRedeemTransactionService;
        private readonly IRabbitMQManager _rabbitMQManager;
        private readonly RabbitExchange _rabbitExchange;

        public LoyaltyGiftTransactionsService(
            IConfiguration configuration,
            IDistributedCache memoryCache,
            IRewardMemberService rewardMemberService,
            IRewardGiftRedeemTransactionService rewardGiftRedeemTransactionService,
            IRabbitMQManager rabbitMQManager,
            RabbitExchange rabbitExchange
        ) : base(configuration, memoryCache)
        {
            _rewardMemberService = rewardMemberService;
            _rewardGiftRedeemTransactionService = rewardGiftRedeemTransactionService;
            _rabbitMQManager = rabbitMQManager;
            _rabbitExchange = rabbitExchange;
        }

        public async Task<LoyaltyCreateRedeemTransactionOutput> CreateRedeemTransaction(LoyaltyCreateRedeemTransactionInput input, HttpContext context)
        {
            var merchantId = Convert.ToInt32(_configuration.GetSection("Reward" + MerchantNameConfig.VPID + ":MerchantId").Value);
            var orderCode = genOrderCode(input.GiftCode, input.MemberCode);
            var cts = new CancellationTokenSource();
            var resultBalance = await _rewardMemberService.GetBalanceMember(input.MemberCode);
            if (resultBalance.TokenBalance < input.TotalAmount)
            {
                //throw new ArgumentException("Token Balance is not enough to make this transaction");
                return new LoyaltyCreateRedeemTransactionOutput()
                {
                    Error = "BalanceNotEnough",
                    Result = new LoyaltyCreateRedeemTransaction()
                    {
                        IsNotEnoughBalance = true,
                        SuccessedRedeem = false,
                        Messages = "Token Balance is not enough to make this transaction"
                    }
                };
            }
            var requestRedeem = new RewardCreateGiftRedeemTransactionRequest()
            {
                OrderCode = orderCode,
                MerchantId = merchantId,
                NationalId = input.MemberCode,
                TotalRequestedAmount = input.TotalAmount,
            };
            try
            {
                var resultRedeemReward = await _rewardGiftRedeemTransactionService.CreateRedeem(requestRedeem);
                var request = new LoyaltyCreateRedeemTransactionDto()
                {
                    Date = input.Date,
                    Description = input.Description,
                    GiftCode = input.GiftCode,
                    MemberCode = input.MemberCode,
                    Quantity = input.Quantity,
                    TotalAmount = input.TotalAmount,
                    TransactionCode = orderCode,
                };
                var result = await PostLoyaltyAsync<LoyaltyCreateRedeemTransactionOutput>(LoyaltyApiUrl.GIFTTRANSACTION_CREATEREDEEMTRANSACTION, request, context);
                if (!string.IsNullOrWhiteSpace(result.Result.Exception) || !result.Result.SuccessedRedeem)
                {
                    var requestRevert = new RewardRevertGiftRedeemTransactionRequest()
                    {
                        MerchantId = merchantId,
                        NationalId = input.MemberCode,
                        OrderCode = orderCode,
                        TokenAmount = input.TotalAmount,
                    };

                    if (!result.Result.Timeout)
                    {
                        await retryRevertToken(requestRevert);
                    }
                    //if (result.Result.SuccessedRedeem)
                    //{
                    //    throw new ArgumentException(result.Result.Exception);
                    //}
                }
                return result;
            }
            catch (WebException ex)
            {
                var requestRevert = new RewardRevertGiftRedeemTransactionRequest()
                {
                    MerchantId = merchantId,
                    NationalId = input.MemberCode,
                    OrderCode = orderCode,
                    TokenAmount = input.TotalAmount,
                };
                await retryRevertToken(requestRevert);
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                var requestRevert = new RewardRevertGiftRedeemTransactionRequest()
                {
                    MerchantId = merchantId,
                    NationalId = input.MemberCode,
                    OrderCode = orderCode,
                    TokenAmount = input.TotalAmount,
                };
                await retryRevertToken(requestRevert);
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with: ", ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
            catch (Exception ex)
            {
                var requestRevert = new RewardRevertGiftRedeemTransactionRequest()
                {
                    MerchantId = merchantId,
                    NationalId = input.MemberCode,
                    OrderCode = orderCode,
                    TokenAmount = input.TotalAmount,
                };
                await retryRevertToken(requestRevert);
                throw ex;
            }

        }

        public async Task<LoyaltyGetAllWithEGiftOutput> GetAllWithEGift(LoyaltyGetAllWithEGiftInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGetAllWithEGiftOutput>(LoyaltyApiUrl.GIFTTRANSACTION_GETALL_WITH_EGIFT, input);
        }

        private async Task<Boolean> revertToken(RewardRevertGiftRedeemTransactionRequest request)
        {
            try
            {
                await _rewardGiftRedeemTransactionService.RevertRedeem(request);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task retryRevertToken(RewardRevertGiftRedeemTransactionRequest request)
        {
            var retryNum = 3;
            while (retryNum != 0)
            {
                var result = await revertToken(request);
                if (result)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
            //var rejectEvent = new GiftRedemptionRejectedEvent
            //{
            //    MemberCode = request.NationalId,
            //    MerchantId = request.MerchantId,
            //    OrderCode = request.OrderCode,
            //    TokenAmount = request.TokenAmount,
            //};
           // _rabbitMQManager.Publish(rejectEvent, _rabbitExchange.EventExchange, ExchangeTypes.Topic, CommonConstants.GiftRedemptionRejectRouteKey);
            //return Task.FromResult(0);
        }

        private string genOrderCode(string orderCode, string memberCode)
        {
            return LoyaltyHelper.GenTransactionCode(orderCode + memberCode + DateTime.Now.Ticks);
        }

        public async Task<GetMemberCodeOutput> UserVerifying(GetMemberCodeInput input)
        {
            return await PostLoyaltyAsync<GetMemberCodeOutput>(LoyaltyApiUrl.GIFTTRANSACTION_USER_VERIFYING, input);
        }
        public async Task<GiftTransferOutput> GiftTransfer(UpdateGiftTransactionsForTransferInput input)
        {
            return await PostLoyaltyAsync<GiftTransferOutput>(LoyaltyApiUrl.GIFTTRANSACTION_GIFT_TRANSFER, input);
        }

        public async Task<LoyaltyCreateRedeemTransactionOutput> PostLoyaltyRedeem(LoyaltyCreateRedeemTransactionDto input, HttpContext context)
        {
            return await PostLoyaltyAsync<LoyaltyCreateRedeemTransactionOutput>(LoyaltyApiUrl.GIFTTRANSACTION_CREATEREDEEMTRANSACTION, input, context);
        }
    }
}
