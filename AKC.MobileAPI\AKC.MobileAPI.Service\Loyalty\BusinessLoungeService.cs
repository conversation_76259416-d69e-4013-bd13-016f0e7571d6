﻿using System.Threading.Tasks;
using System;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.LoyaltyVpbank;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using AKC.MobileAPI.DTO.Loyalty.BusinessLounge;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class BusinessLoungeService : BaseLoyaltyUtilService, IBusinessLoungeService
    {
        public BusinessLoungeService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }
        public async Task<CheckCifAirportLoungeResponse> CheckCif(CheckCifAirportLoungeRequest input)
        {
            return await PostLoyaltyAsync<CheckCifAirportLoungeResponse>(LoyaltyApiUtilsUrl.BUSINESS_LOUNGE_CHECK_CIF, input);
        }

        public async Task<GetListAirportLoungeResponse> GetListAirportLoungeByCif(GetListAirportLoungeRequest input)
        {
            return await PostLoyaltyAsync<GetListAirportLoungeResponse>(LoyaltyApiUtilsUrl.BUSINESS_LOUNGE_GET_LIST, input);
        }
    }
}