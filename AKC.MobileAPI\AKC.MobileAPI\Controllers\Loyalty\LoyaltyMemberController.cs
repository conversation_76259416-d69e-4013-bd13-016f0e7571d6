﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.DTO.Loyalty.Order;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/SecondaryCustomer")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyMemberController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyMemberService _memberService;
        private readonly IExceptionReponseService _exceptionReponseService;
        public LoyaltyMemberController(
            ILogger<LoyaltyOrderController> logger,
            ILoyaltyMemberService memberService,
            IExceptionReponseService exceptionReponseService)
        {
            _logger = logger;
            _memberService = memberService;
            _exceptionReponseService = exceptionReponseService;
        }

        [HttpPost]
        [Route("GetContactListFromIdCard")]
        public async Task<ActionResult<LoyaltyResponse<GetContactListFromIdCardOutput>>> GetContactListFromIdCard(GetContactListFromIdCardInput input)
        {
            try
            {
                var result = await _memberService.GetContactListFromIdCard(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CheckCifCode Error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }
        
        [HttpPost]
        [Route("CheckCifCode")]
        public async Task<ActionResult<LoyaltyResponse<CheckCifCodeOutput>>> CheckCifCode(CheckCifCodeInput input)
        {
            try
            {
                var result = await _memberService.CheckCifCode(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CheckCifCode Error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("CheckCifCodeV1")]
        public async Task<ActionResult<LoyaltyResponse<string>>> CheckCifCodeV1(CheckCifCodeInput input)
        {
            try
            {
                var result = await _memberService.CheckCifCode_PHASE1(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CheckCifCode Error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return StatusCode(400, res);
                }
            }
        }

        //[HttpPost]
        //[Route("ReceivedCifCode")]
        //public async Task<ActionResult<LoyaltyResponse<string>>> ReceivedCifCode(ReceivedCifCodeInput input)
        //{
        //    try
        //    {
        //        var result = await _memberService.ReceivedCifCode(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //        _logger.LogError(ex, "ReceivedCifCode Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        //[HttpGet]
        //[Route("Transaction/GetTransactionPointHistory")]
        //public async Task<ActionResult<LoyaltyResponse<GetTransactionPointHistoryOutput>>> GetTransactionPointHistory([FromQuery] GetTransactionPointHistoryInput input)
        //{
        //    try
        //    {
        //        var result = await _memberService.GetTransactionPointHistory(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //        _logger.LogError(ex, "GetTransactionPointHistory Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}
    }
}
