﻿//using AKC.MobileAPI.DTO.Loyalty.Gift;
//using AKC.MobileAPI.Service.Abstract.Loyalty.Gift;
//using AKC.MobileAPI.Service.Exceptions;
//using Microsoft.AspNetCore.Authorization;
//using Microsoft.AspNetCore.Mvc;
//using Microsoft.Extensions.Logging;
//using Newtonsoft.Json;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading.Tasks;

//namespace AKC.MobileAPI.Controllers.Loyalty.Gift
//{
//    [Route("api/GiftGroups")]
//    [ApiController]
//    [ApiConventionType(typeof(DefaultApiConventions))]
//    [Authorize]
//    public class LoyaltyGiftGroupController: ControllerBase
//    {
//        private readonly ILogger _logger;
//        private readonly ILoyaltyGiftService _loyaltyGiftService;
//        private readonly IExceptionReponseService _exceptionReponseService;
//        public LoyaltyGiftGroupController
//        (
//              ILogger<LoyaltyGiftGroupController> logger,
//              ILoyaltyGiftService loyaltyGiftService,
//              IExceptionReponseService exceptionReponseService
//        )
//        {
//            _logger = logger;
//            _loyaltyGiftService = loyaltyGiftService;
//            _exceptionReponseService = exceptionReponseService;
//        }

//        [HttpGet]
//        [Route("GetAllWithImage")]
//        public async Task<ActionResult<LoyaltyGiftGetAllWithImageOutput>> GetAllWithImage([FromQuery] LoyaltyGiftGetAllWithImageInput input)
//        {
//            try
//            {
//                var result = await _loyaltyGiftService.GetAllWithImage(input);
//                return StatusCode(200, result);
//            }
//            catch (Exception ex)
//            {
//                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
//                _logger.LogError(ex, "GetAllWithImage GiftGroups Error - " + JsonConvert.SerializeObject(ex));

//                return StatusCode(400, res);
//            }
//        }

//        [HttpGet]
//        [Route("GetAllWithImageByMemberCode")]
//        public async Task<ActionResult<LoyaltyGiftGetAllWithImageOutput>> GetAllWithImageByMemberCode([FromQuery] LoyaltyGetAllGiftGroupByMemberInput input)
//        {
//            try
//            {
//                var result = await _loyaltyGiftService.GetAllWithImageByMemberCode(input);
//                return StatusCode(200, result);
//            }
//            catch (Exception ex)
//            {
//                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
//                _logger.LogError(ex, "GetAllWithImageByMemberCode GiftGroups Error - " + JsonConvert.SerializeObject(ex));

//                return StatusCode(400, res);
//            }
//        }
//    }
//}
