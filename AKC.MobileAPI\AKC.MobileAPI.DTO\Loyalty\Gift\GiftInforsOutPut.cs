﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
	public class GiftInforsOutPut
	{
		public ListResultGiftInfors Result { get; set; }
		public string TargetUrl { get; set; }
		public bool Success { get; set; }
		public string Error { get; set; }
		public bool UnAuthorizedRequest { get; set; }
		public bool __abp { get; set; }
	}
	public class ListResultGiftInfors
	{
		public int TotalCount { get; set; }

		public List<GetAllGiftInforsIteams> Items { get; set; }
	}
	public class GetAllGiftInforsIteams
    {
        public GetGiftGroupForView GiftGroup { get; set; }
        public List<GiftShortInforForView> Gifts { get; set; }
    }

    public class GetGiftGroupForView
    {
        public GiftGroupDto GiftGroup { get; set; }

        public ImageLinkDto ImageLink { get; set; }
    }
    public class GiftShortInforForView
    {
        public GiftShortInforDto GiftInfor { get; set; }

        public List<ImageLinkDto> ImageLink { get; set; }

        public GiftShortInforForView()
        {
            ImageLink = new List<ImageLinkDto>();
        }

    }

	public class GiftShortInforDto
	{
		public string Code { get; set; }

		public string Name { get; set; }

		public string Description { get; set; }

		public string Introduce { get; set; }

		public string FullGiftCategoryCode { get; set; }

		public string BrandName { get; set; }
		public string ThirdPartyBrandName { get; set; }

		public string Vendor { get; set; }

		public DateTime EffectiveFrom { get; set; }

		public DateTime EffectiveTo { get; set; }

		public decimal RequiredCoin { get; set; }

		public string Status { get; set; }

		public decimal TotalQuantity { get; set; }

		public decimal UsedQuantity { get; set; }

		public decimal RemainingQuantity { get; set; }

		public decimal FullPrice { get; set; }
		public decimal DiscountPrice { get; set; }

		public bool IsEGift { get; set; }

		//public SettingParamDto TargetAudience { get; set; }
		//public int? TargetAudienceId { get; set; }

		//public DateTime? LastModificationTime { get; set; }
		//public DateTime CreationTime { get; set; }
		//public string CreatedByUser { get; set; }
		//public string UpdatedByUser { get; set; }

		public string Tag { get; set; }

		public bool IsInWishlist { get; set; }

		public int Id { get; set; }

		public string RegionCode { get; set; }

		public string Office { get; set; }
		public string ExpireDuration { get; set; }

		public int TotalWish { get; set; }
	}
}
